This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `pages/index.js`. The page auto-updates as you edit the file.

[API routes](https://nextjs.org/docs/api-routes/introduction) can be accessed on [http://localhost:3000/api/hello](http://localhost:3000/api/hello). This endpoint can be edited in `pages/api/hello.js`.

The `pages/api` directory is mapped to `/api/*`. Files in this directory are treated as [API routes](https://nextjs.org/docs/api-routes/introduction) instead of React pages.

## Deployment on Ubuntu

SSH Login `ssh -i bannerbot-ec2.pem ubuntu@***********`

https://www.digitalocean.com/community/tutorials/how-to-secure-nginx-with-let-s-encrypt-on-ubuntu-20-04

https://www.digitalocean.com/community/tutorials/how-to-add-swap-space-on-ubuntu-20-04

### Commands

1. `sudo nginx -t`
2. `sudo service nginx restart`
3. `sudo vi /etc/nginx/sites-enabled/default`
4. `sudo certbot --nginx -d groweasy.ai -d www.groweasy.ai`
5. `docker build -t groweasy-fe .`
6. `docker run -t -i -p 4000:4000 groweasy-fe`
7. `aws ecr get-login-password --region ap-south-1 --profile bannerbot | docker login --username AWS --password-stdin 926249367436.dkr.ecr.ap-south-1.amazonaws.com`
8. `docker tag groweasy-fe:latest 926249367436.dkr.ecr.ap-south-1.amazonaws.com/groweasy-fe:latest`
9. `docker push 926249367436.dkr.ecr.ap-south-1.amazonaws.com/groweasy-fe:latest`
10. `aws ecs register-task-definition --profile bannerbot --cli-input-json file:///Users/<USER>/Desktop/Github/groweasy-fe/ecs/fargate-task.json`
11. `aws ecs --profile bannerbot --region ap-south-1 create-service --cli-input-json file:///Users/<USER>/Desktop/Github/groweasy-fe/ecs/fargate-service.json`

### Initail Setup

1. https://amanhimself.dev/blog/setup-nextjs-project-with-eslint-prettier-husky-lint-staged/
2. npx mrm@2 lint-staged
3. Setup tailwind: https://tailwindcss.com/docs/guides/nextjs
4. Add typescript: https://nextjs.org/docs/pages/building-your-application/configuring/typescript

### SEO Resources

1. Test schema: https://developers.google.com/search/docs/appearance/structured-data
2. Structured data markup helper: https://www.google.com/webmasters/markup-helper/u/0/
3. FAQs Schema: https://schema.org/FAQPage
4. Schema Markup tutorial: https://www.semrush.com/blog/what-is-schema-beginner-s-guide-to-structured-data/

### Meta Ad Resources

1. https://www.semrush.com/blog/facebook-audience-overlap/

### Other Resources

1. Setting up Meta Pixel: https://github.com/vercel/next.js/blob/canary/examples/with-facebook-pixel/README.md

### Figma Links

1. Ritu's Feedback- https://www.figma.com/design/FIu1f38MbNt237h63xnydy/Ritu---Groweasy?node-id=0-1&t=SHYwLW9Ce7G5XReb-0
2. Aman's Feedback- https://www.figma.com/design/vjDRr4GzWRcIH6MxVjlvzN/GrowEasy?node-id=0-1
3. GrowEasy Figma- https://www.figma.com/design/2bdcCwv0Vtsh8Z6aMonbDR/Mobile-App-Design?node-id=64-4&t=QSToYmBrWvJDAh4A-0
4. GrowEasy Logo: https://www.figma.com/design/buGV3z8yyRAuGkqqgapZLg/feature-image-and-logo?node-id=21-29&t=HuBJS3T6APSoxha7-0
5. Design Templates: https://www.figma.com/design/T8IgwpPQiKSLxAnMvowH2K/Design-Templates?t=vIlO8u4UZSBHkcd1-0
6. Current Design Polishing: https://www.figma.com/design/TNmp72PqjzN4MjkjyGz121/Mobile-revamp?node-id=0-1&t=Wi2sWBrvrwfA5aOO-0

### Local HTTPS Setup

Docs: https://sdust.dev/posts/2023-08-18_Next-Localhost-Https.html

1. `yarn add -D local-ssl-proxy`
2. `brew install mkcert nss`
3. `mkcert --install`
4. `mkdir ssl && cd ssl && mkcert dev.groweasy.ai`
5. `sudo vi /etc/hosts`
6. Add entry `127.0.0.1 dev.groweasy.ai`
