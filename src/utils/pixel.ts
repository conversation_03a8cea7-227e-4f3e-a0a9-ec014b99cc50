export const getPixelPlatformsData = (params: {
  website: string;
  pixelId: string;
  customEventName: string;
}) => {
  const { website, pixelId, customEventName } = params;
  return {
    shopify: {
      name: 'Shopify',
      description:
        'Copy the below snippet and add it as a custom pixel in your Shopify store.',
      guideLink:
        'https://help.shopify.com/en/manual/promoting-marketing/pixels/custom-pixels/manage#add-custom-pixel',
      sections: [
        {
          title: 'Checkout Page Pixel Code:',
          code: `!function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');

  fbq('init', '${pixelId}');

analytics.subscribe("product_viewed", (event) => {
    fbq('track', 'ViewContent', {
      content_ids:  [event.data?.productVariant?.id],
      content_name: event.data?.productVariant?.title,
      currency: event.data?.productVariant?.price.currencyCode,
      value: event.data?.productVariant?.price.amount,
      website: '${website}'
    });
  });

  analytics.subscribe("product_added_to_cart", (event) => {
    fbq('track', 'AddToCart', {
      content_ids: [event.data?.cartLine?.merchandise?.productVariant?.id],
      content_name: event.data?.cartLine?.merchandise?.productVariant?.title,
      currency: event.data?.cartLine?.merchandise?.productVariant?.price?.currencyCode,
      value: event.data?.cartLine?.merchandise?.productVariant?.price.amount,
      website: '${website}'
    });
  });

  analytics.subscribe("checkout_started", (event) => {
    fbq('track', 'InitiateCheckout', {
      website: '${website}'
    });
  });

  analytics.subscribe("checkout_completed", (event) => {
    fbq('track', 'Purchase', {
      currency: event.data?.checkout?.currencyCode,
      value: event.data?.checkout?.totalPrice?.amount,
      website: '${website}'
    });

    fbq('track', '${customEventName}', {
      currency: event.data?.checkout?.currencyCode,
      value: event.data?.checkout?.totalPrice?.amount,
    });
  });`,
        },
      ],
    },
    razorpay: {
      name: 'Razorpay',
      description: 'Use this guide to get started',
      guideLink:
        'https://razorpay.com/docs/payments/payment-pages/plugins-add-ons/fb-pixel',
      sections: [
        {
          title: 'Add the below Facebook Pixel Id to your Payment page',
          code: `${pixelId}`,
        },
        {
          title: 'Select the below metrics to track',
          code: `Page Views, 
Initiate Payment, 
Add to Cart, 
Payment Complete`,
        },
      ],
    },
    typeform: {
      name: 'Typeform',
      description: 'Use this guide to get started',
      guideLink:
        'https://help.typeform.com/hc/en-us/articles/************-Facebook-pixel-integration-Installation-and-setup',
      sections: [
        {
          title: 'Connect the below Facebook Pixel Id to your form',
          code: `${pixelId}`,
        },
      ],
    },
    tally: {
      name: 'Tally',
      description: 'Use this guide to get started',
      guideLink: 'https://tally.so/help/facebook-pixel-integration',
      sections: [
        {
          title: 'Connect the below Facebook Pixel Id to your form',
          code: `${pixelId}`,
        },
      ],
    },
    'custom html/js': {
      name: 'Custom HTML/JS',
      description: 'Use beow guide to learn more',
      guideLink: 'https://developers.facebook.com/docs/meta-pixel/get-started',
      sections: [
        {
          title:
            'Add this code inside the <head> or just before the closing </body> tag of your website HTML',
          code: `<!-- Meta Pixel Code -->
<script>
!function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
  
  fbq('init', '${pixelId}');
  fbq('track', 'PageView');
</script>
<!-- End Meta Pixel Code -->    
`,
        },
        {
          title:
            'When a customer completes a purchase on your site, call the function below',
          code: `function fireEvent() {
  if (typeof fbq !== 'undefined') {
    fbq('track', 'Purchase');
  }
}          
`,
        },
      ],
    },
  };
};
