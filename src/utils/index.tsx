import FetchError from 'src/actions/FetchError';
import { EVENT_NAMES } from 'src/constants/events';
import { META_TAGS } from 'src/constants/seo';
import { logEvent } from 'src/modules/firebase';
import { showToastMessage } from 'src/modules/toast';
import {
  GrowEasyPartners,
  IAdCreditsBalance,
  IGroweasyUser,
  IPartnerConfig,
} from 'src/types';
import {
  Currency,
  GROWEASY_CAMPAIGN_TYPE,
  IBudgetAndScheduling,
  ICampaignInsightDetails,
  ICurrencyBudget,
  IGoogleCampaignInsights,
  ILeadgenFormQuestion,
} from 'src/types/campaigns';
import {
  ICtwaLead,
  IGoogleLead,
  IMetaLead,
  IParsedLead,
} from 'src/types/leads';
import { IPexelsVideoData } from 'src/types/remotion';

export const IS_PROD = process.env.NODE_ENV === 'production';

export const getLocalStorage = (key: string) => {
  return window?.localStorage.getItem(key);
};

export const setLocalStorage = (key: string, value: string) => {
  return window?.localStorage.setItem(key, value);
};

export const getFormattedDateString = (
  date: Date,
  formatOptions?: Intl.DateTimeFormatOptions,
) => {
  const options: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  };
  const formattedDate = new Date(date).toLocaleDateString(
    'en-IN',
    formatOptions ?? options,
  );
  return formattedDate.replace(/\b(?:am|pm)\b/g, (match) =>
    match.toUpperCase(),
  );
};

export const getFormattedTimeString = (date: Date) => {
  return getFormattedDateString(date, {
    day: 'numeric',
    month: 'short',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  });
};

export const logError = (error: FetchError | Error, source: string) => {
  let message: string = error.message;
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
  if (error instanceof FetchError && error.response?.error?.message) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    message = error.response?.error?.message as string;
  }
  if (IS_PROD) {
    logEvent(EVENT_NAMES.api_failure, {
      message,
      source,
    });
    window?.sentry?.logErrorToSentry(error, source);
  }
};

export const logApiErrorAndShowToastMessage = (
  error: FetchError | Error,
  source: string,
) => {
  let message: string = error.message;
  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
  if (error instanceof FetchError && error.response?.error?.message) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    message = error.response?.error?.message as string;
  }
  if (IS_PROD) {
    logEvent(EVENT_NAMES.api_failure, {
      message,
      source,
    });
    window?.sentry?.logErrorToSentry(error, source);
  }
  showToastMessage(message, 'error');
};

export const getDateDifferenceInDays = (startDate: Date, endDate: Date) => {
  // in ms
  const timeDifference = endDate.getTime() - startDate.getTime();
  // Convert the time difference from milliseconds to days
  const daysDifference = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));

  return daysDifference;
};

export const getGenderDetailsString = (genders?: number[]) => {
  if (!genders || genders.length === 2) {
    return 'Males and Females';
  } else if (genders.includes(1)) {
    return 'Males only';
  } else {
    return 'Females only';
  }
};

export const getParseLead = (
  leadData: IMetaLead | ICtwaLead | IGoogleLead,
  formQuestions: ILeadgenFormQuestion[],
) => {
  const created_time =
    typeof leadData.created_time === 'string'
      ? leadData.created_time
      : new Date(leadData.created_time * 1000).toISOString();
  const parsedLead: IParsedLead = {
    created_time,
    field_data: [],
    id: leadData.id,
  };
  if ('wa_id' in leadData) {
    parsedLead.navlink = `/ctwa-leads/${leadData.wa_id}_${leadData.meta_id}`;
  }
  leadData.field_data.forEach((data) => {
    const matchingFormQuestion = formQuestions.find(
      (question) => question.key === data.name,
    );
    if (matchingFormQuestion?.options?.length) {
      // extract value from MCQs
      let value = data.values?.[0];
      // todo investigate: item.values sometimes contain value in lowercase, and some time key
      value = matchingFormQuestion?.options.find(
        (optionItem) =>
          optionItem.key === value ||
          optionItem.value?.toLowerCase() === value?.toLowerCase(),
      )?.value;
      data.values = [value];
    }
    parsedLead.field_data.push({
      ...data,
      ...matchingFormQuestion,
    });
  });
  return parsedLead;
};

export const getLeadsDataAfterMappingQuestions = (
  leads: IMetaLead[] | ICtwaLead[] | IGoogleLead[],
  formQuestions: ILeadgenFormQuestion[],
): IParsedLead[] => {
  const parsedLeads: IParsedLead[] = [];
  // guaranteed that formQuestions & leads.field_data have same length
  // unless there is data mismatch due to bug
  leads.forEach((metaLead: IMetaLead | ICtwaLead | IGoogleLead) => {
    parsedLeads.push(getParseLead(metaLead, formQuestions));
  });
  return parsedLeads;
};

export const getCsvFromArray = (items: Record<string, unknown>[]): string => {
  const replacer = (key: string, value: unknown) =>
    value === null ? '' : value; // specify how you want to handle null values here
  const header = Object.keys(items[0]);
  const csv = [
    // remove unwanted commas from heading columns
    header.map((key) => key.replaceAll(',', ';')).join(','), // header row first
    ...items.map((row) =>
      header
        .map((fieldName) => JSON.stringify(row[fieldName], replacer))
        .join(','),
    ),
  ].join('\r\n');
  return csv;
};

export const GROWEASY_ADMINS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const isAdmin = (user?: IGroweasyUser) => {
  if (GROWEASY_ADMINS.includes(user?.email ?? '')) {
    return true;
  }
  return false;
};

export const shareText = (text: string) => {
  const whatsappUrl = `https://wa.me?text=${encodeURIComponent(text)}`;
  if (window?.bridge) {
    window.bridge.postMessage(
      JSON.stringify({
        method: 'share',
        args: text,
      }),
    );
  } else if (window?.navigator?.share) {
    window.navigator
      .share({
        text,
      })
      .then(() => {})
      .catch(() => {});
  } else {
    window?.open(whatsappUrl, '_blank');
  }
};

export const getPartnerConfig = (partner: GrowEasyPartners): IPartnerConfig => {
  switch (partner) {
    case GrowEasyPartners.BANNERBOT: {
      return {
        partner,
        name: 'BannerBot',
        meta: {
          title:
            'Bannerbot - Lead Generation Campaigns on Facebook & Instagram Powered by AI',
          description: '',
          ogImage: 'https://groweasy.ai/images/partners/bannerbot/og-image.jpg',
          favIcon: 'https://groweasy.ai/images/partners/bannerbot/favicon.ico',
          noIndex: true,
        },
        logoImage: '/images/partners/bannerbot/logo.png',
        disablePayment: true,
        razorpayKeyId: '',
      };
    }
    case GrowEasyPartners.NIVIDA: {
      return {
        partner,
        name: 'Nivida',
        meta: {
          title:
            'Nivida - Lead Generation Campaigns on Facebook & Instagram Powered by AI',
          description: '',
          ogImage: 'https://groweasy.ai/images/partners/nivida/og-image.jpg',
          favIcon: 'https://groweasy.ai/images/partners/nivida/favicon.ico',
          noIndex: true,
        },
        logoImage: '/images/partners/nivida/logo.png',
        disablePayment: false,
        razorpayKeyId: '',
      };
    }
    case GrowEasyPartners.ZENDOT: {
      return {
        partner,
        name: 'ZenDot',
        meta: {
          title:
            'Zendot - Your growth partner through technology & digital innovation',
          description: '',
          ogImage: 'https://groweasy.ai/images/partners/zendot/logo.png',
          favIcon: 'https://groweasy.ai/images/partners/zendot/favicon.png',
          noIndex: true,
        },
        logoImage: '/images/partners/zendot/logo.png',
        disablePayment: false,
        razorpayKeyId: IS_PROD
          ? 'rzp_live_lotEpsAbFs3Y5r'
          : 'rzp_test_2y0Cs8fYikC11o',
      };
    }
    case GrowEasyPartners.GENIUS_ADS: {
      return {
        partner,
        name: 'Genius Ads',
        meta: {
          title:
            'Genius Ads - Lead Generation Campaigns on Facebook, Instagram & Google Powered by AI',
          description: '',
          ogImage:
            'https://groweasy.ai/images/partners/genius-ads/og-image.jpeg',
          favIcon: 'https://groweasy.ai/images/partners/genius-ads/favicon.ico',
          noIndex: true,
        },
        logoImage: '/images/partners/genius-ads/logo.jpeg',
        disablePayment: false,
        razorpayKeyId: '',
      };
    }
    case GrowEasyPartners.AD_GLOBAL_AI: {
      return {
        partner,
        name: 'AdGlobalAI',
        meta: {
          title:
            'AdGlobalAI - Lead Generation Campaigns on Facebook, Instagram & Google Powered by AI',
          description: '',
          ogImage:
            'https://groweasy.ai/images/partners/adglobal-ai/og-image.png',
          favIcon:
            'https://groweasy.ai/images/partners/adglobal-ai/favicon.ico',
          noIndex: false,
        },
        logoImage: '/images/partners/adglobal-ai/logo.png',
        disablePayment: false,
        razorpayKeyId: '',
      };
    }
    default:
      return null;
  }
};

export const getParsedCampaignInsights = (params: {
  budgetAndScheduling: IBudgetAndScheduling;
  insights: ICampaignInsightDetails;
  campaignType: GROWEASY_CAMPAIGN_TYPE;
}): Array<{
  label: string;
  value: string | number;
  description?: string;
}> => {
  const { budgetAndScheduling, insights, campaignType } = params;

  // Meta Only, fallback to Leads count from API call
  insights.leads =
    getMetaLeadsCountFromInsightDetails(insights, campaignType) ||
    insights.leads;
  const platformFeePercentage =
    budgetAndScheduling?.platform_fee_percentage ?? 10;
  // same logic as FE
  // customer's budget = 1000, actual budget flowing to META = (1000 - 1000 * 10%)
  // now ad spent = 900, we should convert it back to 1000
  let spendInInr = parseFloat(insights.spend ?? '0');
  spendInInr = spendInInr * (100 / (100 - platformFeePercentage));

  let spendInCampaignCurrency = 0;
  let campaignBudgetInInr = 0;
  const currency = budgetAndScheduling?.currency ?? Currency.INR;
  let lifetimeBudget = 0;

  switch (currency) {
    case Currency.INR: {
      spendInCampaignCurrency = spendInInr;
      lifetimeBudget = budgetAndScheduling?.lifetime_budget ?? 0;
      campaignBudgetInInr = lifetimeBudget / 100; // Paise to Rupees
      break;
    }

    case Currency.USD: {
      const exchangeRate = budgetAndScheduling?.usd?.exchange_rate ?? 84;
      spendInCampaignCurrency = spendInInr / exchangeRate;
      lifetimeBudget = budgetAndScheduling?.usd?.lifetime_budget ?? 0;
      campaignBudgetInInr = (lifetimeBudget * exchangeRate) / 100; // Cents to USD to INR

      break;
    }

    case Currency.IDR: {
      const exchangeRate = budgetAndScheduling?.idr?.exchange_rate ?? 190;
      spendInCampaignCurrency = spendInInr / exchangeRate;
      lifetimeBudget = budgetAndScheduling?.idr?.lifetime_budget ?? 0;
      campaignBudgetInInr = lifetimeBudget * exchangeRate;
      break;
    }

    case Currency.PHP: {
      const exchangeRate = budgetAndScheduling?.php?.exchange_rate ?? 0.7;
      spendInCampaignCurrency = spendInInr / exchangeRate;
      lifetimeBudget = budgetAndScheduling?.php?.lifetime_budget ?? 0;
      campaignBudgetInInr = lifetimeBudget * exchangeRate;
      break;
    }

    case Currency.THB: {
      const exchangeRate = budgetAndScheduling?.thb?.exchange_rate ?? 0.43;
      spendInCampaignCurrency = spendInInr / exchangeRate;
      lifetimeBudget = budgetAndScheduling?.thb?.lifetime_budget ?? 0;
      campaignBudgetInInr = lifetimeBudget * exchangeRate;
      break;
    }

    case Currency.VND: {
      const exchangeRate = budgetAndScheduling?.vnd?.exchange_rate ?? 295;
      spendInCampaignCurrency = spendInInr / exchangeRate;
      lifetimeBudget = budgetAndScheduling?.vnd?.lifetime_budget ?? 0;
      campaignBudgetInInr = lifetimeBudget * exchangeRate;
      break;
    }

    case Currency.MYR: {
      const exchangeRate = budgetAndScheduling?.myr?.exchange_rate ?? 0.057;
      spendInCampaignCurrency = spendInInr / exchangeRate;
      lifetimeBudget = budgetAndScheduling?.myr?.lifetime_budget ?? 0;
      campaignBudgetInInr = lifetimeBudget * exchangeRate;
      break;
    }
  }

  const currencyIcon = getCurrencySymbol(currency);
  // Ads manager campaign budget is always in INR since our Ad account is based out of India
  const budgetSpentPercentage = (spendInInr / campaignBudgetInInr) * 100;

  const details: Array<{
    label: string;
    value: string | number;
    description?: string;
  }> = [
    /*{
      label: 'Reach',
      value: data.reach,
      description: 'The number of people who saw your ads at least once. ',
    },*/
    {
      label: 'Views',
      value: insights.impressions ?? 0,
      description: 'The number of times your ads were on screen.',
    },
    {
      label: 'Clicks',
      value: insights.clicks ?? 0,
      description: 'The number of clicks on your ads.',
    },
    /*{
      label: 'CPC',
      value: `₹${parseFloat(data.cpc).toFixed(2)}`,
      description: 'The average cost for each click (all).',
    },*/
    {
      label: 'Cost',
      value: `${currencyIcon}${spendInCampaignCurrency.toFixed(2)}`,
      description:
        'The estimated total amount of money you have spent on your campaign',
    },
  ];
  // Google
  if (
    [
      GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH,
      GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
      GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
    ].includes(campaignType)
  ) {
    details.push(
      ...[
        {
          label: 'Conversions',
          value: insights.conversions ?? 0,
          description: '',
        },
        {
          label: 'CPCo',
          value: `${
            insights.conversions
              ? currencyIcon +
                (spendInCampaignCurrency / insights.conversions).toFixed(2)
              : '-'
          }`,
          description: 'Cost per Conversion.',
        },
      ],
    );
  } else if ([GROWEASY_CAMPAIGN_TYPE.META_SALES].includes(campaignType)) {
    const actualCpcoNode = getCpcoNodeForMetaSales(insights);
    const totalConversions = actualCpcoNode
      ? Math.round(
          parseFloat(insights.spend ?? '0') / parseFloat(actualCpcoNode.value),
        )
      : 0;
    details.push(
      ...[
        {
          label: 'Conversions',
          value: totalConversions ?? 0,
          description: '',
        },
        {
          label: 'CPCo',
          value: `${
            totalConversions
              ? currencyIcon +
                (spendInCampaignCurrency / totalConversions).toFixed(2)
              : '-'
          }`,
          description: 'Cost per Conversion.',
        },
        {
          label: 'CR',
          value: parseInt(insights.clicks)
            ? `${((totalConversions / parseInt(insights.clicks)) * 100).toFixed(
                2,
              )}%`
            : '-',
          description: 'Conversion Rate',
        },
      ],
    );
  } else {
    // handle for meta sales
    details.push(
      ...[
        {
          label: 'Leads',
          value: insights.leads ?? 0,
          description: '',
        },
        {
          label: 'CPL',
          value: `${
            insights.leads
              ? currencyIcon +
                (spendInCampaignCurrency / insights.leads).toFixed(2)
              : '-'
          }`,
          description: 'Cost per Lead.',
        },
      ],
    );
  }
  details.push(
    {
      label: 'Spent',
      value: `${Math.round(budgetSpentPercentage)}%`,
      description: 'Budget utilised so far',
    },
    {
      label: 'Budget',
      value: `${currencyIcon}${
        lifetimeBudget /
        ([Currency.IDR, Currency.VND].includes(currency) ? 1 : 100)
      }`,
      description: 'Total campaign budget',
    },
  );
  return details;
};

export const getGoogleCampaignInsightDetails = (data: {
  insightsArr: IGoogleCampaignInsights[];
}): ICampaignInsightDetails => {
  const { insightsArr } = data;

  let totalClicks = 0;
  let totalCostMicros = 0;
  let totalImpressions = 0;
  let totalConversions = 0;

  insightsArr.forEach((item) => {
    totalClicks += parseInt(item.metrics?.clicks) ?? 0;
    totalCostMicros += parseInt(item.metrics?.costMicros) ?? 0;
    totalImpressions += parseInt(item.metrics?.impressions) ?? 0;
    totalConversions += item.metrics?.conversions ?? 0;
  });

  return {
    clicks: totalClicks.toString(),
    impressions: totalImpressions.toString(),
    spend: Math.round(totalCostMicros / 1000000).toString(),
    reach: '',
    date_start: '',
    date_stop: '',
    account_id: '',
    cpc: '', // todo
    ctr: '', // todo
    leads: 0,
    conversions: totalConversions,
  };
};

export const isValidURL = (url: string) => {
  const pattern = new RegExp(
    '^(https?:\\/\\/)?' + // protocol
      '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
      '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
      '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
      '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
      '(\\#[-a-z\\d_]*)?$',
    'i',
  ); // fragment locator
  return !!pattern.test(url);
};

export const getMetaTags = (path: string) => {
  const metaTags = META_TAGS[path];
  const BASE_URL = 'https://groweasy.ai';
  const canonicalUrl = path === '/' ? BASE_URL : `${BASE_URL}${path}`;

  if (metaTags) {
    return (
      <>
        <title key="title">{metaTags.title}</title>
        <meta
          name="description"
          key="description"
          content={metaTags.description}
        />
        <meta name="keywords" key="keywords" content={metaTags.keywords} />
        {/* <meta name="robots" key="robots" content="index, follow" /> */}
        <meta property="og:title" key="og:title" content={metaTags.title} />
        <meta
          property="og:description"
          key="og:description"
          content={metaTags.description}
        />
        <meta property="og:url" key="og:url" content={canonicalUrl} />
        <meta
          property="og:image"
          key="og:image"
          content={
            metaTags.ogImagePath
              ? `${BASE_URL}${metaTags.ogImagePath}`
              : 'https://groweasy.ai/images/groweasy-og-image.png'
          }
        />
        <link
          rel="shortcut icon"
          type="image/x-icon"
          href="/images/favicon.ico"
        />
        <link rel="apple-touch-icon" href="/images/apple-touch-icon.png" />
        <meta property="og:site_name" key="og:site_name" content="GrowEasy" />
        <link rel="canonical" key="canonical" href={canonicalUrl} />
      </>
    );
  } else {
    return null;
  }
};

export const areUrlsSame = (url1: string, url2: string) => {
  const parsedUrl1 = new URL(url1);
  const parsedUrl2 = new URL(url2);

  return (
    parsedUrl1.protocol === parsedUrl2.protocol &&
    parsedUrl1.hostname === parsedUrl2.hostname &&
    parsedUrl1.pathname === parsedUrl2.pathname &&
    parsedUrl1.search === parsedUrl2.search &&
    parsedUrl1.hash === parsedUrl2.hash
  );
};

export const extractUrlFromCloudflareCdn = (cloudFlareUrl: string) => {
  const regex = /https?:\/\/[^/]+\/.*?(https?:\/\/.*)/;
  const match = cloudFlareUrl.match(regex);

  return match ? match[1] : '';
};

export const getUpdatedCloudflareImageUrl = (
  url: string,
  params: {
    width: number | string;
    height: number | string;
  },
): string => {
  const { width, height } = params;

  // Check if URL contains cdn-cgi/image
  if (!url.includes('cdn-cgi/image/')) {
    return url; // Return as is if it's not a Cloudflare image URL
  }

  // Extract existing parameters
  const regex = /(cdn-cgi\/image\/)([^/]+)(\/https:\/\/)/;
  const match = url.match(regex);

  let newParams = `width=${width},height=${height}`;

  if (match) {
    const existingParams = match[2];

    // Remove existing width and height
    const updatedParams = existingParams
      .split(',')
      .filter(
        (param) => !param.startsWith('width=') && !param.startsWith('height='),
      )
      .join(',');

    // Append new width & height
    newParams = updatedParams ? `${newParams},${updatedParams}` : newParams;

    return url.replace(regex, `$1${newParams}$3`);
  }

  return url; // Fallback if no match
};

export const getUpdatedUnsplashImageUrl = (
  url: string,
  params: {
    width: number;
    height: number;
  },
): string => {
  const { width, height } = params;

  const urlObj = new URL(url);
  urlObj.searchParams.set('crop', 'edges');
  urlObj.searchParams.set('fit', 'crop');
  urlObj.searchParams.set('w', width.toString());
  urlObj.searchParams.set('h', height.toString());

  return urlObj.toString();
};

export const getCloudflareSquareUrl = (
  url: string,
  params: { width: number },
): string => {
  const { width } = params;
  return `https://groweasy.ai/cdn-cgi/image/width=${width},height=${width},fit=pad,q=100/${url}`;
};

export const getUnderscoreSeparatedBaseDomain = (website: string): string => {
  // Remove protocol (http:// or https://) and paths
  let domain = website
    .toLowerCase()
    .replace(/^https?:\/\//, '')
    .replace(/\/.*$/, '');

  // Remove 'www.' if present
  domain = domain.replace(/^www\./, '');

  // Replace dots with underscores
  domain = domain.replace(/\./g, '_');

  return domain;
};

export const copyText = (text: string): void => {
  navigator?.clipboard
    ?.writeText(text)
    .then(() => {
      showToastMessage('Text copied!', 'success');
    })
    .catch((/* error */) => {
      showToastMessage('Failed to copy text', 'error');
    });
};

export const openUrlInNewTab = (url: string): boolean => {
  const absoluteUrl = url.startsWith('http') ? url : `http://${url}`;

  if (window?.bridge) {
    window.bridge.postMessage(
      JSON.stringify({
        method: 'launch_url',
        args: absoluteUrl,
      }),
    );
    return true; // Assume success when using bridge
  } else {
    const newWindow = window.open(absoluteUrl, '_blank');
    return newWindow !== null;
  }
};

export const getMetaLeadsCountFromInsightDetails = (
  insights: ICampaignInsightDetails,
  campaignType: GROWEASY_CAMPAIGN_TYPE,
): number => {
  let leadsCount = 0;
  let actualCplNode:
    | {
        action_type: string;
        value: string;
      }
    | undefined;

  if (campaignType === GROWEASY_CAMPAIGN_TYPE.META_SALES) {
    actualCplNode = getCpcoNodeForMetaSales(insights);
  } else if (campaignType === GROWEASY_CAMPAIGN_TYPE.CTWA) {
    actualCplNode = insights?.cost_per_action_type?.find(
      (item) =>
        item.action_type === 'onsite_conversion.total_messaging_connection',
    );
  } else {
    // For Instant form and other types
    actualCplNode = insights?.cost_per_action_type?.find(
      (item) =>
        item.action_type === 'lead' ||
        item.action_type === 'onsite_conversion.lead_grouped',
    );
  }

  if (actualCplNode) {
    leadsCount = Math.round(
      parseFloat(insights.spend ?? '0') / parseFloat(actualCplNode.value),
    );
  }
  return leadsCount;
};

/**
 * Calculates the maximum possible target width and height for an image while maintaining
 * the given aspect ratio and ensuring dimensions do not exceed the original image size.
 */
export const getImageTransformationParams = ({
  imageWidth,
  imageHeight,
  targetAspectRatio,
}: {
  imageWidth: number;
  imageHeight: number;
  targetAspectRatio: number;
}): { targetWidth: number; targetHeight: number } => {
  let targetWidth = imageWidth;
  let targetHeight = Math.round(targetWidth / targetAspectRatio);

  if (targetHeight > imageHeight) {
    targetHeight = imageHeight;
    targetWidth = Math.round(targetHeight * targetAspectRatio);
  }

  return { targetWidth, targetHeight };
};

export const getPartnerFromHost = (host: string) => {
  let partner: GrowEasyPartners | undefined = undefined;
  if (host === 'leads.bannerbot.xyz') {
    partner = GrowEasyPartners.BANNERBOT;
  } else if (host === 'ai.nividasoftware.com') {
    partner = GrowEasyPartners.NIVIDA;
  } else if (host === 'ai.zendot.in') {
    partner = GrowEasyPartners.ZENDOT;
  } else if (host === 'ads.teamgeniusmarketing.com') {
    partner = GrowEasyPartners.GENIUS_ADS;
  } else if (['adglobalai.com', 'www.adglobalai.com'].includes(host)) {
    partner = GrowEasyPartners.AD_GLOBAL_AI;
  }
  return partner;
};

export const getProgressWidthClass = (progress: number): string => {
  const roundedProgress = Math.round(progress / 5) * 5;
  switch (roundedProgress) {
    case 0:
      return 'w-0';
    case 5:
      return 'w-[5%]';
    case 10:
      return 'w-[10%]';
    case 15:
      return 'w-[15%]';
    case 20:
      return 'w-1/5';
    case 25:
      return 'w-1/4';
    case 30:
      return 'w-[30%]';
    case 35:
      return 'w-[35%]';
    case 40:
      return 'w-2/5';
    case 45:
      return 'w-[45%]';
    case 50:
      return 'w-1/2';
    case 55:
      return 'w-[55%]';
    case 60:
      return 'w-3/5';
    case 65:
      return 'w-[65%]';
    case 70:
      return 'w-[70%]';
    case 75:
      return 'w-3/4';
    case 80:
      return 'w-4/5';
    case 85:
      return 'w-[85%]';
    case 90:
      return 'w-[90%]';
    case 95:
      return 'w-[95%]';
    case 100:
      return 'w-full';
    default:
      return `w-[${roundedProgress}%]`;
  }
};

export const getCurrencySymbol = (currency: Currency): string => {
  switch (currency) {
    case Currency.USD:
      return '$';
    case Currency.INR:
      return '₹';
    case Currency.IDR:
      return 'Rp';
    case Currency.PHP:
      return '₱';
    case Currency.THB:
      return '฿';
    case Currency.VND:
      return '₫';
    case Currency.MYR:
      return 'RM';
    default:
      // Type-safe fallback (will never hit due to enum exhaustiveness)
      return '';
  }
};

const CURRENCY_TO_LOCALE_MAP: Record<Currency, string> = {
  [Currency.INR]: 'en-IN', // English (India)
  [Currency.USD]: 'en-US', // English (United States)
  [Currency.IDR]: 'id-ID', // Indonesian (Indonesia)
  [Currency.PHP]: 'en-PH', // English (Philippines)
  [Currency.THB]: 'th-TH', // Thai (Thailand)
  [Currency.VND]: 'vi-VN', // Vietnamese (Vietnam)
  [Currency.MYR]: 'ms-MY', // Malay (Malaysia)
};

export const formatCurrencyAmount = (
  amount: number,
  currency: Currency,
  locale?: string,
): string => {
  const isZeroDecimal = [Currency.IDR, Currency.VND].includes(currency);

  // fallback to en-IN if somehow currency is not in the map
  const determinedLocale =
    locale ?? CURRENCY_TO_LOCALE_MAP[currency] ?? 'en-IN';

  return new Intl.NumberFormat(determinedLocale, {
    style: 'currency',
    currency,
    minimumFractionDigits: isZeroDecimal ? 0 : 2,
    maximumFractionDigits: isZeroDecimal ? 0 : 2,
  }).format(amount);
};

export const getUsersAdCreditBalanceForACurrency = (
  adCreditsbalance: IAdCreditsBalance | null,
  currency: Currency,
): number => {
  if (!adCreditsbalance) {
    return 0;
  }
  const key = currency.toLowerCase() as keyof IAdCreditsBalance;

  // Avoid returning timestamp fields by mistake
  const isValidCurrencyKey = [
    'usd',
    'inr',
    'idr',
    'php',
    'thb',
    'vnd',
    'myr',
  ].includes(key);

  return isValidCurrencyKey ? (adCreditsbalance[key] as number) ?? 0 : 0;
};

export const getCampaignCurrencyBudgetNode = (
  budget?: IBudgetAndScheduling,
): ICurrencyBudget | undefined => {
  const currency = budget?.currency ?? Currency.INR;
  if (currency === Currency.INR) {
    return {
      lifetime_budget: budget?.lifetime_budget ?? 0,
      daily_budget: budget?.daily_budget ?? 0,
      exchange_rate: 1,
    };
  }
  const currencyKey = currency.toLowerCase() as keyof IBudgetAndScheduling;

  const currencyData = budget?.[currencyKey] as ICurrencyBudget | undefined;
  return currencyData;
};

// Meta sales can have multiple conversion events, this function decides it
const getCpcoNodeForMetaSales = (
  insights: ICampaignInsightDetails,
): { action_type: string; value: string } | undefined => {
  const cpcoCandidates: { action_type: string; value: string }[] = [];

  // 1. Custom conversion actions
  const customConversionNodes = insights?.cost_per_action_type?.filter((item) =>
    item.action_type.startsWith('offsite_conversion.custom.'),
  );
  if (customConversionNodes) cpcoCandidates.push(...customConversionNodes);

  // 2. Standard purchase
  const purchaseNode = insights?.cost_per_action_type?.find(
    (item) => item.action_type === 'purchase',
  );
  if (purchaseNode) cpcoCandidates.push(purchaseNode);

  // 3. Fallback to generic pixel custom
  const pixelCustomNode = insights?.cost_per_action_type?.find(
    (item) => item.action_type === 'offsite_conversion.fb_pixel_custom',
  );
  if (pixelCustomNode) cpcoCandidates.push(pixelCustomNode);

  // 4. Return the node with the lowest value (converted to number)
  const actualCplNode = cpcoCandidates.reduce((min, curr) => {
    const minVal = parseFloat(min.value);
    const currVal = parseFloat(curr.value);
    return currVal < minVal ? curr : min;
  }, cpcoCandidates[0]);

  return actualCplNode;
};

export const getHdVideoUrlsFromPexelsData = (
  videos: IPexelsVideoData[],
): Array<{
  url: string;
  duration: number;
}> => {
  const hdVideoUrls: Array<{ url: string; duration: number }> = [];

  for (const video of videos) {
    const sortedVideoFiles = video.video_files
      .filter((videoFile) => videoFile.quality === 'hd')
      .sort((a, b) => a.width - b.width); // sorting on the basis of width
    const hdVideoUrl = sortedVideoFiles?.[0]?.link;
    if (hdVideoUrl) {
      hdVideoUrls.push({ url: hdVideoUrl, duration: video.duration });
    }
  }

  if (hdVideoUrls.length) return hdVideoUrls;

  // if for some reason the hdVideoUrls doesn't contain value going with the width 720 or above
  for (const video of videos) {
    const filteredVideoFiles = video.video_files
      .filter((item) => item.width >= 720)
      .sort((a, b) => a.width - b.width);
    const hdVideoUrl = filteredVideoFiles?.[0]?.link;
    if (hdVideoUrl) {
      hdVideoUrls.push({ url: hdVideoUrl, duration: video.duration });
    }
  }

  if (hdVideoUrls.length) return hdVideoUrls;

  // final fallback
  for (const video of videos) {
    // taking the first link can be of any size
    const filteredVideoFiles = video.video_files;
    const hdVideoUrl = filteredVideoFiles?.[0]?.link;
    if (hdVideoUrl) {
      hdVideoUrls.push({ url: hdVideoUrl, duration: video.duration });
    }
  }

  return hdVideoUrls;
};
