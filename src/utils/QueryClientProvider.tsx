import React from 'react';
import { QueryClient, QueryClientProvider as Provider } from 'react-query';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
    },
  },
});

interface IQueryClientProvider {
  children: React.ReactNode;
}

const QueryClientProvider = (props: IQueryClientProvider) => {
  const { children } = props;
  return <Provider client={queryClient}>{children}</Provider>;
};

export default QueryClientProvider;
