import { QueryParams } from '@/constants/index';
import { useRouter } from 'next/router';
import React from 'react';
import { ICampaign } from 'src/types/campaigns';

const AiMediaButtonComp = ({
  campaignDetails,
}: {
  campaignDetails: ICampaign;
}) => {
  const router = useRouter();
  return (
    <button
      type="button"
      onClick={() => {
        void router.push({
          pathname: '/admin/ai-media',
          query: {
            [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
          },
        });
      }}
      className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-3 py-1.5 rounded-lg text-xs font-medium hover:from-purple-600 hover:to-blue-600 transition-all duration-200 flex items-center space-x-1 active:scale-95"
      title="Generate AI Media"
    >
      <svg
        className="w-4 h-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M13 10V3L4 14h7v7l9-11h-7z"
        />
      </svg>
      <span className="hidden sm:inline truncate">AI Media</span>
    </button>
  );
};

export default AiMediaButtonComp;
