import { useState } from 'react';
import { ICampaign } from 'src/types/campaigns';
import CrossIcon from '@/images/common/cross.svg';
import { useMutation } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import { IGroweasyUser, IUserProfile } from 'src/types';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import BottomSheet from '../lib/BottomSheet';
import {
  getAdminUserProfileDetails,
  updateAdminUserCampaign,
} from 'src/actions/admin';
import Button from '../lib/Button';
import { QueryParams } from '@/constants/index';
import { showToastMessage } from 'src/modules/toast';

interface IAssignCampaignBsProps {
  campaign: Partial<ICampaign>;
  onClose: () => void;
  user: IGroweasyUser;
  onRefresh: () => void;
}

const AssignCampaignBs = (props: IAssignCampaignBsProps) => {
  const { campaign, onClose, user, onRefresh } = props;

  const [searchEmail, setSearchEmail] = useState('');
  const [userProfile, setUserProfile] = useState<IUserProfile | null>(null);

  const updateAdminUserCampaignMutation = useMutation(updateAdminUserCampaign);
  const userProfileResponseMutation = useMutation(getAdminUserProfileDetails);

  const onSearchUsersClick = () => {
    userProfileResponseMutation
      .mutateAsync({
        queryParams: {
          email: searchEmail.toLowerCase(),
          //uid: query
        },
        headers: getCommonHeaders(user),
      })
      .then((response) => {
        if (response?.data) {
          setUserProfile(response.data);
        } else {
          showToastMessage('No user found', 'error');
        }
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'AssignCampaignBs.onSearchUsersClick',
        );
      });
  };

  const onConfirmCtaClick = () => {
    if (!userProfile) {
      return;
    }
    updateAdminUserCampaignMutation
      .mutateAsync({
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaign.id,
        },
        headers: getCommonHeaders(user),
        data: {
          uid: userProfile.uid,
          name: `${userProfile?.email}_${campaign?.details?.business_details
            ?.business_category}_${Date.now()}`,
        },
      })
      .then(() => {
        showToastMessage(
          `Campaign Assigned to ${userProfile?.name}`,
          'success',
        );
        onClose();
        onRefresh();
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'AssignCampaignBs.onConfirmCtaClick',
        );
      });
  };

  return (
    <BottomSheet
      onClose={onClose}
      className="h-4/5"
      loading={
        userProfileResponseMutation.isLoading ||
        updateAdminUserCampaignMutation.isLoading
      }
      contentClassName="h-full flex flex-col flex-1"
    >
      <div className="flex flex-col flex-1 overflow-hidden gap-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 flex-1">
            Assign Campaign
          </h2>
          <CrossIcon
            width="16"
            height="16"
            className="text-gray-500 hover:text-gray-700 cursor-pointer transition-colors"
            onClick={onClose}
          />
        </div>

        {/* Content */}
        <div className="flex-1 flex-col overflow-y-auto space-y-4">
          <div>
            <h3 className="text-base font-medium text-gray-900">
              {campaign.name}
            </h3>
            <p className="mt-1 text-sm text-gray-600">
              {
                campaign.details?.business_details
                  ?.product_or_service_description
              }
            </p>
          </div>

          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              Search Users
            </label>
            <div className="flex gap-2">
              <input
                className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm outline-none"
                type="text"
                onChange={(event) => setSearchEmail(event.target?.value)}
                placeholder="Type email to assign this campaign"
              />
              <Button
                className="!py-2 !px-4 !text-sm !rounded-md"
                disabled={!searchEmail}
                onClick={onSearchUsersClick}
              >
                Search
              </Button>
            </div>
          </div>

          {userProfile && (
            <div className="mt-6 p-4 bg-gray-50 rounded-md">
              <p className="text-sm font-medium text-gray-700 mb-2">
                Assign this campaign to:
              </p>
              <p className="text-sm font-semibold text-gray-900">
                {userProfile?.name}
              </p>
              <p className="text-sm text-gray-600">{userProfile?.email}</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <Button
          className="mt-4 w-full py-2 text-sm font-medium"
          onClick={onConfirmCtaClick}
          disabled={updateAdminUserCampaignMutation.isLoading || !userProfile}
        >
          Confirm
        </Button>
      </div>
    </BottomSheet>
  );
};

export default AssignCampaignBs;
