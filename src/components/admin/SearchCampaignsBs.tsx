import { useState } from 'react';
import { useRouter } from 'next/router';
import CrossIcon from '@/images/common/cross.svg';
import BottomSheet from '../lib/BottomSheet';
import Button from '../lib/Button';
import { showToastMessage } from 'src/modules/toast';

interface ISearchCampaignsBsProps {
  onClose: () => void;
}

const SearchCampaignsBs = (props: ISearchCampaignsBsProps) => {
  const { onClose } = props;
  const router = useRouter();

  const [searchEmail, setSearchEmail] = useState('');

  const onSearchClick = () => {
    const email = searchEmail.trim();
    if (!email) {
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      showToastMessage('Please enter a valid email address', 'error');
      return;
    }

    void router.push({
      pathname: '/admin/dashboard',
      query: {
        email: email.toLowerCase(),
      },
    });

    onClose();
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchEmail(e.target.value);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearchClick();
    }
  };

  return (
    <BottomSheet
      onClose={onClose}
      className="h-auto"
      contentClassName="h-full flex flex-col flex-1"
    >
      <div className="flex flex-col flex-1 overflow-hidden gap-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 flex-1">
            Search Campaigns
          </h2>
          <CrossIcon
            width="16"
            height="16"
            className="text-gray-500 hover:text-gray-700 cursor-pointer transition-colors"
            onClick={onClose}
          />
        </div>

        {/* Content */}
        <div className="flex flex-1 flex-col space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              User Email
            </label>
            <input
              type="email"
              value={searchEmail}
              onChange={handleEmailChange}
              onKeyPress={handleKeyPress}
              placeholder="Enter user email (e.g., <EMAIL>)"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
              autoFocus
            />
          </div>

          <p className="text-sm text-gray-600">
            Enter the email address of the user whose campaigns you want to
            view.
          </p>
        </div>

        {/* Footer */}
        <Button
          className="mt-4 w-full py-2 text-sm font-medium"
          onClick={onSearchClick}
          disabled={!searchEmail.trim()}
        >
          SEARCH
        </Button>
      </div>
    </BottomSheet>
  );
};

export default SearchCampaignsBs;
