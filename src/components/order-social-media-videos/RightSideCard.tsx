import Heading from '@/components/lib/typography/Heading';
import NextButton from './NextButton';

function RightSideCard({
  icon,
  heading,
  children,
  handleNext,
  NextAreaContent = null,
  nextButtonLoading = false,
  loadingText,
  showMessage = false,
  disabled = false,
  className,
  buttonText,
}: {
  icon: React.ReactNode;
  heading: string;
  children: React.ReactNode;
  NextAreaContent?: React.ReactNode;
  handleNext: () => void;
  nextButtonLoading?: boolean;
  loadingText?: string;
  showMessage?: boolean;
  disabled?: boolean;
  className?: string;
  buttonText?: string;
}) {
  return (
    <div className={` flex-1 flex flex-col overflow-auto ${className}`}>
      <div className="  flex-1 overflow-auto new-scroll-bar py-4">
        <div className=" relative w-16 h-16 pointer-events-none text-[#286053] ">
          {icon}
        </div>
        <Heading level={3} className=" text-[#286053] mt-4 ">
          {heading}
        </Heading>
        <div className=" mt-8 ">{children}</div>
      </div>
      <div className=" my-4  ">
        {NextAreaContent ? (
          NextAreaContent
        ) : (
          <NextButton
            buttonContent={buttonText}
            handleNext={handleNext}
            loading={nextButtonLoading}
            loadingText={loadingText}
            showMessage={showMessage}
            disabled={disabled}
          />
        )}
      </div>
    </div>
  );
}

export default RightSideCard;
