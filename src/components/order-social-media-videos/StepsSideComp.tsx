import Body from '@/components/lib/typography/Body';
import CheckIcon from '@/images/common/tick.svg';
import { steps } from './constants';
import { showToastMessage } from 'src/modules/toast';

const StepsSideComp = ({
  currStepId,
  setCurrStepId,
  maxStepId,
}: {
  currStepId: number;
  maxStepId: number;
  setCurrStepId: React.Dispatch<React.SetStateAction<number>>;
}) => {
  return (
    <div className=" mt-14 hidden lg:flex flex-col xl:pl-10  pr-4 flex-shrink-0 ">
      {steps.map((step, index) => {
        const isCompleted = index < maxStepId;
        return (
          <div key={index}>
            <ShowSteps
              currId={index}
              maxStepId={maxStepId}
              currStepId={currStepId}
              setCurrStepId={setCurrStepId}
              isCompleted={isCompleted}
              isActive={index === currStepId}
            />
            {index !== steps.length - 1 && (
              <div
                className={` h-10 border-l-2 relative left-7 ${
                  isCompleted
                    ? 'border-[#286053] '
                    : 'border-[#979797] border-dashed '
                } `}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

const ShowSteps = ({
  currId,
  isCompleted = false,
  isActive = false,
  maxStepId,
  currStepId,
  setCurrStepId,
}: {
  currId: number;
  isCompleted: boolean;
  maxStepId: number;
  isActive?: boolean;
  currStepId: number;
  setCurrStepId: React.Dispatch<React.SetStateAction<number>>;
}) => {
  const data = steps[currId];

  function handleClick() {
    // debugger;
    if (currId <= maxStepId && currId !== currStepId) {
      setCurrStepId(currId);
    } else {
      showToastMessage('Please fill in all the details to Unlock.', 'error');
    }
  }

  return (
    <div className=" flex items-center gap-6 ">
      <div
        className={` w-14 h-14 rounded-full font-medium grid place-items-center text-xl flex-shrink-0 ${
          isCompleted || isActive
            ? 'bg-[#286053] text-white '
            : 'shadow-[inset_0_0_0_1px] shadow-[#979797] text-[#979797] '
        } `}
      >
        {isCompleted ? (
          <div className=" w-4">
            <CheckIcon />
          </div>
        ) : (
          currId + 1
        )}
      </div>
      <div>
        <Body
          className={`${
            isActive || isCompleted ? '!text-black' : '!text-[#979797]'
          } font-semibold whitespace-nowrap cursor-pointer `}
          onClick={handleClick}
        >
          {data.title}
        </Body>
        <Body
          className={`${
            isActive || isCompleted ? '!text-[#767676]' : '!text-[#979797]'
          } !text-base xl:!text-lg 2xl:text-xl h-[1em] `}
        >
          {data.description}
        </Body>
      </div>
    </div>
  );
};

export default StepsSideComp;
