import { useState } from 'react';
import { UseMutationResult } from 'react-query';
import { OrderData } from 'src/types/video_campaign';
import RightSideCard from './RightSideCard';
import ButtonV2 from '@/components/lib/ButtonV2';
import LanguageIcon from '@/images/icons/language-icon.svg';
import EnglishLanguageIcon from '@/images/icons/english-language-icon.svg';
import HindiLanguageIcon from '@/images/icons/hindi-language-icon.svg';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import { getCommonHeaders } from 'src/actions';
import FetchError from 'src/actions/FetchError';

interface ISelectLanguageProps {
  currStepId: number;
  setCurrStepId: React.Dispatch<React.SetStateAction<number>>;
  data: OrderData;
  setData: React.Dispatch<React.SetStateAction<OrderData>>;
  getVideoScriptsMutation: UseMutationResult<
    {
      data: object;
    },
    unknown,
    {
      headers: Record<string, string>;
      queryParams: Record<string, string>;
      data: {
        prompt: string;
        version: string;
        language: string;
      };
    },
    unknown
  >;
  maxStepId: number;
  setMaxStepId: React.Dispatch<React.SetStateAction<number>>;
}

function SelectLanguage(props: ISelectLanguageProps) {
  const {
    data,
    currStepId,
    setCurrStepId,
    setData,
    getVideoScriptsMutation,
    setMaxStepId,
  } = props;

  const [selectedLanguage, setSelectedLanguage] = useState<string>(
    data.language || 'hi',
  );

  function handleNext() {
    setCurrStepId(1);
    setMaxStepId(1);

    logEvent(EVENT_NAMES.groweasy_videos_language_selected);
    setData((prev) => ({
      ...prev,
      language: selectedLanguage as 'en' | 'hi',
      selectedScript: [],
    }));

    getVideoScriptsMutation
      .mutateAsync({
        headers: getCommonHeaders(),
        queryParams: {},
        data: {
          prompt: data.prompt,
          version: 'v2',
          language: selectedLanguage,
        },
      })
      .then((res) => {
        console.log(res);
        setData((prev) => ({
          ...prev,
          videoScript: res.data as {
            [key: string]: { title: string; script: string };
          },
        }));
        setCurrStepId((prev) => prev + 1);
        setMaxStepId(currStepId + 1);
      })
      .catch((err: FetchError | Error) => {
        logApiErrorAndShowToastMessage(err, 'SelectLanguage.getVideoScripts');
        setCurrStepId(0);
        setMaxStepId(0);
      });

    // if (getVideoScriptsMutation.isLoading) {
    //   setNextButtonLoading(true);
    //   setButtonClicked(true);
    // } else {
    //   setCurrStepId((prev) => prev + 1);
    //   setMaxStepId(currStepId + 1);
    // }
  }

  return (
    <RightSideCard
      icon={<LanguageIcon />}
      heading="Select Language for your Video Ad"
      handleNext={handleNext}
      nextButtonLoading={getVideoScriptsMutation.isLoading}
      loadingText="Please wait while we fetch the scripts"
    >
      <div className=" flex flex-col gap-4 w-[180px] ">
        {[
          {
            Icon: HindiLanguageIcon as React.ElementType,
            language: 'Hindi',
            value: 'hi',
          },
          {
            Icon: EnglishLanguageIcon as React.ElementType,
            language: 'English',
            value: 'en',
          },
        ].map((item) => (
          <ButtonV2
            variant={selectedLanguage === item.value ? 'primary' : 'outline'}
            icon={<item.Icon />}
            onClick={() => setSelectedLanguage(item.value)}
            key={item.language}
            className=" w-full"
          >
            {item.language}
          </ButtonV2>
        ))}
      </div>
    </RightSideCard>
  );
}

export default SelectLanguage;
