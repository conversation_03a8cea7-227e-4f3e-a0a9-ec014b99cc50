import { BiGlobe } from 'react-icons/bi';

const VernacularIllustration = () => {
  return (
    <div className="relative w-full h-full flex items-center justify-center">
      <div className="w-full max-w-[480px] aspect-square md:aspect-video relative group cursor-pointer">
        <div className="absolute inset-0 bg-gray-50 rounded-lg shadow-lg overflow-hidden border border-gray-200 transition-all duration-300 group-hover:shadow-xl group-hover:border-teal-200">
          <div className="h-8 bg-gray-100 border-b border-gray-200 flex items-center px-3">
            <div className="h-3 w-20 bg-gray-200 rounded-full "></div>
          </div>
          <div className="p-3 h-[calc(100%-2rem)] flex flex-col">
            <div className="flex gap-2 mb-3 overflow-x-auto pb-1 scrollbar-hide">
              <div className="h-7 px-3 bg-gray-50 rounded-full flex items-center justify-center whitespace-nowrap text-xs hover:bg-gray-100 transition-colors duration-200">
                English
              </div>
              <div className="h-7 px-3 bg-teal-500 text-white rounded-full flex items-center justify-center whitespace-nowrap text-xs hover:bg-teal-600 transition-colors duration-200">
                हिन्दी
              </div>
              <div className="h-7 px-3 bg-gray-100 rounded-full flex items-center justify-center whitespace-nowrap text-xs hover:bg-gray-200 transition-colors duration-200">
                தமிழ்
              </div>
              <div className="h-7 px-3 bg-gray-100 rounded-full flex items-center justify-center whitespace-nowrap text-xs hover:bg-gray-200 transition-colors duration-200">
                বাংলা
              </div>
              <div className="h-7 px-3 bg-gray-100 rounded-full flex items-center justify-center whitespace-nowrap text-xs hover:bg-gray-200 transition-colors duration-200">
                తెలుగు
              </div>
              <div className="h-7 px-3 bg-gray-100 rounded-full flex items-center justify-center whitespace-nowrap text-xs hover:bg-gray-200 transition-colors duration-200">
                मराठी
              </div>
              <div className="h-7 px-3 bg-gray-100 rounded-full flex items-center justify-center whitespace-nowrap text-xs hover:bg-gray-200 transition-colors duration-200">
                + more
              </div>
            </div>

            <div className="flex-1 bg-white rounded-lg border border-gray-200 overflow-hidden flex group-hover:border-teal-200 transition-all duration-300 group-hover:shadow-sm">
              <div className="w-1/3 bg-gradient-to-br from-teal-400 to-teal-600 flex items-center justify-center ">
                <div className="text-white font-bold text-base">हिन्दी</div>
              </div>

              <div className="w-1/2 p-3 flex flex-col justify-between">
                <div>
                  <div className="h-3 w-5/6 bg-gray-200 rounded-full mb-1.5 "></div>
                  <div className="h-2.5 w-3/4 bg-gray-200 rounded-full mb-3 "></div>

                  <div className="flex gap-1.5 mb-2">
                    <div className="h-6 w-6 rounded-full bg-gray-100 group-hover:bg-gray-200 transition-colors duration-300"></div>
                    <div className="flex-1">
                      <div className="h-2.5 w-1/2 bg-gray-200 rounded-full mb-1 "></div>
                      <div className="h-2 w-1/3 bg-gray-100 rounded-full group-hover:bg-gray-200 transition-colors duration-300"></div>
                    </div>
                  </div>
                </div>

                <div className="h-7 w-full bg-teal-500 rounded flex items-center justify-center mt-auto group-hover:bg-teal-600 transition-colors duration-300">
                  <div className="h-2.5 w-12 bg-white rounded-full"></div>
                </div>
              </div>
            </div>
            <div className="mt-2 flex gap-1.5">
              {['EN', 'हि', 'தமி', 'বা', 'తె', 'म'].map((lang, i) => (
                <div
                  key={i}
                  className="h-5 w-5 rounded-full bg-gray-100 flex items-center justify-center text-[9px] hover:bg-gray-200 transition-colors duration-200"
                >
                  {lang}
                </div>
              ))}
              <div className="h-5 px-1.5 rounded-full bg-teal-100 text-teal-700 flex items-center justify-center text-[9px] ml-auto hover:bg-teal-200 transition-colors duration-200">
                +5 more
              </div>
            </div>
          </div>
        </div>

        <div className="absolute top-8 -right-2 w-8 h-8 rounded-full bg-teal-500 flex items-center justify-center shadow-md group-hover:bg-teal-600 transition-all duration-300 group-hover:scale-110">
          <BiGlobe className="text-white text-lg group-hover:animate-spin" />
        </div>
      </div>
    </div>
  );
};

export default VernacularIllustration;
