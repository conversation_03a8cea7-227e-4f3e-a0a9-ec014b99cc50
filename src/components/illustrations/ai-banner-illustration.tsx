import { IoSparkles } from 'react-icons/io5';
import { RiSparklingFill } from 'react-icons/ri';

const AIBannerIllustration = () => {
  return (
    <div className="w-full max-w-full aspect-square relative flex items-center justify-center overflow-hidden">
      <div className="absolute mt-12 inset-0 flex items-center justify-center">
        {/* Simplified card with text and gradient */}
        <div className="bg-white rounded-lg shadow-sm p-5 h-52 w-full mx-2 relative transition-all duration-300 hover:shadow-lg border group">
          {/* Sparkle icon */}
          <div className="absolute -top-2 -right-2 bg-teal-400 rounded-full p-1.5">
            <RiSparklingFill className="text-white text-xs" />
          </div>

          {/* Header with title */}
          <div className="flex items-center mb-4">
            <div className="h-2 w-2 rounded-full bg-teal-500 mr-2"></div>
            <div className="text-sm font-medium text-gray-600">
              AI Content Generator
            </div>
          </div>

          {/* Simplified content area - text and gradient side by side */}
          <div className="flex gap-4">
            {/* Text generation side */}
            <div className="flex-1 flex flex-col">
              <div className="space-y-2">
                <div className="h-1.5 w-full bg-gray-200 rounded-full"></div>
                <div className="h-1.5 w-4/5 bg-gray-200 rounded-full"></div>
                <div className="h-1.5 w-2/3 bg-gray-200 rounded-full"></div>
                <div className="h-1.5 w-5/6 bg-gray-200 rounded-full"></div>
              </div>
            </div>

            {/* Gradient instead of image */}
            <div className="w-24 h-24 rounded-md overflow-hidden flex-shrink-0 bg-gradient-to-br from-emerald-400 via-teal-400 to-cyan-500 relative">
              {/* Scan line animation */}
              <IoSparkles className="absolute text-white w-16 h-16 animate-pulse opacity-50 -translate-x-1/2 -translate-y-1/2 top-1/2 left-1/2" />
              <div
                className="absolute inset-x-0 h-1 bg-white/30 z-20 top-0"
                style={{ animation: 'scanLine 1.5s linear infinite' }}
              ></div>
            </div>
          </div>

          {/* Simple progress bar */}
          <div className="mt-4">
            <div className="h-1.5 w-full bg-gray-100 rounded-full overflow-hidden">
              <div className="h-full w-2/3 bg-teal-400 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Animation for scan line */}
      <style jsx>{`
        @keyframes scanLine {
          0% {
            top: 0;
          }
          100% {
            top: 100%;
          }
        }
      `}</style>
    </div>
  );
};

export default AIBannerIllustration;
