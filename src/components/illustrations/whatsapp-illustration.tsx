import { FaWhatsapp } from 'react-icons/fa6';

const WhatsappIllustration = () => {
  return (
    <div className="relative w-full h-full flex items-center justify-center">
      <div className="w-full max-w-2xl aspect-[8/5] relative group transition-all">
        {/* Card */}
        <div className="absolute inset-0 bg-gradient-to-br from-white to-teal-50 rounded-xl shadow-lg border border-gray-200 overflow-hidden flex items-center p-6">
          {/* Left: WhatsApp logo */}
          <div className="flex flex-col items-center justify-center mr-6">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-teal-500 to-green-600 flex items-center justify-center shadow-lg mb-2">
              <FaWhatsapp className="text-white" size={40} />
            </div>
            <div className="text-xs text-teal-700 font-semibold tracking-wide">
              WhatsApp
            </div>
          </div>
          {/* Center: Chat bubbles */}
          <div className="flex-1 flex flex-col justify-center gap-3">
            <div className="flex justify-end">
              <div className="bg-teal-100 px-4 py-2 rounded-2xl rounded-tr-sm max-w-[60%] shadow transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg">
                <div className="h-2 w-24 bg-teal-300 rounded-full mb-1"></div>
                <div className="h-2 w-16 bg-teal-200 rounded-full"></div>
              </div>
            </div>
            <div className="flex">
              <div className="bg-white px-4 py-2 rounded-2xl rounded-tl-sm max-w-[60%] shadow transition-all duration-300 group-hover:scale-105 group-hover:shadow-lg">
                <div className="h-2 w-20 bg-gray-300 rounded-full mb-1"></div>
                <div className="h-2 w-28 bg-gray-200 rounded-full"></div>
              </div>
            </div>
            <div className="flex justify-end">
              <div className="bg-teal-100 px-4 py-2 rounded-2xl rounded-tr-sm max-w-[60%] shadow transition-all duration-300 group-hover:scale-110 group-hover:shadow-xl">
                <div className="h-2 w-20 bg-teal-300 rounded-full mb-1"></div>
                <div className="h-2 w-12 bg-teal-200 rounded-full"></div>
              </div>
            </div>
          </div>
          {/* Right: Send icon */}
          <div className="flex flex-col items-center justify-end ml-1">
            <div className="w-12 h-12 rounded-full bg-teal-500 flex items-center justify-center shadow-lg transition-transform duration-300 group-hover:scale-110 group-hover:-rotate-12">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m5 12 14 0"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </div>
            <div className="text-xs text-teal-700 mt-2 font-medium">Send</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhatsappIllustration;
