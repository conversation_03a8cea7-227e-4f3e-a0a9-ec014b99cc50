import {
  BsBriefcase,
  BsPeople,
  BsBar<PERSON>hart,
  BsBell,
  BsSearch,
  BsPlus,
} from 'react-icons/bs';

const CRMIllustration = () => {
  return (
    <div className="relative w-full h-full flex items-center justify-center">
      <div className="w-full max-w-[450px] aspect-square md:aspect-video relative">
        {/* Main container */}
        <div className="absolute inset-0 bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
          {/* Header */}
          <div className="h-12 bg-teal-500 flex items-center px-4">
            <div className="text-white text-sm font-semibold">CRM</div>
            <div className="ml-auto flex gap-3">
              <div className="h-6 w-6 rounded-full bg-white/10 flex items-center justify-center text-white">
                <BsSearch size={12} />
              </div>
              <div className="h-6 w-6 rounded-full bg-white/10 flex items-center justify-center text-white">
                <BsBell size={12} />
              </div>
            </div>
          </div>

          {/* Content area */}
          <div className="flex h-[calc(100%-3rem)]">
            {/* Sidebar - simplified */}
            <div className="w-16 bg-gray-50 border-r border-gray-100 flex flex-col items-center py-4 gap-4">
              <div className="w-10 h-10 rounded-lg bg-teal-500 flex items-center justify-center text-white">
                <BsBriefcase size={16} />
              </div>
              <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center text-gray-400">
                <BsPeople size={16} />
              </div>
              <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center text-gray-400">
                <BsBarChart size={16} />
              </div>
            </div>

            {/* Main content - super simplified */}
            <div className="flex-1 p-4">
              {/* Title bar */}
              <div className="mb-4 flex items-center">
                <div className="h-4 w-24 bg-gray-200 rounded"></div>

                <div className="ml-auto">
                  <div className="h-8 w-8 rounded-full bg-teal-500 flex items-center justify-center text-white">
                    <BsPlus size={20} />
                  </div>
                </div>
              </div>

              {/* Tab bar - simplified to blocks */}
              <div className="flex gap-2 mb-4">
                <div className="h-6 w-16 bg-teal-300 rounded"></div>
                <div className="h-6 w-16 bg-gray-100 rounded"></div>
                <div className="h-6 w-16 bg-orange-300 rounded"></div>
              </div>

              {/* List items - simplified to blocks */}
              <div className="space-y-3">
                {[1, 2].map((i) => (
                  <div
                    key={i}
                    className="p-3 bg-white rounded-lg border border-gray-100 flex items-center"
                  >
                    <div className="w-10 h-10 rounded-full bg-gray-100 flex-shrink-0"></div>
                    <div className="ml-3 flex-1">
                      <div className="h-3 w-24 bg-gray-300 rounded mb-2"></div>
                      <div className="h-2 w-32 bg-gray-100 rounded"></div>
                    </div>
                    <div className="ml-auto h-6 w-16 rounded bg-teal-300/10 border border-blue-100"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CRMIllustration;
