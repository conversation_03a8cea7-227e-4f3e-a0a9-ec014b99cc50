import Link from 'next/link';
import { FaEnvelope, FaPhone } from 'react-icons/fa';
import { FaLocationCrosshairs } from 'react-icons/fa6';

const ContactUsComp = () => {
  return (
    <div className="py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6">
        <div className="text-center mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-3">
            Get in Touch
          </h2>
          <p className="text-gray-600">
            Ready to transform your digital marketing? Let&apos;s discuss how
            GrowEasy can help.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm flex flex-col text-center">
            <div className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-white text-xl">
                <FaEnvelope />
              </span>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Email</h3>
            <a
              href="mailto:<EMAIL>"
              className="text-primary2 hover:underline text-sm my-2"
            >
              <EMAIL>
            </a>
            <a
              href="mailto:<EMAIL>"
              className="text-primary2 hover:underline text-sm"
            >
              <EMAIL>
            </a>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm text-center">
            <div className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-white text-xl">
                <FaPhone />
              </span>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Phone</h3>
            <a
              href="tel:+919998269709"
              className="text-primary2 font-medium hover:underline"
            >
              +91-9998269709
            </a>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm text-center">
            <div className="w-12 h-12 bg-teal-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-white text-xl">
                <FaLocationCrosshairs />
              </span>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Location</h3>
            <p className="text-gray-600 text-sm">
              Noida SEZ, Sec 3<br />
              Gautam Buddha Nagar, UP 201301
            </p>
          </div>
        </div>

        <div className="mt-8 text-center bg-gradient-to-br from-teal-600 to-primary rounded-lg p-6 text-white">
          <h3 className="text-xl font-bold mb-2">Ready to Get Started?</h3>
          <p className="mb-6 opacity-90">
            Join businesses growing with GrowEasy&apos;s AI marketing solutions.
          </p>
          <Link href="/login">
            <button className="bg-white text-primary px-6 py-2 rounded font-semibold hover:bg-gray-100 transition-colors">
              Join fast!
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ContactUsComp;
