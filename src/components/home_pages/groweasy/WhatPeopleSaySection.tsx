import React from 'react';
import QuotationIcon from '@/images/icons/quotation-icon.svg';
import StartIcon from '@/images/icons/star-v2-icon.svg';
import BodyV2 from '@/components/lib//typography/BodyV2';
import Image from 'next/image';
import Heading from '@/components/lib//typography/Heading';

const data = [
  {
    imageUrl: '/images/testimonials-people/akshat.jpg',
    body: 'Using GrowEasy, we saw an immediate increase in ROAS across our Google and Facebook campaigns. It automated the entire process and delivered better results with less manual intervention.',
    userName: 'Akshat',
    userPosition: 'Founder, Pagaar.ai',
    startCount: 5,
  },
  {
    imageUrl: '/images/testimonials-people/dhruvin.jpg',
    body: 'GrowEasy transformed the way we manage digital marketing. It took the guesswork out of campaign optimization and allowed us to focus on scaling. Within days, our leads skyrocketed!',
    userName: 'Dhruvin',
    userPosition: 'Sr Manager, Sprinklr',
    startCount: 5,
  },
  {
    imageUrl: '/images/testimonials-people/dipankar.jpg',
    body: 'As an agency, we needed a tool that could scale and GrowEasy delivered. It automated repetitive tasks and gave us actionable insights, helping our clients achieve great results effortlessly.',
    userName: 'Dipankar',
    userPosition: 'CEO, Zendot',
    startCount: 5,
  },
  {
    imageUrl: '/images/testimonials-people/yash.jpg',
    body: "Launching a lead generation campaign has never been easier. With GrowEasy's AI, I set up my ads in 5 minutes, and the platform helped me achieve a higher return on ad spend than ever before.",
    userName: 'Yash Chaudhary',
    userPosition: 'Founder, Flabs',
    startCount: 5,
  },
];

const WhatPeopleSaySection = () => {
  return (
    <div className=" max-w-[1600px] mx-auto  ">
      <div className="pt-24 space-y-16 relative max-w-[1400px] mx-auto px-2 md:px-8 ">
        <Heading
          level={2}
          className=" !text-[28px]  md:!text-[42px] !font-bricolage-grotesque max-w-5xl tracking-tight text-center mx-auto "
        >
          Trusted by{' '}
          <span className="text-primary2 text-glow">
            100+ growing businesses
          </span>
        </Heading>
        <div className=" grid grid-cols-1 px-4 max-w-7xl mx-auto sm:grid-cols-2 gap-6 ">
          {data.map((d, index) => (
            <Card
              key={index}
              imageUrl={d.imageUrl}
              body={d.body}
              userName={d.userName}
              userPosition={d.userPosition}
              starCount={d.startCount}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

interface ICardProps {
  imageUrl: string;
  body: string;
  userName: string;
  userPosition: string;
  starCount: number;
}

const Card = (props: ICardProps) => {
  const { imageUrl, body, userName, userPosition, starCount } = props;
  return (
    <div className=" flex-1 bg-gradient-to-tr from-primary to-primary2 shadow hover:shadow-lg hover:-translate-y-1 cursor-pointer transition-all duration-300 rounded-2xl p-6 text-white relative overflow-hidden">
      <div className=" flex justify-between mb-3 items-center">
        <div className="text-secondary w-12 md:w-16 md:h-16 flex items-center justify-center ">
          <QuotationIcon />
        </div>
        <div className=" flex items-center">
          {Array.from({ length: starCount }).map((_, index) => (
            <div className=" w-8 h-8 text-yellow-400" key={index}>
              <StartIcon />
            </div>
          ))}
        </div>
      </div>
      <img
        src="/images/groweasy-logo-square.svg"
        alt=""
        className="absolute bottom-0 right-16 opacity-5 z-0 scale-150"
      />

      <div className="mb-14 z-10 relative">
        <BodyV2 variant="sm" className="md:leading-snug">
          {body}
        </BodyV2>
      </div>

      <div className=" flex items-center gap-4">
        <Image
          src={imageUrl}
          height={72}
          width={72}
          className=" rounded-full "
          alt=" User Image "
        />
        <div className=" flex flex-col">
          <h2 className="text-white font-semibold text-lg md:text-xl">
            {userName}
          </h2>
          <BodyV2 variant="sm" className=" text-gray-400 ">
            {userPosition}
          </BodyV2>
        </div>
      </div>
    </div>
  );
};

export default WhatPeopleSaySection;
