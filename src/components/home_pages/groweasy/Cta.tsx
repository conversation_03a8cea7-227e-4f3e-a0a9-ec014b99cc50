import RocketIcon from '@/images/icons/rocket-launch-icon.svg';
import Link from 'next/link';
import { BiChevronRight } from 'react-icons/bi';

export default function Cta() {
  return (
    <section className="py-10 overflow-hidden bg-gradient-to-r from-primary to-primary via-primary2 text-white relative mt-10 mb-20 group max-w-[1400px] mx-auto px-4 md:px-8 md:pt-24 rounded-3xl">
      <div className="absolute top-60 -left-40 blur-[100px] h-28 w-60 bg-teal-400/60 " />
      <div className="absolute top-60 -right-40 blur-[100px] h-28 w-60 bg-teal-400/60 " />
      <div className=" top-0 -right-9 md:top-5 -translate-x-full translate-y-full transition-all duration-1000 group-hover:translate-x-0 group-hover:translate-y-0 absolute w-[350px] h-[350px] md:w-[450px] md:h-[450px] flex items-center justify-center text-white/20">
        <RocketIcon />
      </div>
      <div className="container mx-auto px-4 text-center font-poppins py-8 md:py-4">
        <h2 className="text-3xl md:text-4xl font-bold mb-6  mx-auto">
          Marketing Doesn&apos;t Have to Be Hard. Let AI Do the Work.
        </h2>
        <p className="text-base md:text-xl text-teal-100 mb-8">
          Start getting leads in 5 minutes – no marketing team needed.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/login">
            <button className="bg-secondary text-white text-sm items-center md:text-xl font-semibold py-2 md:py-4 px-6 tracking-tight rounded-full inline-flex gap-2 active:scale-95 transition-all">
              Launch Your First Campaign <BiChevronRight size={30} />
            </button>
          </Link>
          <Link href="/login">
            <button className="bg-transparent text-white border-2 border-white/80 text-sm items-center md:text-xl font-semibold py-3 md:py-4 px-6 rounded-full hover:bg-white/10 transition-all duration-300 active:scale-95">
              Log In
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
}
