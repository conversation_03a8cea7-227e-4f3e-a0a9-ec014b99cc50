import { FaMoneyBill1Wave } from 'react-icons/fa6';
import { ImConfused2 } from 'react-icons/im';
import { BsGraphDownArrow } from 'react-icons/bs';
import Link from 'next/link';

export default function WhyUs() {
  const problems = [
    {
      icon: <FaMoneyBill1Wave size={40} />,
      title: 'Agencies are expensive',
      description: 'and take weeks to deliver',
    },
    {
      icon: <ImConfused2 size={40} />,
      title: 'Doing it yourself is overwhelming',
      description: '- too many tools, too little time',
    },
    {
      icon: <BsGraphDownArrow size={40} />,
      title: 'Leads are dropping off',
      description: 'without smart follow-ups',
    },
  ];

  return (
    <section className="py-24">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 md:mb-20">
          <h2 className="mb-3 font-bricolage-grotesque text-3xl md:text-5xl font-semibold">
            What&apos;s <span className="text-primary2">Holding You Back?</span>
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 max-w-7xl mx-auto">
          {problems.map((problem, index) => (
            <div className="flex justify-center relative p-2 group" key={index}>
              <div className="h-1/3 group-hover:translate-x-1 group-hover:-translate-y-1 transition-all w-1/3 bg-primary2 rounded-xl absolute top-0 right-0 z-0" />
              <div className="h-1/3 group-hover:-translate-x-1 group-hover:translate-y-1 transition-all w-1/3 bg-primary2 rounded-xl absolute bottom-0 left-0 z-0" />

              <div className="flex flex-col border px-4 md:px-8 py-10 md:py-16 shadow-md hover:shadow-xl transition-all duration-300 rounded-xl relative z-10 bg-white">
                <div className="bg-primary2 text-white rounded-xl mb-6 w-16 h-16 flex items-center justify-center p-4">
                  {problem.icon}
                </div>
                <h3 className="text-lg md:text-xl text-start font-semibold mb-2 tracking-tight">
                  {problem.title}{' '}
                  <span className="text-gray-600 font-medium">
                    {problem.description}
                  </span>
                </h3>
              </div>
            </div>
          ))}
        </div>

        <div className="flex flex-col gap-4 md:flex-row justify-between mx-auto items-center mt-16 md:mt-20 max-w-7xl px-2">
          <p className="text-2xl md:text-4xl tracking-tight font-semibold text-center">
            That&apos;s why we built{' '}
            <span className="text-primary2">GrowEasy</span>.
          </p>

          <Link href="/login">
            <button className="bg-secondary text-white text-base md:text-xl font-semibold py-4 px-6 rounded-full hover:opacity-90 active:scale-95 transition-all">
              Launch Your Campaign
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
}
