import React from 'react';
import Image from 'next/image';
import BodyV2 from '@/components/lib/typography/BodyV2';
import Heading from '@/components/lib/typography/Heading';
import Link from 'next/link';

interface IHeroV2Props {
  startButtonRef: React.MutableRefObject<HTMLDivElement>;
}

const TickIcon = (props: { className?: string }) => {
  const { className = '' } = props;

  return (
    <div className={className}>
      <svg
        width="24"
        height="34"
        viewBox="0 0 24 34"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          y="5"
          width="24"
          height="24"
          rx="12"
          fill="white"
          fillOpacity="0.15"
        />
        <path
          d="M7.5 17.2143L10.5 20.2143L16.5 13.7858"
          stroke="white"
          strokeWidth="1.28571"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

const HeroV2 = (props: IHeroV2Props) => {
  const { startButtonRef } = props;

  return (
    <div className="relative bg-primary pt-16 md:pt-40 min-h-screen md:pb-24 flex items-center md:justify-start justify-center overflow-hidden">
      <div className="max-w-[1400px] mx-auto px-4 md:px-8 flex flex-col md:flex-row items-start w-full">
        <Image
          src="/images/groweasy-logo-square.svg"
          width={1000}
          height={1000}
          alt="Groweasy Logo"
          className="absolute -bottom-20 md:-bottom-40 -left-20 z-10 w-[1000px] invert opacity-5"
        />

        {/* Left side content */}
        <div className="w-full md:w-[60%] text-white z-10">
          <Heading
            level={1}
            className="md:!text-5xl md:!leading-snug mb-6 mr-10 tracking-tight !leading-snug"
          >
            Grow Your Business with AI-Powered Digital Marketing
          </Heading>

          <p className="text-sm md:text-base mb-4">
            Get 2X – 5X better results using GrowEasy’s smart, automated
            marketing tools.
          </p>

          <div className="flex items-start gap-2 mb-2">
            <TickIcon />
            <p className="text-sm md:text-base mt-1">
              Generate high-quality leads in just 5 minutes
            </p>
          </div>

          <div className="flex items-start gap-2">
            <TickIcon />
            <div>
              <p className="text-sm md:text-base mt-1">
                Launch AI-powered ads effortlessly:
              </p>
              <ul className="list-disc list-inside pl-2 mt-2 text-sm md:text-base space-y-1">
                <li>Ad banners & video ads</li>
                <li>Ad copy & forms</li>
                <li>Lead qualification</li>
                <li>Optimization & targeting</li>
              </ul>
            </div>
          </div>

          <div
            className="flex flex-col sm:flex-row gap-4 mt-8"
            ref={startButtonRef}
          >
            <Link href="/login">
              <button className="bg-secondary active:scale-95 hover:bg-secondary/90 text-white font-medium px-8 py-4 rounded-full flex items-center">
                Start Now
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 ml-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </Link>

            {/* <Link href="/login">
              <button className="bg-white/10 active:scale-95 hover:bg-white/20 text-white font-medium px-8 py-4 rounded-full border border-white/30 backdrop-blur">
                Login to Continue
              </button>
            </Link> */}
          </div>

          <BodyV2 variant="xs" className="mt-2 px-2 text-white/70">
            No credit card required
          </BodyV2>
        </div>

        <div className="absolute bottom-0 right-0 z-20 hidden md:block">
          <Image
            src="/images/heroimg.png"
            width={800}
            height={400}
            alt="GrowEasy Dashboard Preview"
            className="heroclamp hidden lg:block"
            priority
          />
        </div>
        <div className="absolute inset-0 bg-gradient-to-br from-primary to-primary2 opacity-80"></div>
      </div>
    </div>
  );
};

export default HeroV2;
