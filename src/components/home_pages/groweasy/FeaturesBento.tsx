import dynamic from 'next/dynamic';

const AIPoweredIllustration = dynamic(
  () => import('../../illustrations/ai-banner-illustration'),
);
const OptimizationIllustration = dynamic(
  () => import('../../illustrations/optimization-illustration'),
);
const WhatsappIllustration = dynamic(
  () => import('../../illustrations/whatsapp-illustration'),
);
const VernacularIllustration = dynamic(
  () => import('../../illustrations/vernacular-illustration'),
);
const CRMIllustration = dynamic(
  () => import('../../illustrations/crm-illustration'),
);

import React from 'react';
import Heading from '@/components/lib/typography/Heading';

interface Feature {
  title: string;
  description: string;
  colSpan: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  component: React.ComponentType<any>;
}

const features: Feature[] = [
  {
    title: 'AI powered Ad Launch',
    description:
      'Leverage cutting-edge AI to create and launch high-converting ads campaigns',
    colSpan: 'md:col-span-3',
    component: AIPoweredIllustration,
  },
  {
    title: '24/7 optimization',
    description:
      'Our system works round the clock to optimize your campaigns for maximum performance',
    colSpan: 'md:col-span-4',
    component: OptimizationIllustration,
  },
  {
    title: 'Whatsapp integration',
    description:
      'Seamlessly connect with your customers on WhatsApp for improved engagement',
    colSpan: 'md:col-span-3',
    component: WhatsappIllustration,
  },
  {
    title: 'Vernacular Ad support',
    description:
      'Create ads in multiple languages to reach a diverse regional audiences and increase engagement',
    colSpan: 'md:col-span-5',
    component: VernacularIllustration,
  },
  {
    title: 'Free built-in CRM',
    description:
      'Manage your customer relationships with our free and advanced built-in CRM system',
    colSpan: 'md:col-span-5',
    component: CRMIllustration,
  },
];

const FeaturesBento: React.FC = () => {
  return (
    <div className="max-w-[1600px] mx-auto">
      <div className="pt-16 md:pt-24 relative z-[1] max-w-[1350px] mx-auto px-4 md:px-8">
        <div className="space-y-4">
          <Heading
            level={2}
            className="text-3xl md:text-5xl font-bricolage-grotesque max-w-5xl text-center mx-auto bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent py-1"
          >
            Why Businesses Love{' '}
            <span className="text-primary2 text-glow">Groweasy</span>
          </Heading>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-10 gap-4 mt-14 w-full">
          {features.map((feature, index) => {
            const IllustrationComponent = feature.component;

            return (
              <div
                key={index}
                className={`px-6 pt-10 border bg-white rounded-xl hover:border-teal-600/70 border-teal-600/30 relative overflow-hidden shadow hover:shadow-lg transition-shadow duration-300 ease-in-out group ${feature.colSpan} min-h-80 flex flex-col  cursor-pointer`}
              >
                <div className="absolute -bottom-2/3 -right-1/2 w-full h-full bg-gradient-to-br from-teal-200 to-teal-400 opacity-10 blur-[100px]"></div>
                <div className="absolute -right-2/3 -top-12 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#d0ede29e_1px,transparent_1px),linear-gradient(to_bottom,#d0ede29e_1px,transparent_1px)] bg-[size:24px_24px] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)]"></div>
                <div className="absolute h-1 w-full bg-gradient-to-r via-teal-500 from-transparent to-transparent blur-xl top-0 left-0"></div>

                <div className="relative z-10 mb-4">
                  <h2 className="text-lg md:text-3xl font-semibold tracking-tight group-hover:text-primary2 transition-all">
                    {feature.title}
                  </h2>
                  <p className="text-sm mt-3 md:text-lg text-gray-dark md:leading-snug">
                    {feature.description}
                  </p>
                </div>

                <div className="mt-auto relative flex-grow flex items-end justify-center overflow-hidden h-64">
                  <div className="absolute top-0 left-0 bg-gradient-to-b from-white to-transparent w-full h-1/5 z-10"></div>
                  <IllustrationComponent />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default FeaturesBento;
