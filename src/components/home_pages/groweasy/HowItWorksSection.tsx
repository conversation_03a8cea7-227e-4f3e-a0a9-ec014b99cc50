import { BiRocket } from 'react-icons/bi';
import { BsPeople } from 'react-icons/bs';
import { IoColorPalette, IoLogoWhatsapp } from 'react-icons/io5';

const data = [
  {
    icon: <BsPeople size={40} color="white" />,
    heading: 'Step 1',
    body: 'Tell us about your business and marketing goals',
  },
  {
    icon: <IoColorPalette size={40} color="white" />,
    heading: 'Step 2',
    body: 'Groweasy creates creatives, ad copies and budget for you',
  },
  {
    icon: <BiRocket size={40} color="white" />,
    heading: 'Step 3',
    body: 'Launch across Google, Instagram, Youtube & Facebook and generate leads',
  },
  {
    icon: <IoLogoWhatsapp size={40} color="white" />,
    heading: 'Step 4',
    body: 'AI whatsaap agent talks to generate leads and qualifies them',
  },
];

const HowItWorksSection = () => {
  return (
    <div className=" relative max-w-[1600px] mx-auto  ">
      <div className=" relative z-[1] max-w-[1500px] mx-auto px-4 md:px-8 pt-24 ">
        <div className=" py-16 md:py-20 px-2 ">
          <h2 className="font-bricolage-grotesque text-center tracking-tight text-3xl md:text-5xl font-semibold ">
            Run Smart campaigns in
            <span className="text-primary2 text-glow"> 4 simple steps</span>
          </h2>
          <div className="flex flex-col mt-10 gap-10 items-center justify-center">
            <div className=" grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl w-full">
              {data.map((item, index) => (
                <div
                  key={index}
                  className="flex flex-col items-start justify-start text-center p-4 md:pb-6 md:p-8 bg-white rounded-xl shadow hover:shadow-lg transition duration-300 ease-in-out"
                >
                  <div className="w-14 h-14 md:w-20 md:h-20 flex items-center justify-center p-2 rounded-xl bg-primary2 mb-4">
                    {item.icon}
                  </div>
                  <h3 className="text-xl md:text-2xl font-semibold tracking-tight mb-2">
                    {item.heading}
                  </h3>
                  <p className="text-gray-600 md:leading-snug md:text-lg text-start">
                    {item.body}
                  </p>
                </div>
              ))}
            </div>
            <div className="flex items-center justify-center w-full max-w-6xl bg-gradient-to-r from-primary2 to-teal-700 rounded-xl shadow-lg px-5 md:px-8 py-12 md:py-2 relative">
              <div className="md:basis-2/3 flex flex-col items-start justify-center text-white">
                <h2 className="text-xl md:text-4xl font-semibold tracking-tight leading-tight mb-6 pr-6">
                  Get qualified leads in your CRM or use GrowEasy AI powered CRM
                </h2>
                <p className="md:text-xl font-semibold tracking-tight mb-1">
                  <span className="text-secondary">NO </span>
                  Agencies
                </p>
                <p className="md:text-xl font-semibold tracking-tight mb-1">
                  <span className="text-secondary">NO </span> Ad manager
                </p>
                <p className="md:text-xl font-semibold tracking-tight">
                  <span className="text-secondary">NO </span> Stress
                </p>
              </div>
              <div className="basis-1/3 overflow-hidden absolute -right-1 bottom-0 md:relative">
                <img
                  src="/images/Visual data.svg"
                  alt="CRM"
                  className="w-32 md:w-full h-auto p-2"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HowItWorksSection;
