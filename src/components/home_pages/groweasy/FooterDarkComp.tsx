import Image from 'next/image';
import BodyV2 from '@/components/lib//typography/BodyV2';
import FooterNavlinks from './FooterNavigation';

const FooterDarkComp = ({
  removePadding = false,
}: {
  removePadding?: boolean;
}) => {
  return (
    <div
      className={` bg-[#131C1B] text-off-white ${
        removePadding ? ' pt-6 ' : 'pt-20'
      }`}
    >
      <div className=" relative max-w-[1400px] mx-auto pb-[30px] md:pb-[60px] px-4 md:px-8 ">
        <div>
          <div className={`flex flex-col `}>
            <div className="flex items-center justify-center gap-6 md:gap-10">
              <BodyV2 variant="xs" className=" text-center font-light ">
                Building in India 🇮🇳
              </BodyV2>
            </div>

            <div className="flex justify-center my-6 items-center gap-4 flex-wrap md:gap-20">
              <a
                title="GrowEasy - Generative AI powered all in one lead generation app : Product Hunt"
                rel="noreferrer noopener"
                href="https://www.producthunt.com/products/groweasy?utm_source=badge-top-post-badge&utm_medium=badge#groweasy"
                target="_blank"
              >
                <Image
                  src="https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=430187&theme=light&period=daily"
                  alt="GrowEasy - Generative&#0032;AI&#0032;powered&#0032;all&#0032;in&#0032;one&#0032;lead&#0032;generation&#0032;app | Product Hunt"
                  width={200}
                  height={54}
                  loading="lazy"
                  fetchPriority="low"
                  className="w-44 h-10"
                />
              </a>
            </div>
            <FooterNavlinks />
            {/* <div>
              <div className="space-y-1">
                <p className="text-xs">Recognised by</p>
                <Image
                  width={200}
                  height={54}
                  loading="lazy"
                  fetchPriority="low"
                  src="/images/agencies-logos/startup-india-logo.webp"
                  alt="Startup India Logo"
                  className="w-32 h-7 object-contain bg-slate-50 p-px rounded-md"
                />
              </div>
              <div className="space-y-1">
                <p className="text-xs">Business Partner</p>
                <Image
                  width={200}
                  height={54}
                  loading="lazy"
                  fetchPriority="low"
                  src="/images/agencies-logos/meta.png"
                  alt="Meta Logo"
                  className="w-32 h-7 object-contain bg-slate-50 p-px rounded-md"
                />
              </div>
            </div> */}
            <div className="h-0.5 w-full my-10 bg-gray-dark bg-opacity-30 " />
            <p className="text-xs text-center">
              &copy; 2024 AUTOTME SOFTWARE PRIVATE LIMITED. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FooterDarkComp;
