import React from 'react';
import Image from 'next/image';
import BodyV2 from '@/components/lib//typography/BodyV2';
import Link from 'next/link';
import Heading from '@/components/lib//typography/Heading';

const data = [
  {
    imageUrl: '/images/about-us/tej-pandya.jpeg',
    userName: 'Te<PERSON>',
    userDescription: 'Co-founder & CEO',
    descriptionHeading: 'Formerly at INDmoney, 5paisa, ShopClues',
    descriptionBody:
      "Master of Business Administration - MBA, Analytics SVKM's Narsee Monjee Institute of Management Studies (NMIMS)",
    href: 'https://www.linkedin.com/in/tejpandya/',
  },
  {
    imageUrl: '/images/about-us/varun.jpeg',
    userName: 'Varu<PERSON>',
    userDescription: 'Co-founder & CT<PERSON>',
    descriptionHeading: 'Formerly at INDmoney, Goibibo, Oracle',
    descriptionBody: 'B.Tech (IT)  NIT Kurukshetra 2013-2017',
    href: 'https://www.linkedin.com/in/varunon9/',
  },
];

const MeetTheTeam = () => {
  return (
    <div className=" relative max-w-[1600px] mx-auto  ">
      <div className=" absolute top-0 left-0 max-md:hidden w-full z-0 h-full flex justify-between px-4 ">
        <div className=" w-[1px] h-full bg-[#767676] bg-opacity-10 " />
        {/* <div className=" w-[1px] h-full bg-[#767676] bg-opacity-10 " />
        <div className=" w-[1px] h-full bg-[#767676] bg-opacity-10 " /> */}
        <div className=" w-[1px] h-full bg-[#767676] bg-opacity-10 " />
      </div>
      <div className=" relative m-[1] max-w-[1400px] mx-auto px-4 md:px-8 ">
        <div className=" pt-[60px] md:pt-[100px] space-y-[60px] md:space-y-[100px] ">
          <Heading
            level={2}
            className=" !text-[28px]  md:!text-[42px] !font-bricolage-grotesque max-w-5xl text-center mx-auto "
          >
            Meet the Team
          </Heading>
          <div className=" flex gap-6 flex-wrap justify-center ">
            {data.map((d, index) => (
              <Card key={index} {...d} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

interface ICardProps {
  imageUrl: string;
  userName: string;
  userDescription: string;
  descriptionHeading: string;
  descriptionBody: string;
  href: string;
}

const Card = (props: ICardProps) => {
  const {
    imageUrl,
    userName,
    userDescription,
    descriptionHeading,
    descriptionBody,
    href,
  } = props;

  return (
    <Link
      href={href}
      target="_blank"
      className=" shadow-sm border-2 border-[#225E57] border-opacity-20 hover:border-opacity-40 transition-all duration-300 bg-white rounded-xl max-w-sm w-full flex flex-col place-items-center space-y-6 px-4 py-8 backdrop-blur-sm "
    >
      <div className=" rounded-full overflow-hidden border-4 border-[#225E57] ">
        <Image
          src={imageUrl}
          width={140}
          height={140}
          className=" "
          alt="Founders Image"
        />
      </div>
      <div className=" text-center space-y-2">
        <BodyV2 className=" font-bold">{userName}</BodyV2>
        <BodyV2 variant="sm" className=" text-[#F57141] font-semibold ">
          {userDescription}
        </BodyV2>
      </div>

      <div className=" bg-[#F57141] h-1 w-[100px] " />

      <div className=" text-center space-y-2 ">
        <BodyV2 variant="sm" className=" font-medium text-[#767676] ">
          {descriptionHeading}
        </BodyV2>
        <BodyV2 variant="sm" className="  !text-base text-[#767676] ">
          {descriptionBody}
        </BodyV2>
      </div>
    </Link>
  );
};

export default MeetTheTeam;
