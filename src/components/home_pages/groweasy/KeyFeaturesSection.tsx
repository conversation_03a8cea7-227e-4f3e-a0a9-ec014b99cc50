import React from 'react';
import BodyV2 from '@/components/lib//typography/BodyV2';
import PasteIcon from '@/images/icons/paste-icon.svg';
import DesignServiceIcon from '@/images/icons/design-service-icon.svg';
import ExperimentIcon from '@/images/icons/experiment-icon.svg';
import BarChartIcon from '@/images/icons/bar-chart-icon.svg';
import CampaignIcon from '@/images/icons/campaign-icon.svg';
import AddCardIcon from '@/images/icons/addcard-icon.svg';
import Heading from '@/components/lib//typography/Heading';

const data = [
  {
    icon: <PasteIcon />,
    heading: 'AI-Powered Copywriting',
    body: 'Generate persuasive, conversion-optimized ad copy instantly.',
  },
  {
    icon: <DesignServiceIcon />,
    heading: 'AI Graphic Designing',
    body: 'Craft engaging, high-quality visuals tailored for your campaigns.',
  },
  {
    icon: <ExperimentIcon />,
    heading: 'AI Experiment Engine',
    body: 'Test multiple creatives to see what works best',
  },
  {
    icon: <BarChartIcon />,
    heading: 'AI Analyst',
    body: 'Get insights in real-time with advanced data analysis for better decision-making.',
  },
  {
    icon: <CampaignIcon />,
    heading: '24/7 Campaign Optimization',
    body: 'Automated Fine-tuning of your campaigns with smart, data-driven optimizations.',
  },
  {
    icon: <AddCardIcon />,
    heading: 'Automated Budget Allocation',
    body: 'Distribute ad spending efficiently across platforms like Google and Facebook for maximum ROAS.',
  },
];

const KeyFeaturesSection = () => {
  return (
    <div className=" relative max-w-[1600px] mx-auto  ">
      <div className=" absolute top-0 left-0 max-md:hidden w-full z-0 h-full flex justify-between px-4 ">
        <div className=" w-[1px] h-full bg-[#767676] bg-opacity-10 " />
        {/* <div className=" w-[1px] h-full bg-[#767676] bg-opacity-10 " />
        <div className=" w-[1px] h-full bg-[#767676] bg-opacity-10 " /> */}
        <div className=" w-[1px] h-full bg-[#767676] bg-opacity-10 " />
      </div>
      <div className=" relative z-[1] max-w-[1400px] mx-auto px-4 md:px-8">
        <div className=" pt-[60px] md:pt-[100px] space-y-[92px] md:space-y-[132px] ">
          <div className=" space-y-4 ">
            <Heading
              level={2}
              className=" !text-[28px]  md:!text-[42px] !font-bricolage-grotesque max-w-5xl text-center mx-auto "
            >
              Key Features of Our AI Marketing Platform
            </Heading>
            <BodyV2 className=" max-w-5xl text-center mx-auto">
              Discover how AI can transform your marketing strategy, boost
              conversions, and streamline your workflow effortlessly.
            </BodyV2>
          </div>
          <div className=" grid grid-cols-1 min-[550px]:grid-cols-2 min-[900px]:grid-cols-3 gap-x-6 gap-y-16">
            {data.map((d, index) => (
              <Card
                icon={d.icon}
                heading={d.heading}
                body={d.body}
                key={index}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

interface ICardProps {
  icon: React.JSX.Element;
  heading: string;
  body: string;
}

const Card = (props: ICardProps) => {
  const { icon, heading, body } = props;

  return (
    <div className=" relative flex-1 pt-20 border-2 bg-white shadow-sm border-[#225E57] border-opacity-20 hover:border-opacity-40 rounded-xl px-6 pb-3 backdrop-blur-sm ">
      <div className=" absolute top-0 left-[10%] -translate-y-1/2 p-2.5 rounded-md bg-[#225E57]">
        <div className="  w-11 h-11 flex items-center justify-center text-white ">
          {icon}
        </div>
      </div>
      <div>
        <div className=" space-y-2">
          <Heading level={3} className=" font-semibold !text-xl md:!text-2xl ">
            {heading}
          </Heading>
          <BodyV2 variant="sm">{body}</BodyV2>
        </div>
      </div>
    </div>
  );
};

export default KeyFeaturesSection;
