import React from 'react';
import StarIcon from '@/images/icons/star-icon.svg';
import BodyV2 from '@/components/lib/typography/BodyV2';
import StartNowCTA from '@/components/home_pages/groweasy/buttons/StartNowCTA';
import Image from 'next/image';
import GoogleIcon from '@/images/common/google.svg';
import FacebookIcon from '@/images/common/facebook.svg';
import Heading from '@/components/lib/typography/Heading';

interface IHeroProps {
  startButtonRef: React.MutableRefObject<HTMLDivElement>;
}

const Hero = (props: IHeroProps) => {
  const { startButtonRef } = props;
  return (
    <div className=" relative max-w-[1600px] mx-auto ">
      <main className=" relative z-[1] max-w-[1400px] mx-auto px-4 md:px-8">
        <div className=" pt-[160px] md:pt-[200px] flex flex-col space-y-6 ">
          <div className=" flex items-center gap-2 bg-[#FBC5AE] py-2.5 px-6 rounded-3xl w-fit mx-auto ">
            <div className=" font-medium flex max-xs:flex-col items-center gap-2 flex-wrap text-center ">
              <span className=" mx-auto flex  gap-2 items-center">
                <div className=" h-4 w-4 flex ">
                  <StarIcon />
                </div>
                <BodyV2 variant="xs">
                  Drive High ROAS Effortlessly Across
                </BodyV2>
              </span>
              <span className=" flex items-center gap-2 mx-auto">
                <GoogleIcon className="h-6 w-6" />&
                <FacebookIcon className="h-6 w-6" />
              </span>
            </div>
          </div>

          <Heading
            level={1}
            className=" !text-[28px]  md:!text-[42px] !font-bricolage-grotesque max-w-5xl text-center mx-auto "
          >
            Launch AI Driven Ad Campaigns in 5 Minutes
          </Heading>

          <BodyV2 variant="sm" className=" max-w-5xl text-center mx-auto">
            With our platform, launching high-conversion ad campaigns has never
            been easier. Automate, optimize, and accelerate your marketing
            efforts—whether you{"'"}re an agency or a business owner—achieve the
            best return on ad spend (ROAS) without the guesswork.
          </BodyV2>

          <div
            className=" flex items-center justify-center"
            ref={startButtonRef}
          >
            <StartNowCTA />
          </div>

          <div className="relative z-10 w-full h-fit translate-y-10 md:translate-y-16 shadow-md">
            <Image
              src="/images/landing-page/landing-page-image.png"
              width={1336}
              height={752}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1336px"
              alt="landing page image"
              priority
            />
          </div>
        </div>
      </main>
    </div>
  );
};

export default Hero;
