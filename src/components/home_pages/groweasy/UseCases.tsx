import Heading from '@/components/lib/typography/Heading';
import Link from 'next/link';
import React from 'react';
import {
  FaUser,
  FaShoppingCart,
  FaWhatsapp,
  FaGraduationCap,
  FaArrowRight,
} from 'react-icons/fa';

const UseCaseCard = ({
  icon,
  title,
  industries,
  className = '',
}: {
  icon: React.ReactNode;
  title: string;
  industries: string[];
  className?: string;
}) => {
  return (
    <div
      className={`bg-white p-6 rounded-xl shadow-sm border border-gray-100 ${className}`}
    >
      <div className="flex items-center mb-4">
        <div className="bg-primary2 p-2 rounded-md mr-3 flex items-center justify-center">
          {icon}
        </div>
        <h3 className="text-gray-800 font-medium text-lg">{title}</h3>
      </div>

      <div>
        <p className="text-gray-500 mb-2 text-sm">Ideal for:</p>
        <ul className="grid grid-cols-1 gap-x-2 gap-y-1">
          {industries.map((industry, index) => (
            <li key={index} className="flex items-center mb-1.5">
              <span className="h-5 w-5 rounded-full bg-orange-100 flex-shrink-0 mr-2 flex items-center justify-center">
                <span className="text-orange-400 text-xs">✓</span>
              </span>
              <span className="text-gray-700 text-sm">{industry}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default function UseCases() {
  const useCases = [
    {
      icon: <FaUser size={18} color="white" />,
      title: 'Generate Leads of Prospective Buyers',
      industries: [
        'Dentists',
        'Clinics',
        'Travel agents',
        'Real estate agents',
        'Financial consultants',
        'B2B SaaS companies',
        'Automobile dealers',
        'Gyms',
        'Salons',
        'Interior designers',
        'Construction companies',
        'Home services companies',
        'Event and wedding service providers',
      ],
    },
    {
      icon: <FaShoppingCart size={18} color="white" />,
      title: 'Drive Purchases on Your Website',
      industries: ['D2C brands', 'eCommerce stores', 'Subscription businesses'],
    },
    {
      icon: <FaWhatsapp size={18} color="white" />,
      title: 'Get Inquiries via WhatsApp Message (CTWA)',
      industries: [
        'Local businesses',
        'Service providers',
        'WhatsApp Business app users',
      ],
    },
    {
      icon: <FaGraduationCap size={18} color="white" />,
      title: 'Get Student Applications on Your Website',
      industries: [
        'EdTech platforms',
        'Coaching institutes',
        'Universities',
        'Schools',
        'Colleges',
        'Study abroad institutes',
      ],
    },
  ];

  return (
    <div className="max-w-6xl mx-auto px-4 py-10">
      <Heading
        level={2}
        className="mb-6 md:mb-10 text-3xl md:text-5xl font-bricolage-grotesque max-w-5xl text-center mx-auto bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent py-1"
      >
        Powerful <span className="text-primary2">Use Cases</span>
      </Heading>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 lg:grid-rows-2 gap-6 md:auto-rows-fr">
        <UseCaseCard
          icon={useCases[0].icon}
          title={useCases[0].title}
          industries={useCases[0].industries}
          className="lg:row-span-2"
        />
        <UseCaseCard
          icon={useCases[1].icon}
          title={useCases[1].title}
          industries={useCases[1].industries}
        />
        <UseCaseCard
          icon={useCases[2].icon}
          title={useCases[2].title}
          industries={useCases[2].industries}
        />
        <UseCaseCard
          icon={useCases[3].icon}
          title={useCases[3].title}
          industries={useCases[3].industries}
          className="lg:col-span-2"
        />
      </div>

      <div className="mt-6"></div>

      <div className="flex justify-center mt-10">
        <Link href="/login">
          <button className="bg-secondary text-white text-base md:text-lg font-semibold py-4 px-6 rounded-full inline-flex items-center active:scale-95 transition-all">
            Launch Your First Campaign
            <FaArrowRight className="ml-2" />
          </button>
        </Link>
      </div>
    </div>
  );
}
