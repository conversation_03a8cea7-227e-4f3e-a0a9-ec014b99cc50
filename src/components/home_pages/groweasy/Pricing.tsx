import HeadingV2 from '@/components/lib/typography/HeadingV2';
import { FaGift } from 'react-icons/fa';
import { FaMoneyCheckAlt } from 'react-icons/fa';
import { RiNodeTree } from 'react-icons/ri';
import { IconType } from 'react-icons';
import Link from 'next/link';

const pricingCards: {
  icon: IconType;
  description: string;
}[] = [
  {
    icon: FaMoneyCheckAlt,
    description: 'Monthly subscription starting at just ₹1,499 / $19',
  },
  {
    icon: RiNodeTree,
    description:
      'Options to choose from your own ad accounts or GrowEasy agency accounts',
  },
  {
    icon: FaGift,
    description: 'Tailored agency plan with white-labeling features',
  },
];

export default function Pricing() {
  return (
    <section id="pricing" className="py-20 md:py-28 relative">
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <HeadingV2 className="mb-3">
            Simple,{' '}
            <span className="text-primary2 text-glow">Growth-Focused</span>{' '}
            Pricing
          </HeadingV2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Transparent pricing designed to scale with your success. No hidden
            fees, just results.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {pricingCards.map((card, index) => (
            <div
              key={index}
              className="flex flex-col items-center p-6 md:p-8 bg-white rounded-xl shadow border border-gray-200 cursor-pointer hover:shadow-lg transition-all duration-300 ease-in-out"
            >
              <div className="bg-primary2/10 text-primary2 p-4 rounded-full mb-5 w-16 h-16 flex items-center justify-center">
                <card.icon className="w-8 h-8" />
              </div>
              <p className="text-lg text-center font-medium">
                {card.description}
              </p>
            </div>
          ))}
        </div>

        <div className="flex justify-center mt-16">
          <Link href="/pricing">
            <button className="bg-secondary text-white text-base md:text-xl font-semibold py-4 px-6 rounded-full active:scale-95 transition-all">
              View Detailed Pricing Plans →
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
}
