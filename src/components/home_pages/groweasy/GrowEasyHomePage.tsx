import Head from 'next/head';
import { getMetaTags } from 'src/utils';
import dynamic from 'next/dynamic';
import { useEffect, useRef, useState } from 'react';
import HeroV2 from '@/components/home_pages/groweasy/HeroV2';

const Footer = dynamic(
  () => import('@/components/home_pages/groweasy/FooterDarkComp'),
);
const Header = dynamic(() => import('@/components/home_pages/groweasy/Header'));
const HowItWorksSection = dynamic(
  () => import('@/components/home_pages/groweasy/HowItWorksSection'),
);
const FeaturesBentoSection = dynamic(
  () => import('@/components/home_pages/groweasy/FeaturesBento'),
);

const WhatPeopleSaySection = dynamic(
  () => import('@/components/home_pages/groweasy/WhatPeopleSaySection'),
);
const WhyUs = dynamic(() => import('@/components/home_pages/groweasy/WhyUs'));
const Pricing = dynamic(
  () => import('@/components/home_pages/groweasy/Pricing'),
);
const CtaSection = dynamic(
  () => import('@/components/home_pages/groweasy/Cta'),
);
const PowerfulUseCases = dynamic(
  () => import('@/components/home_pages/groweasy/UseCases'),
);
const TrustedSection = dynamic(
  () => import('@/components/home_pages/groweasy/TrustedSection'),
);

const GrowEasyHomePage = () => {
  const [showStartCTA, setShowStartCTA] = useState(false);
  const startButtonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setShowStartCTA(!entry.isIntersecting);
      },
      { threshold: 0 },
    );

    if (startButtonRef.current) {
      observer.observe(startButtonRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div className="bg-off-white">
      <Head>{getMetaTags('/')}</Head>
      <div>
        <Header showStartCTA={showStartCTA} />
        <>
          <HeroV2 startButtonRef={startButtonRef} />
          <TrustedSection />
          <WhyUs />
          <FeaturesBentoSection />
          <HowItWorksSection />
          <PowerfulUseCases />
          <WhatPeopleSaySection />
          <Pricing />
          <CtaSection />
          <Footer />
        </>
      </div>
    </div>
  );
};

export default GrowEasyHomePage;
