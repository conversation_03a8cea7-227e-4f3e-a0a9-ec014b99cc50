'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

const data = [
  {
    imageURL: '/images/agencies-logos/flabs.svg',
    href: 'https://flabslis.com/',
  },
  {
    imageURL: '/images/agencies-logos/mapisa.png',
    href: 'https://www.shivazzasundaram.com/',
  },
  {
    imageURL: '/images/agencies-logos/purple-ribbon.png',
    href: 'https://purpleribbon.health/',
  },
  {
    imageURL: '/images/agencies-logos/zendot.png',
    href: 'https://zendot.in/',
  },
  {
    imageURL: '/images/agencies-logos/nivida.svg',
    href: 'https://www.nivida.in/',
  },
  {
    imageURL: '/images/agencies-logos/ziti.png',
    href: 'https://letsziti.com/',
  },
];

export default function TrustedSection() {
  return (
    <div className="relative bg-gradient-to-r from-primary to-primary via-primary2 pt-16 overflow-hidden">
      <div className="max-w-[1450px] mx-auto px-4 md:px-8 mb-16 md:mb-20 relative z-10">
        <div className="flex items-center gap-4">
          <div
            className="flex-1 h-[1.5px] max-sm:hidden"
            style={{
              background: 'linear-gradient(to right, rgba(0,0,0,0), #fff 76%)',
            }}
          />
          <p className="uppercase text-white flex-1 text-center md:whitespace-nowrap text-sm">
            trusted by marketing agencies and businesses worldwide
          </p>
          <div
            className="flex-1 h-[1.5px] max-sm:hidden"
            style={{
              background: 'linear-gradient(to left, rgba(0,0,0,0), #fff 76%)',
            }}
          />
        </div>
        <div className="max-w-[1450px] mx-auto mt-10  ">
          <div className=" min-w-[60rem] relative h-10 flex overflow-hidden [mask-image:linear-gradient(to_right,transparent,white_20%,white_80%,transparent)] ">
            {[...data].map((d, index) => (
              <ImageCard key={`${d.imageURL}-${index}`} {...d} index={index} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

interface IImageCardProps {
  imageURL: string;
  href: string;
  index: number;
}

function ImageCard({ imageURL, href, index }: IImageCardProps) {
  const iconCount = 6;
  const animationTime = 40;

  return (
    <div
      className="absolute h-10 flex items-center justify-center flex-shrink-0 left-full"
      style={{
        animation: `moveLeft ${animationTime}s infinite`,
        animationDelay: `${
          (animationTime / iconCount) * (iconCount - index - 1) * -1
        }s`,
        animationTimingFunction: 'linear',
      }}
    >
      <Link href={href} target="_blank">
        <div style={{ width: '10rem', height: '2.5rem', position: 'relative' }}>
          <Image
            src={imageURL}
            alt="company logo"
            fill
            style={{ objectFit: 'contain' }}
            sizes="10rem"
            className="opacity-80"
          />
        </div>
      </Link>
    </div>
  );
}
