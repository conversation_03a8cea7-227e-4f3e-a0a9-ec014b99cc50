import {
  GROWEASY_CAMPAIGN_TYPE,
  ICampaignInsightDetails,
} from 'src/types/campaigns';
import Accordion, { IAccordionProps } from '../lib/Accordion';
import { getMetaLeadsCountFromInsightDetails } from 'src/utils';
import SpinnerLoader from '../lib/SpinnerLoader';

interface IMetaAnalyticsByMediaAssetAccordionProps {
  className?: string;
  insights: ICampaignInsightDetails[];
  campaignType: GROWEASY_CAMPAIGN_TYPE;
  loading: boolean;
  accordionProps?: Partial<IAccordionProps>;
}

const MetaAnalyticsByMediaAssetAccordion: React.FC<
  IMetaAnalyticsByMediaAssetAccordionProps
> = ({ insights, campaignType, className = '', loading, accordionProps }) => {
  const onMediaAssetClick = (url: string) => {
    // in case of video, sometimes it comes as relative URL, e.g. /61567333153945/videos/1355550248784598/
    const absoluteUrl = url.startsWith('https')
      ? url
      : `https://facebook.com${url}`;
    if (window?.bridge) {
      window.bridge.postMessage(
        JSON.stringify({
          method: 'launch_url',
          args: absoluteUrl,
        }),
      );
    } else {
      window.open(absoluteUrl, '_blank');
    }
  };

  return (
    <Accordion
      title="Performance by Media Assets"
      className={className}
      {...accordionProps}
    >
      <div className="my-4">
        {loading ? (
          <div className="flex justify-center">
            <SpinnerLoader size={24} borderWidth={3} />
          </div>
        ) : (
          <table className="w-full border border-gray-300">
            <thead>
              <tr className="bg-gray-100 text-left text-xs font-medium">
                <th className="px-4 py-2 border border-gray-300">Media</th>
                <th className="px-4 py-2 border border-gray-300">Views</th>
                <th className="px-4 py-2 border border-gray-300">Clicks</th>
                <th className="px-4 py-2 border border-gray-300">
                  {[GROWEASY_CAMPAIGN_TYPE.META_SALES].includes(campaignType)
                    ? 'Conversions'
                    : 'Leads'}
                </th>
              </tr>
            </thead>
            <tbody>
              {insights.length > 0 ? (
                insights.map((item, index) => (
                  <tr key={index} className="text-xxs">
                    <td className="px-4 py-2 border border-gray-300">
                      <div
                        className="cursor-pointer"
                        onClick={() => onMediaAssetClick(item.media_asset?.url)}
                      >
                        {item.media_asset ? (
                          item.media_asset.asset_type === 'image_asset' ? (
                            <img
                              src={item.media_asset.url}
                              alt="Media Asset"
                              className="w-24 object-cover"
                            />
                          ) : (
                            <div className="relative w-24">
                              <img
                                src={
                                  item.media_asset.url?.startsWith('https')
                                    ? item.media_asset.url
                                    : item.media_asset.thumbnail_url
                                }
                                alt="Media Asset"
                                className="w-24 object-cover"
                              />
                              {/* Play Icon Overlay */}
                              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-lg">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="white"
                                  viewBox="0 0 24 24"
                                  className="w-10 h-10 opacity-90"
                                >
                                  <path d="M8 5v14l11-7z" />
                                </svg>
                              </div>
                            </div>
                            // <video
                            //   src={item.media_asset.url}
                            //   controls
                            //   className="w-24 object-cover"
                            // />
                          )
                        ) : (
                          'N/A'
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-2 border border-gray-300">
                      {item.impressions ?? '0'}
                    </td>
                    <td className="px-4 py-2 border border-gray-300">
                      {item.clicks ?? '0'}
                    </td>
                    <td className="px-4 py-2 border border-gray-300">
                      {getMetaLeadsCountFromInsightDetails(item, campaignType)}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={3}
                    className="px-4 py-4 text-center text-gray-500"
                  >
                    No data available
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        )}
      </div>
    </Accordion>
  );
};

export default MetaAnalyticsByMediaAssetAccordion;
