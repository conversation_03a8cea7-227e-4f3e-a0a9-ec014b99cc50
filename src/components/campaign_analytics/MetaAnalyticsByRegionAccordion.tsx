import {
  GROWEASY_CAMPAIGN_TYPE,
  ICampaignInsightDetails,
} from 'src/types/campaigns';
import Accordion, { IAccordionProps } from '../lib/Accordion';
import { getMetaLeadsCountFromInsightDetails } from 'src/utils';
import SpinnerLoader from '../lib/SpinnerLoader';

interface IMetaAnalyticsByRegionAccordionProps {
  insights: ICampaignInsightDetails[];
  campaignType: GROWEASY_CAMPAIGN_TYPE;
  accordionProps?: Partial<IAccordionProps>;
  loading?: boolean;
}

import React from 'react';
const MetaAnalyticsByRegionAccordion: React.FC<
  IMetaAnalyticsByRegionAccordionProps
> = ({ insights, campaignType, accordionProps, loading }) => {
  return (
    <Accordion title="Performance by Region" {...accordionProps}>
      <div className="my-4">
        {loading ? (
          <div className="flex justify-center">
            <SpinnerLoader size={24} borderWidth={3} />
          </div>
        ) : (
          <table className="w-full border border-gray-300">
            <thead>
              <tr className="bg-gray-100 text-left text-xs font-medium">
                <th className="px-4 py-2 border border-gray-300">Region</th>
                <th className="px-4 py-2 border border-gray-300">Views</th>
                <th className="px-4 py-2 border border-gray-300">Clicks</th>
                <th className="px-4 py-2 border border-gray-300">
                  {[GROWEASY_CAMPAIGN_TYPE.META_SALES].includes(campaignType)
                    ? 'Conversions'
                    : 'Leads'}
                </th>
              </tr>
            </thead>
            <tbody>
              {insights.length > 0 ? (
                insights.map((item, index) => {
                  const leadsCount = getMetaLeadsCountFromInsightDetails(
                    item,
                    campaignType,
                  );
                  return (
                    <tr key={index} className="text-xxs">
                      <td className="px-4 py-2 border border-gray-300">
                        {item.region ?? 'N/A'}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {item.impressions ?? '0'}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {item.clicks ?? '0'}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {leadsCount}
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td
                    colSpan={4}
                    className="px-4 py-4 text-center text-gray-500"
                  >
                    No data available
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        )}
      </div>
    </Accordion>
  );
};

export default MetaAnalyticsByRegionAccordion;
