import {
  GROWEASY_CAMPAIGN_TYPE,
  ICampaignInsightDetails,
} from 'src/types/campaigns';
import Accordion, { IAccordionProps } from '../lib/Accordion';
import { getMetaLeadsCountFromInsightDetails } from 'src/utils';
import SpinnerLoader from '../lib/SpinnerLoader';
//import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';

interface IMetaAnalyticsByAgeAndGenderAccordionProps {
  className?: string;
  insights: ICampaignInsightDetails[];
  campaignType: GROWEASY_CAMPAIGN_TYPE;
  accordionProps?: Partial<IAccordionProps>;
  loading?: boolean;
}

/* Commented out chart-related code
const processData = (insights: ICampaignInsightDetails[], key: string, label: 'Views' | 'Clicks' | 'Leads') => {
  // Sort by the key (Views, Clicks, or Leads) in descending order
  const sorted = [...insights].sort((a, b) => (b[key] ?? 0) - (a[key] ?? 0));

  // Take top 5 and aggregate the rest into "Others"
  const top5 = sorted.slice(0, 5);
  const othersTotal = sorted.slice(5).reduce((acc, item) => acc + (parseInt(item[key]) ?? 0), 0);

  const processedData = top5.map(item => ({
    category: `${item.age}, ${item.gender}`,
    value: item[key] ?? 0,
    [label]: item[key] ?? 0
  }));

  if (othersTotal > 0) {
    processedData.push({ category: "Others", value: othersTotal, [label]: othersTotal });
  }

  return processedData;
};
*/

const MetaAnalyticsByAgeAndGenderAccordion: React.FC<
  IMetaAnalyticsByAgeAndGenderAccordionProps
> = ({ insights, campaignType, className = '', accordionProps, loading }) => {
  /*const viewsData = processData(insights, 'impressions', 'Views');
  const clicksData = processData(insights, 'clicks', 'Clicks');
  const leadsData = processData(insights.map(item => ({
    ...item,
    leads: getMetaLeadsCountFromInsightDetails(item)
  })), 'leads', 'Leads');

  const renderChart = (data: {
    category: string;
    value: number;
  }[], params: {
    title: string,
    color: string,
    label: 'Views' | 'Clicks' | 'Leads'
  }) => {
    const { title, color, label } = params;
    return <div className="mb-6 mr-4">
      <h2 className="text-xs text-gray-dark font-medium my-2">{title}</h2>
      <ResponsiveContainer width="100%" height={250}>
        <BarChart data={data} layout="vertical" margin={{ left: 12 }}>
          <XAxis type="number" tick={{ fontSize: "12px" }} />
          <YAxis dataKey="category" type="category" tick={{ fontSize: "10px" }} />
          <Tooltip cursor={{ fill: 'transparent' }} />
          <Bar dataKey={label} fill={color} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  };*/

  return (
    <Accordion
      title="Performance by Age group & Gender"
      className={className}
      {...accordionProps}
    >
      <div className="my-4">
        {loading ? (
          <div className="flex justify-center">
            <SpinnerLoader size={24} borderWidth={3} />
          </div>
        ) : (
          <>
            {/* <div className="mt-6">
              {renderChart(viewsData, {
                title: 'Views', color: '#8884d8', label: 'Views'
              })}
              {renderChart(clicksData, {
                title: 'Clicks', color: '#82ca9d', label: 'Clicks'
              })}
              {renderChart(leadsData, {
                title: 'Leads', color: '#ff7300', label: 'Leads'
              })}
            </div> */}
            <table className="w-full border border-gray-300">
              <thead>
                <tr className="bg-gray-100 text-left text-xs font-medium">
                  <th className="px-4 py-2 border border-gray-300">
                    Age group & Gender
                  </th>
                  <th className="px-4 py-2 border border-gray-300">Views</th>
                  <th className="px-4 py-2 border border-gray-300">Clicks</th>
                  <th className="px-4 py-2 border border-gray-300">
                    {[GROWEASY_CAMPAIGN_TYPE.META_SALES].includes(campaignType)
                      ? 'Conversions'
                      : 'Leads'}
                  </th>
                </tr>
              </thead>
              <tbody>
                {insights.length > 0 ? (
                  insights.map((item, index) => (
                    <tr key={index} className="text-xxs">
                      <td className="px-4 py-2 border border-gray-300 capitalize">
                        {item.age}, {item.gender}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {item.impressions ?? '0'}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {item.clicks ?? '0'}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {getMetaLeadsCountFromInsightDetails(
                          item,
                          campaignType,
                        )}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={4}
                      className="px-4 py-4 text-center text-gray-500"
                    >
                      No data available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </>
        )}
      </div>
    </Accordion>
  );
};

export default MetaAnalyticsByAgeAndGenderAccordion;
