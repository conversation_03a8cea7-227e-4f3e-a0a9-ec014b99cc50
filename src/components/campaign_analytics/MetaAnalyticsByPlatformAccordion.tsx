import {
  GROWEASY_CAMPAIGN_TYPE,
  ICampaignInsightDetails,
} from 'src/types/campaigns';
import Accordion, { IAccordionProps } from '../lib/Accordion';
import { getMetaLeadsCountFromInsightDetails } from 'src/utils';
import SpinnerLoader from '../lib/SpinnerLoader';

interface IMetaAnalyticsByPlatformAccordionProps {
  insights: ICampaignInsightDetails[];
  campaignType: GROWEASY_CAMPAIGN_TYPE;
  className?: string;
  accordionProps?: Partial<IAccordionProps>;
  loading?: boolean;
}

const MetaAnalyticsByPlatformAccordion: React.FC<
  IMetaAnalyticsByPlatformAccordionProps
> = ({ insights, campaignType, className = '', accordionProps, loading }) => {
  return (
    <Accordion
      title="Performance by Platform"
      className={className}
      {...accordionProps}
    >
      <div className="my-4">
        {loading ? (
          <div className="flex justify-center">
            <SpinnerLoader size={24} borderWidth={3} />
          </div>
        ) : (
          <table className="w-full border border-gray-300">
            <thead>
              <tr className="bg-gray-100 text-left text-xs font-medium">
                <th className="px-4 py-2 border border-gray-300">Platform</th>
                <th className="px-4 py-2 border border-gray-300">Views</th>
                <th className="px-4 py-2 border border-gray-300">Clicks</th>
                <th className="px-4 py-2 border border-gray-300">
                  {[GROWEASY_CAMPAIGN_TYPE.META_SALES].includes(campaignType)
                    ? 'Conversions'
                    : 'Leads'}
                </th>
              </tr>
            </thead>
            <tbody>
              {insights.length > 0 ? (
                insights.map((item, index) => {
                  const leadsCount = getMetaLeadsCountFromInsightDetails(
                    item,
                    campaignType,
                  );
                  return (
                    <tr key={index} className="text-xxs">
                      <td className="px-4 py-2 border border-gray-300">
                        {item.publisher_platform ?? 'N/A'}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {item.impressions ?? '0'}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {item.clicks ?? '0'}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {leadsCount}
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td
                    colSpan={4}
                    className="px-4 py-4 text-center text-gray-500"
                  >
                    No data available
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        )}
      </div>
    </Accordion>
  );
};

export default MetaAnalyticsByPlatformAccordion;
