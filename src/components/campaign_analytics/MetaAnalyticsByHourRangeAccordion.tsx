import {
  GROWEASY_CAMPAIGN_TYPE,
  ICampaignInsightDetails,
} from 'src/types/campaigns';
import Accordion, { IAccordionProps } from '../lib/Accordion';
import { getMetaLeadsCountFromInsightDetails } from 'src/utils';
import SpinnerLoader from '../lib/SpinnerLoader';

interface IMetaAnalyticsByHourRangeAccordionProps {
  className?: string;
  insights: ICampaignInsightDetails[];
  campaignType: GROWEASY_CAMPAIGN_TYPE;
  accordionProps?: Partial<IAccordionProps>;
  loading?: boolean;
}

const MetaAnalyticsByHourRangeAccordion: React.FC<
  IMetaAnalyticsByHourRangeAccordionProps
> = ({ insights, className = '', campaignType, accordionProps, loading }) => {
  const formatHourRange = (hourRange: string): string => {
    if (!hourRange) return 'N/A';

    // Extract the hour from the format "00:00-00:59"
    const hour = parseInt(hourRange.split(':')[0], 10);

    // Convert to 12-hour format with AM/PM
    const period = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12; // Convert 0 to 12 for 12 AM

    return `${hour12}:00 ${period} - ${hour12}:59 ${period}`;
  };

  return (
    <Accordion
      title="Performance by Hour Range"
      className={className}
      {...accordionProps}
    >
      <div className="my-4">
        {loading ? (
          <div className="flex justify-center">
            <SpinnerLoader size={24} borderWidth={3} />
          </div>
        ) : (
          <table className="w-full border border-gray-300">
            <thead>
              <tr className="bg-gray-100 text-left text-xs font-medium">
                <th className="px-4 py-2 border border-gray-300">Hour Range</th>
                <th className="px-4 py-2 border border-gray-300">Views</th>
                <th className="px-4 py-2 border border-gray-300">Clicks</th>
                <th className="px-4 py-2 border border-gray-300">
                  {[GROWEASY_CAMPAIGN_TYPE.META_SALES].includes(campaignType)
                    ? 'Conversions'
                    : 'Leads'}
                </th>
              </tr>
            </thead>
            <tbody>
              {insights.length > 0 ? (
                insights.map((item, index) => {
                  const leadsCount = getMetaLeadsCountFromInsightDetails(
                    item,
                    campaignType,
                  );
                  return (
                    <tr key={index} className="text-xxs">
                      <td className="px-4 py-2 border border-gray-300">
                        {formatHourRange(
                          item.hourly_stats_aggregated_by_audience_time_zone,
                        )}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {item.impressions ?? '0'}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {item.clicks ?? '0'}
                      </td>
                      <td className="px-4 py-2 border border-gray-300">
                        {leadsCount}
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td
                    colSpan={4}
                    className="px-4 py-4 text-center text-gray-500"
                  >
                    No data available
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        )}
      </div>
    </Accordion>
  );
};

export default MetaAnalyticsByHourRangeAccordion;
