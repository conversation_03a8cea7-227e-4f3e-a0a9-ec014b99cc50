import {
  GROWEASY_CAMPAIGN_TYPE,
  GrowEasyCampaignStatus,
  ICampaign,
} from 'src/types/campaigns';
import { getFormattedDateString } from 'src/utils';
import { useRouter } from 'next/router';
import { QueryParams } from 'src/constants';
import CampaignStatusComp from './CampaignStatusComp';
import CampaignProgressBarComp from './CampaignProgressBarComp';
import { AdPlatforms } from 'src/types';
import FacebookIcon from '@/images/common/facebook.svg';
import InstagramIcon from '@/images/common/instagram.svg';
import GoogleIcon from '@/images/common/google.svg';
import YoutubeIcon from '@/images/common/youtube.svg';
import WhatsAppIcon from '@/images/common/whatsapp.svg';
import CartIcon from '@/images/common/cart.svg';
import { IoCall } from 'react-icons/io5';

interface ICampaignListItemProps {
  data: ICampaign;
  adminView?: boolean;
  className?: string;
}

const CampaignListItem = (props: ICampaignListItemProps) => {
  const { data, adminView, className = '' } = props;

  const router = useRouter();

  const onCampaignClick = () => {
    const adPlatform = data.platform ?? AdPlatforms.META;
    void router.push({
      pathname:
        data.status === GrowEasyCampaignStatus.DRAFT
          ? '/onboarding'
          : adPlatform === AdPlatforms.GOOGLE
            ? '/campaign-details/google'
            : '/campaign-details',
      query: {
        [QueryParams.CAMPAIGN_ID]: data.id,
        [QueryParams.PLATFORM]: adPlatform,
        [QueryParams.TYPE]: data.type ?? GROWEASY_CAMPAIGN_TYPE.LEAD_FORM,
      },
    });
  };

  const campaignName = adminView
    ? data.name
    : `${
        data.friendly_name ?? data.details?.business_details?.business_category
      }`;
  const campaignCreationOrStartDate = new Date(
    data.details?.budget_and_scheduling?.start_time ??
      data.created_at._seconds * 1000,
  );
  const productOrServiceDesc =
    data.details?.business_details?.product_or_service_description ?? '';
  const productOrServiceDescCharsLimit = 100;

  return (
    <div
      className={`bg-white cursor-pointer ${className}`}
      onClick={onCampaignClick}
    >
      <div className="flex items-center">
        <p className="text-sm text-primary font-semibold overflow-hidden text-ellipsis mr-3">
          {campaignName}
        </p>
        <div className="flex-1" />
        <CampaignStatusComp campaignDetails={data} />
      </div>
      <p className="text-xxs mt-1 line-clamp-2">
        {productOrServiceDesc.length > productOrServiceDescCharsLimit
          ? `${productOrServiceDesc.substring(
              0,
              productOrServiceDescCharsLimit,
            )}...`
          : productOrServiceDesc}
      </p>
      <p className="text-xs text-gray-dark font-medium mt-3">
        {getFormattedDateString(campaignCreationOrStartDate)}
      </p>
      <CampaignProgressBarComp campaignDetails={data} className="mt-3" />
      {data.platform === AdPlatforms.GOOGLE ? (
        <div className="flex items-center gap-x-2 mt-5">
          {data.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH ? (
            <GoogleIcon className="w-4 h-4" />
          ) : data.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX ? (
            <>
              <GoogleIcon className="w-4 h-4" />
              <YoutubeIcon className="w-4 h-4" />
            </>
          ) : (
            <>
              <GoogleIcon className="w-4 h-4" />
              <IoCall className="w-4 h-4 text-hyperlink" />
            </>
          )}
        </div>
      ) : null}
      {data.platform === AdPlatforms.META ? (
        <div className="flex items-center gap-x-2 mt-5">
          {data.type === GROWEASY_CAMPAIGN_TYPE.CTWA ? (
            <WhatsAppIcon className="w-4 h-4" />
          ) : (
            <>
              <FacebookIcon className="w-4 h-4" />
              <InstagramIcon className="w-4 h-4" />
              {data.type === GROWEASY_CAMPAIGN_TYPE.META_SALES ? (
                <CartIcon className="w-4 h-4 text-primary" />
              ) : null}
            </>
          )}
        </div>
      ) : null}
    </div>
  );
};

export default CampaignListItem;
