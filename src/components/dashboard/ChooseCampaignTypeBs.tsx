import BottomSheet from '../lib/BottomSheet';
import CrossIcon from '@/images/common/cross.svg';
import PeopleIcon from '@/images/common/people.svg';
import SearchIcon from '@/images/common/search.svg';
//import GraphIcon from '@/images/common/graph.svg';
import CartIcon from '@/images/common/cart.svg';
import ChatIcon from '@/images/common/chat.svg';
import FacebookIcon from '@/images/common/fbgreen.svg';
import InstagramIcon from '@/images/common/instagramgreen.svg';
import GoogleIcon from '@/images/common/googlegreen.svg';
//import YoutubeIcon from '@/images/common/youtubegreen.svg';
import WhatsAppIcon from '@/images/common/whatsappgreen.svg';
import ChevronRightIcon from '@/images/common/chevron-right.svg';
import {
  AdPlatforms,
  GrowEasyPartners,
  IGroweasyUser,
  IPartnerConfig,
} from 'src/types';
import { GROWEASY_CAMPAIGN_TYPE } from 'src/types/campaigns';
import { useRouter } from 'next/router';
import { QueryParams } from 'src/constants';
import { ElementType, useState } from 'react';
import { MdCall } from 'react-icons/md';

interface IChooseCampaignTypeBsProps {
  onClose: () => void;
  partnerConfig?: IPartnerConfig;
  user?: IGroweasyUser;
}
const CAMPAIGN_DATA = [
  {
    title: 'Generate Leads on Social Media',
    description:
      'Reach your audience with engaging ads and capture leads effortlessly',
    LeftIcon: PeopleIcon as ElementType,
    rightIcons: [FacebookIcon, InstagramIcon],
    platform: AdPlatforms.META,
    type: GROWEASY_CAMPAIGN_TYPE.LEAD_FORM,
    goal: 'leads',
    bgColor: 'bg-gradient-to-br from-blue-400 to-blue-600',
    iconColor: 'text-white',
    borderColor: 'border-blue-200',
  },
  {
    title: 'Get Inquiries on WhatsApp',
    description:
      'Connect with customers instantly for inquiries, support, and sales.',
    LeftIcon: ChatIcon as ElementType,
    rightIcons: [WhatsAppIcon],
    platform: AdPlatforms.META,
    type: GROWEASY_CAMPAIGN_TYPE.CTWA,
    goal: 'leads',
    disclaimer:
      'You must have a Facebook page linked to your WhatsApp business number.',
    bgColor: 'bg-gradient-to-br from-green-400 to-green-600',
    iconColor: 'text-white',
    borderColor: 'border-green-200',
  },
  {
    title: 'Get Calls from potential customers searching on Google',
    description:
      'Drive more revenue for your business with Get Calls via Google Search campaign',
    LeftIcon: MdCall as ElementType,
    rightIcons: [GoogleIcon],
    platform: AdPlatforms.GOOGLE,
    type: GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
    goal: 'leads',
    bgColor: 'bg-gradient-to-br from-rose-500 to-red',
    iconColor: 'text-white',
    borderColor: 'border-rose-200',
  },
  {
    title: 'Drive Sales on Website with Meta ads',
    description:
      'Optimize your campaigns for purchases— ideal for maximizing D2C conversions.',
    LeftIcon: CartIcon as ElementType,
    rightIcons: [FacebookIcon, InstagramIcon],
    platform: AdPlatforms.META,
    type: GROWEASY_CAMPAIGN_TYPE.META_SALES,
    goal: 'sales',
    bgColor: 'bg-gradient-to-br from-purple-400 to-purple-600',
    iconColor: 'text-white',
    borderColor: 'border-purple-200',
  },
  {
    title: 'Dominate Search with Google Ads',
    description:
      'Show up when prospects search for your offerings with highly relevant text ads.',
    LeftIcon: SearchIcon as ElementType,
    rightIcons: [GoogleIcon],
    platform: AdPlatforms.GOOGLE,
    type: GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH,
    goal: 'sales',
    bgColor: 'bg-gradient-to-br from-yellow-400 to-yellow-600',
    iconColor: 'text-white',
    borderColor: 'border-yellow-200',
  },
  /*{
    title: 'Maximize Sales with Google Performance Max',
    description:
      'Drive more sales for your D2C brand with AI-powered, multi-channel Google campaigns',
    LeftIcon: GraphIcon as ElementType,
    rightIcons: [GoogleIcon, YoutubeIcon],
    platform: AdPlatforms.GOOGLE,
    type: GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
    goal: 'sales',
    bgColor: 'bg-gradient-to-br from-rose-500 to-red',
    iconColor: 'text-white',
    borderColor: 'border-rose-200',
  },*/
];

const ChooseCampaignTypeBs = (props: IChooseCampaignTypeBsProps) => {
  const { onClose, partnerConfig } = props;
  const [activeTab, setActiveTab] = useState<'leads' | 'sales'>('leads');
  const router = useRouter();

  const onCampaignTypeClick = (platform: string, type: string) => {
    void router.push({
      pathname: '/onboarding',
      query: {
        [QueryParams.PLATFORM]: platform,
        [QueryParams.TYPE]: type,
      },
    });
  };

  const filteredCampaigns = CAMPAIGN_DATA.filter(
    (campaign) => campaign.goal === activeTab,
  );

  return (
    <BottomSheet
      className="h-5/6 rounded-t-2xl"
      contentClassName="h-full flex flex-col flex-1"
      onClose={onClose}
    >
      <div className="flex flex-col flex-1 overflow-hidden">
        <div className="flex items-center px-2 md:px-5 pb-4 border-b">
          <h1 className="text-black font-poppins font-semibold text-xl sm:text-2xl tracking-tight">
            What&apos;s your marketing goal?
          </h1>
          <div className="flex-1" />
          <button
            type="button"
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center hover:bg-gray-200 rounded-full transition-colors"
            aria-label="Close"
          >
            <CrossIcon
              width="16"
              height="16"
              className="text-gray-600 w-3 h-3 md:w-4 md:h-4"
            />
          </button>
        </div>

        <div className="flex mx-2 md:mx-5 mt-4 p-0.5 bg-gray-100 rounded-xl overflow-hidden">
          <button
            type="button"
            className={`flex-1 rounded-xl py-4 px-2 sm:px-4 cursor-pointer transition-all duration-200 text-center font-medium text-sm sm:text-base ${
              activeTab === 'leads'
                ? 'bg-white border border-gray-200'
                : 'bg-gray-100 hover:bg-gray-50'
            }`}
            onClick={() => setActiveTab('leads')}
          >
            Leads & Enquiries
          </button>
          <button
            type="button"
            className={`flex-1 rounded-xl py-4 px-2 sm:px-4 cursor-pointer transition-all duration-200 text-center font-medium text-sm sm:text-base ${
              activeTab === 'sales'
                ? 'bg-white border border-gray-200'
                : 'bg-gray-100 hover:bg-gray-50'
            }`}
            onClick={() => setActiveTab('sales')}
          >
            Sales & Conversions
          </button>
        </div>

        {/* Campaign Options */}
        <div className="flex-1 flex-col overflow-y-scroll no-scrollbar px-2 md:px-5 py-4">
          {filteredCampaigns.length === 0 ? (
            <div className="text-center py-10 text-gray-500">
              No options available for this category
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {filteredCampaigns.map((item, index) => {
                // disable CTWA for Genius Ads
                if (item.type === GROWEASY_CAMPAIGN_TYPE.CTWA) {
                  if (
                    [GrowEasyPartners.GENIUS_ADS].includes(
                      partnerConfig?.partner,
                    )
                  ) {
                    return null;
                  }
                }

                return (
                  <div
                    key={index}
                    className="rounded-xl shadow hover:shadow-md px-3 py-4 sm:px-4 md:py-6 border border-green-900/20 hover:border-green-900/40 transition-all duration-200 cursor-pointer"
                    onClick={() =>
                      onCampaignTypeClick(item.platform, item.type)
                    }
                  >
                    <div className="flex items-center">
                      <div
                        className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex justify-center items-center bg-green-50/50 border mr-3 sm:mr-4`}
                      >
                        <item.LeftIcon className="w-4 h-4 sm:w-5 sm:h-5 text-green-900" />
                      </div>

                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <p className="text-teal-900 font-semibold text-sm md:leading-snug leading-snug sm:text-lg">
                            {item.title}
                          </p>
                          <div className="flex items-center ml-2 mt-0.5">
                            {item.rightIcons.map((RightIcon, iconIdx) => (
                              <div key={iconIdx} className="ml-1.5">
                                <RightIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                              </div>
                            ))}
                          </div>
                        </div>

                        <p className="text-xs sm:text-sm leading-snug text-gray-600 mt-1 pr-2 sm:pr-4">
                          {item.description}
                        </p>

                        {item.disclaimer && (
                          <div className="mt-2 bg-gray-200/50 p-2.5 rounded-xl inline-flex items-start justify-start gap-1 text-[10px] sm:text-xs tracking-wide leading-tight sm:leading-tight text-primary/80">
                            <span className="mt-0.5">ⓘ </span>
                            <p>{item.disclaimer}</p>
                          </div>
                        )}
                      </div>

                      <div className="ml-2">
                        <ChevronRightIcon className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </BottomSheet>
  );
};

export default ChooseCampaignTypeBs;
