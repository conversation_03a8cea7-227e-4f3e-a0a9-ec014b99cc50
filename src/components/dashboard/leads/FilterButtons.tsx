import { useState } from 'react';
import CrossIcon from '@/images/common/cross.svg';
import { ILeadsStatus, LeadStatusNameMapping } from 'src/types/leads_crm';
import { ICampaign } from 'src/types/campaigns';
import BottomSheet from '@/components/lib/BottomSheet';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { FaCalendarAlt } from 'react-icons/fa';
import { BiChevronRight } from 'react-icons/bi';

interface IFilterButtonsProps {
  campaigns: ICampaign[];
  onFiltersChange: (filters: ILeadFilters) => void;
}

export interface ILeadFilters {
  status?: ILeadsStatus;
  dateRange?: {
    start: string;
    end: string;
  };
  quality?: string;
  campaign?: string;
}

type FilterTab = 'status' | 'date' | 'quality' | 'campaign';

interface IFilterChip {
  key: string;
  label: string;
  onRemove: () => void;
}

const FilterButtons = (props: IFilterButtonsProps) => {
  const { campaigns, onFiltersChange } = props;

  const [activeFilters, setActiveFilters] = useState<ILeadFilters>({});
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<FilterTab>('status');
  const [tempFilters, setTempFilters] = useState<ILeadFilters>({});
  const [customDateRange, setCustomDateRange] = useState<{
    start: Date | null;
    end: Date | null;
  }>({ start: null, end: null });
  const [selectedDateOption, setSelectedDateOption] = useState<string>('');

  const statusOptions = [
    {
      value: ILeadsStatus.GOOD_LEAD_FOLLOW_UP,
      label: LeadStatusNameMapping[ILeadsStatus.GOOD_LEAD_FOLLOW_UP],
    },
    {
      value: ILeadsStatus.DID_NOT_CONNECT_OR_BUSY,
      label: LeadStatusNameMapping[ILeadsStatus.DID_NOT_CONNECT_OR_BUSY],
    },
    {
      value: ILeadsStatus.BAD_LEAD,
      label: LeadStatusNameMapping[ILeadsStatus.BAD_LEAD],
    },
    {
      value: ILeadsStatus.SALE_DONE,
      label: LeadStatusNameMapping[ILeadsStatus.SALE_DONE],
    },
  ];

  const qualityOptions = [
    { value: 'hot', label: 'Hot' },
    { value: 'warm', label: 'Warm' },
    { value: 'cold', label: 'Cold' },
  ];

  const dateOptions = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'last_7_days', label: 'Last 7 days' },
    { value: 'last_30_days', label: 'Last 30 days' },
    { value: 'custom', label: 'Custom range' },
  ];

  const openDrawer = () => {
    setTempFilters({ ...activeFilters });

    // Set the selected date option based on current active filters
    if (activeFilters.dateRange) {
      const existingOption = dateOptions.find(
        (opt) => opt.value === activeFilters.dateRange?.start,
      );
      if (existingOption) {
        setSelectedDateOption(existingOption.value);
      } else {
        // It's a custom date range
        setSelectedDateOption('custom');
        // Try to parse the dates back to Date objects for the date picker
        if (activeFilters.dateRange.start && activeFilters.dateRange.end) {
          setCustomDateRange({
            start: new Date(activeFilters.dateRange.start),
            end: new Date(activeFilters.dateRange.end),
          });
        }
      }
    }

    setIsDrawerOpen(true);
  };

  const closeDrawer = () => {
    setIsDrawerOpen(false);
    setActiveTab('status');
    setSelectedDateOption('');
    setCustomDateRange({ start: null, end: null });
  };

  const applyFilters = () => {
    setActiveFilters({ ...tempFilters });
    onFiltersChange(tempFilters);
    closeDrawer();
  };

  const handleTempFilterSelect = (filterType: string, value: string) => {
    const newFilters = { ...tempFilters };

    if (filterType === 'status') {
      newFilters.status = value as ILeadsStatus;
    } else if (filterType === 'quality') {
      newFilters.quality = value;
    } else if (filterType === 'campaign') {
      newFilters.campaign = value;
    } else if (filterType === 'date') {
      if (value === 'custom') {
        setSelectedDateOption('custom');
        // Don't set dateRange yet, wait for custom dates to be selected
      } else {
        setSelectedDateOption(value);
        newFilters.dateRange = { start: value, end: value };
        setCustomDateRange({ start: null, end: null });
      }
    }

    setTempFilters(newFilters);
  };

  const handleCustomDateChange = () => {
    if (customDateRange.start && customDateRange.end) {
      const newFilters = { ...tempFilters };
      newFilters.dateRange = {
        start: customDateRange.start.toLocaleDateString('en-CA'),
        end: customDateRange.end.toLocaleDateString('en-CA'),
      };
      setTempFilters(newFilters);
    }
  };

  const removeFilter = (filterType: string) => {
    const newFilters = { ...activeFilters };

    if (filterType === 'status') {
      delete newFilters.status;
    } else if (filterType === 'quality') {
      delete newFilters.quality;
    } else if (filterType === 'campaign') {
      delete newFilters.campaign;
    } else if (filterType === 'date') {
      delete newFilters.dateRange;
      setSelectedDateOption('');
      setCustomDateRange({ start: null, end: null });
    }

    setActiveFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'status':
        return (
          <div className="space-y-3">
            {statusOptions.map((option) => (
              <label
                key={option.value}
                className="flex items-center space-x-3 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={tempFilters.status === option.value}
                  onChange={() =>
                    handleTempFilterSelect(
                      'status',
                      tempFilters.status === option.value ? '' : option.value,
                    )
                  }
                  className="w-4 h-4 text-primary2 border-gray-300 rounded focus:ring-primary2"
                />
                <span className="text-sm">{option.label}</span>
              </label>
            ))}
          </div>
        );

      case 'date':
        return (
          <div className="space-y-4">
            {dateOptions.map((option) => (
              <label
                key={option.value}
                className="flex items-center space-x-3 cursor-pointer"
              >
                <input
                  type="radio"
                  name="dateOption"
                  checked={selectedDateOption === option.value}
                  onChange={() => handleTempFilterSelect('date', option.value)}
                  className="w-4 h-4 text-primary2 border-gray-300 focus:ring-primary2"
                />
                <span className="text-sm">{option.label}</span>
              </label>
            ))}

            {selectedDateOption === 'custom' && (
              <div className="mt-4 space-y-3 pl-7">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Start Date
                  </label>
                  <DatePicker
                    selected={customDateRange.start}
                    onChange={(date: Date | null) => {
                      setCustomDateRange((prev) => ({ ...prev, start: date }));
                      if (date && customDateRange.end) {
                        setTimeout(handleCustomDateChange, 0);
                      }
                    }}
                    selectsStart
                    startDate={customDateRange.start}
                    endDate={customDateRange.end}
                    placeholderText="Select start date"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary2"
                    showIcon
                    icon={<FaCalendarAlt />}
                    calendarIconClassName="!w-3 !h-3"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    End Date
                  </label>
                  <DatePicker
                    selected={customDateRange.end}
                    onChange={(date: Date | null) => {
                      setCustomDateRange((prev) => ({ ...prev, end: date }));
                      if (customDateRange.start && date) {
                        setTimeout(handleCustomDateChange, 0);
                      }
                    }}
                    selectsEnd
                    startDate={customDateRange.start}
                    endDate={customDateRange.end}
                    minDate={customDateRange.start}
                    placeholderText="Select end date"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-primary2"
                    showIcon
                    icon={<FaCalendarAlt />}
                    calendarIconClassName="!w-3 !h-3"
                  />
                </div>
              </div>
            )}
          </div>
        );

      case 'quality':
        return (
          <div className="space-y-3">
            {qualityOptions.map((option) => (
              <label
                key={option.value}
                className="flex items-center space-x-3 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={tempFilters.quality === option.value}
                  onChange={() =>
                    handleTempFilterSelect(
                      'quality',
                      tempFilters.quality === option.value ? '' : option.value,
                    )
                  }
                  className="w-4 h-4 text-primary2 border-gray-300 rounded focus:ring-primary2"
                />
                <span className="text-sm">{option.label}</span>
              </label>
            ))}
          </div>
        );

      case 'campaign':
        return (
          <div className="space-y-3">
            {campaignOptions.map((option) => (
              <label
                key={option.value}
                className="flex items-center space-x-3 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={tempFilters.campaign === option.value}
                  onChange={() =>
                    handleTempFilterSelect(
                      'campaign',
                      tempFilters.campaign === option.value ? '' : option.value,
                    )
                  }
                  className="w-4 h-4 text-primary2 border-gray-300 rounded focus:ring-primary2"
                />
                <span className="text-sm">{option.label}</span>
              </label>
            ))}
          </div>
        );

      default:
        return null;
    }
  };

  const getActiveFilterChips = (): IFilterChip[] => {
    const chips: IFilterChip[] = [];

    if (activeFilters.status) {
      const statusLabel = statusOptions.find(
        (opt) => opt.value === activeFilters.status,
      )?.label;
      chips.push({
        key: 'status',
        label: `Status: ${statusLabel}`,
        onRemove: () => removeFilter('status'),
      });
    }

    if (activeFilters.dateRange) {
      const dateLabel = dateOptions.find(
        (opt) => opt.value === activeFilters.dateRange?.start,
      )?.label;

      let displayLabel = `Date: ${dateLabel || 'Custom range'}`;

      // If it's a custom date range, show the actual dates
      if (
        !dateLabel &&
        activeFilters.dateRange.start &&
        activeFilters.dateRange.end
      ) {
        const startDate = new Date(activeFilters.dateRange.start);
        const endDate = new Date(activeFilters.dateRange.end);
        displayLabel = `Date: ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
      }

      chips.push({
        key: 'date',
        label: displayLabel,
        onRemove: () => removeFilter('date'),
      });
    }

    if (activeFilters.quality) {
      const qualityLabel = qualityOptions.find(
        (opt) => opt.value === activeFilters.quality,
      )?.label;
      chips.push({
        key: 'quality',
        label: `Quality: ${qualityLabel}`,
        onRemove: () => removeFilter('quality'),
      });
    }

    if (activeFilters.campaign) {
      const campaignLabel =
        campaigns.find((c) => c.id === activeFilters.campaign)?.friendly_name ||
        'Unknown';
      chips.push({
        key: 'campaign',
        label: `Campaign: ${campaignLabel}`,
        onRemove: () => removeFilter('campaign'),
      });
    }

    return chips;
  };

  const campaignOptions = campaigns.map((campaign) => ({
    value: campaign.id,
    label: campaign.friendly_name || campaign.name || 'Unnamed Campaign',
  }));

  const tabs = [
    { id: 'status' as FilterTab, label: 'Status' },
    { id: 'date' as FilterTab, label: 'Date' },
    { id: 'quality' as FilterTab, label: 'Quality' },
    { id: 'campaign' as FilterTab, label: 'Campaign' },
  ];

  return (
    <div className="mb-4">
      {/* Filter Button */}
      <div className="flex gap-2 overflow-x-auto py-2 no-scrollbar">
        <button
          type="button"
          className="flex items-center px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-sm bg-white shadow"
          onClick={openDrawer}
        >
          <span>Status</span>
          <BiChevronRight className="ml-2 w-3 h-3 text-gray-500" />
        </button>
        <button
          type="button"
          className="flex items-center px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-sm bg-white shadow"
          onClick={openDrawer}
        >
          <span>Date</span>
          <BiChevronRight className="ml-2 w-3 h-3 text-gray-500" />
        </button>
        <button
          type="button"
          className="flex items-center px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-sm bg-white shadow"
          onClick={openDrawer}
        >
          <span>Quality</span>
          <BiChevronRight className="ml-2 w-3 h-3 text-gray-500" />
        </button>
        <button
          type="button"
          className="flex items-center px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-sm bg-white shadow"
          onClick={openDrawer}
        >
          <span>Campaign</span>
          <BiChevronRight className="ml-2 w-3 h-3 text-gray-500" />
        </button>
        <button
          type="button"
          className="flex items-center px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-sm bg-white shadow"
          onClick={openDrawer}
        >
          <span>Source</span>
          <BiChevronRight className="ml-2 w-3 h-3 text-gray-500" />
        </button>
      </div>

      {/* Active Filter Chips */}
      {getActiveFilterChips().length > 0 && (
        <div className="flex gap-2 flex-wrap mt-3">
          {getActiveFilterChips().map((chip) => (
            <div
              key={chip.key}
              className="flex items-center px-2 py-1.5 sm:px-4 bg-teal-700/15 border-primary2/40 border rounded-full text-xs"
            >
              <span>{chip.label}</span>
              <button
                type="button"
                className="ml-3 text-gray-500 hover:text-gray-700"
                onClick={chip.onRemove}
              >
                <CrossIcon className="size-2" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Bottom Drawer */}
      {isDrawerOpen && (
        <BottomSheet onClose={closeDrawer} className="h-4/5">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-900">Filter</h2>
              <button
                type="button"
                onClick={closeDrawer}
                className="text-gray-500 hover:text-gray-700"
              >
                <CrossIcon className="w-5 h-5" />
              </button>
            </div>

            {/* Tabs */}
            <div className="flex border-b border-gray-200 mb-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary2 text-primary2'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto min-h-80 no-scrollbar">
              {renderTabContent()}
            </div>

            {/* Apply Button */}
            <div className="mt-10 py-4 border-t border-gray-200">
              <button
                type="button"
                onClick={applyFilters}
                className="w-full bg-primary2 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary transition-colors"
              >
                Apply
              </button>
            </div>
          </div>
        </BottomSheet>
      )}
    </div>
  );
};

export default FilterButtons;
