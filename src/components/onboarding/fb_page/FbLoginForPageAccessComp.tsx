import Button from '@/components/lib/Button';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useMutation } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import { processFbLoginDetails } from 'src/actions/onboarding';
import {
  FB_LOGIN_TO_RUN_ADS_USING_USERS_PAGE_CONFIG_ID,
  QueryParams,
} from 'src/constants';
import { EVENT_NAMES } from 'src/constants/events';
import { logEvent } from 'src/modules/firebase';
import {
  IFbLoginStatusResult,
  IGroweasyUser,
  IProcessedFbLoginResponse,
} from 'src/types';
import { GROWEASY_CAMPAIGN_TYPE } from 'src/types/campaigns';
import { logApiErrorAndShowToastMessage, openUrlInNewTab } from 'src/utils';

interface IFbLoginForPageAccessCompProps {
  user?: IGroweasyUser;
  selectedFbPageId: string;
  onFbPageChange: (fbPageId: string) => void;
  campaignType: GROWEASY_CAMPAIGN_TYPE;
  assignedPagesAndUserDetailsFromDb?: IProcessedFbLoginResponse;
}

const FbLoginForPageAccessComp = (props: IFbLoginForPageAccessCompProps) => {
  const {
    user,
    selectedFbPageId,
    onFbPageChange,
    campaignType,
    assignedPagesAndUserDetailsFromDb,
  } = props;

  const [fbLoginStatus, setFbLoginStatus] =
    useState<IFbLoginStatusResult | null>(null);
  const [loggedInUserDetails, setLoggedInUserDetails] =
    useState<IProcessedFbLoginResponse | null>(null);

  const router = useRouter();

  const processLoginDetailsMutation = useMutation(processFbLoginDetails);

  const insideWebview = typeof window !== 'undefined' && !!window?.bridge;

  useEffect(() => {
    // FB login is blocked in Webview so handling that separately
    // app: if users has already assigned pages earlier, display that
    // web: Fb login SDK will do the same thing
    if (insideWebview) {
      if (assignedPagesAndUserDetailsFromDb) {
        setLoggedInUserDetails(assignedPagesAndUserDetailsFromDb);
      }
    } else {
      window?.FB.getLoginStatus(function (response) {
        setFbLoginStatus(response);
      });
    }
  }, [assignedPagesAndUserDetailsFromDb]);

  useEffect(() => {
    // only Web flow, not in app
    if (insideWebview) {
      return;
    }
    // connected: The person is logged into Facebook, and has logged into your webpage.
    // not_authorized: The person is logged into Facebook, but has not logged into your webpage.
    // unknown: The person is not logged into Facebook, so you don't know if they have logged into your webpage.
    // Or FB.logout() was called before, and therefore, it cannot connect to Facebook.
    if (fbLoginStatus?.status === 'connected') {
      // fetch User's Pages and render
      /*window.FB?.api('/me', function(response: {
        name: string
        id: string
      }) {
        setLoggedInUserDetails(response)
      });
      window.FB?.api('/me/assigned_pages', function (response) {
        console.log(response)
      });*/
      processLoginDetailsMutation
        .mutateAsync({
          headers: getCommonHeaders(user),
          queryParams: router.query as Record<string, string>,
          body: { result: fbLoginStatus },
        })
        .then((response) => {
          setLoggedInUserDetails(response.data);
        })
        .catch((error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'FbLoginForPageAccessComp.processLoginDetailsMutation',
          );
        });
    } else {
      setLoggedInUserDetails(null);
    }
  }, [fbLoginStatus]);

  const onFbLoginClick = () => {
    logEvent(EVENT_NAMES.fb_page_login_with_fb_clicked);
    window.FB?.login(
      function (response) {
        setFbLoginStatus(response);
      },
      {
        config_id: FB_LOGIN_TO_RUN_ADS_USING_USERS_PAGE_CONFIG_ID,
        // response_type & override_default_response_type for SUAT only
        //response_type: 'code', // must be set to 'code' for System User access token
        //override_default_response_type: true,
      },
    );
  };

  const onFbLogoutClick = () => {
    logEvent(EVENT_NAMES.fb_page_logout_clicked);
    window.FB?.logout(function (response) {
      // this does not get called in case of SUAT
      setFbLoginStatus(response);
    });
    // explicitly resetting login status since callback does not get called in SUAT case
    setFbLoginStatus(null);
  };

  const onFbConnectOrReconnectClick = () => {
    logEvent(EVENT_NAMES.fb_page_connect_or_reconnect_clicked);
    openUrlInNewTab(
      `https://groweasy.ai/campaign-onboarding-connect-facebook-pages?${QueryParams.TOKEN}=${user?.authToken}`,
    );
  };

  return (
    <div>
      {
        /*fbLoginStatus !== null && fbLoginStatus.status === 'connected'*/ loggedInUserDetails?.account_details ? (
          <div>
            <div className="flex items-center">
              <Image
                src="/images/common/facebook.svg"
                alt="Facebok"
                width="40"
                height="40"
                className="rounded-full"
              />
              <p className="text-sm ml-3">
                {loggedInUserDetails?.account_details?.name}
              </p>
            </div>
            {loggedInUserDetails?.assigned_pages?.map((item, index) => {
              const selected = item.id === selectedFbPageId;
              return (
                <div key={index} className="mt-6 px-3">
                  <div className="flex items-center">
                    <div
                      className="w-4 h-4 rounded-full border border-gray-light mr-3 flex items-center justify-center cursor-pointer"
                      onClick={() => onFbPageChange(item.id)}
                    >
                      {selected ? (
                        <div className="w-2 h-2 rounded-full bg-primary" />
                      ) : null}
                    </div>
                    <p className="text-sm font-medium mr-2">{item.name}</p>
                    <p
                      onClick={() =>
                        openUrlInNewTab(
                          `https://www.facebook.com/profile.php?id=${item.id}`,
                        )
                      }
                      className="text-sm text-hyperlink cursor-pointer"
                    >
                      ({item.id})
                    </p>
                  </div>
                </div>
              );
            })}
            {loggedInUserDetails?.assigned_pages?.length ? (
              <div className="mt-6">
                {campaignType === GROWEASY_CAMPAIGN_TYPE.LEAD_FORM ? (
                  <p className="text-xs">
                    Make sure that You have accepted Facebook T&Cs here:
                    <span
                      onClick={() =>
                        openUrlInNewTab(
                          'https://www.facebook.com/ads/leadgen/tos',
                        )
                      }
                      className="text-hyperlink ml-1 cursor-pointer"
                    >
                      https://www.facebook.com/ads/leadgen/tos
                    </span>
                  </p>
                ) : null}
                {campaignType === GROWEASY_CAMPAIGN_TYPE.CTWA ? (
                  <p className="text-xs">
                    To link your WhatsApp business account to Facebook Page{' '}
                    <span
                      onClick={() =>
                        openUrlInNewTab(
                          'https://faq.whatsapp.com/***************/?cms_platform=android',
                        )
                      }
                      className="text-hyperlink cursor-pointer"
                    >
                      Click here
                    </span>
                  </p>
                ) : null}
              </div>
            ) : (
              <div className="mt-6">
                <p className="text-sm font-medium">No Facebook Page found </p>
              </div>
            )}
            {insideWebview ? (
              <Button
                className="mt-3 !py-1 !px-3 !text-xs flex items-center"
                onClick={onFbConnectOrReconnectClick}
              >
                <div className="mr-1">
                  <Image
                    src="/images/common/facebook.svg"
                    width="20"
                    height="20"
                    alt="fb-logo"
                  />
                </div>
                <p>Reconnect with Facebook</p>
              </Button>
            ) : (
              <Button
                className="mt-12 !py-1 !px-3 !text-xs flex items-center"
                onClick={onFbLogoutClick}
              >
                Logout
              </Button>
            )}
          </div>
        ) : (
          <div>
            <p className="text-sm">
              Login to Facebook and connect your Page with us
            </p>
            {campaignType === GROWEASY_CAMPAIGN_TYPE.CTWA ? (
              <div>
                <p className="text-xs mt-1 text-gray-dark">
                  This page must be linked to your WhatsApp Business account.
                </p>
                <p
                  className="text-xs mt-3 text-hyperlink cursor-pointer"
                  onClick={() =>
                    openUrlInNewTab(
                      'https://faq.whatsapp.com/***************/?cms_platform=android',
                    )
                  }
                >
                  How to link?
                </p>
              </div>
            ) : null}
            <Button
              className="mt-3 !py-1 !px-3 !text-xs flex items-center"
              onClick={
                insideWebview ? onFbConnectOrReconnectClick : onFbLoginClick
              }
            >
              <div className="mr-1">
                <Image
                  src="/images/common/facebook.svg"
                  width="20"
                  height="20"
                  alt="fb-logo"
                />
              </div>
              <p>{insideWebview ? 'Connect' : 'Login'} with Facebook</p>
            </Button>
          </div>
        )
      }
      {processLoginDetailsMutation.isLoading ? <FullScreenLoader /> : null}
    </div>
  );
};

export default FbLoginForPageAccessComp;
