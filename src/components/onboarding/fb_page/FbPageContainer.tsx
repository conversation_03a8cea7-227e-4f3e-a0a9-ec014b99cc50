import Image from 'next/image';
import { useEffect, useState } from 'react';
import {
  AD_GLOBAL_AI_DEFAULT_PAGE_ID,
  GROWEASY_DEFAULT_PAGE_ID,
  OnboardingStepIds,
} from 'src/constants';
import { GrowEasyPartners, IGroweasyUser, IPartnerConfig } from 'src/types';
import { GROWEASY_CAMPAIGN_TYPE, ICampaign } from 'src/types/campaigns';
import FbLoginForPageAccessComp from './FbLoginForPageAccessComp';
import Button from '@/components/lib/Button';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import { useMutation, useQuery } from 'react-query';
import { getFbAssignedPages, grantFbPageAccess } from 'src/actions/onboarding';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import { getCommonHeaders } from 'src/actions';
import { useRouter } from 'next/router';
import FetchError from 'src/actions/FetchError';

interface IFbPageContainerProps {
  campaignDetails: ICampaign;
  saveCampaignDetails: (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => void;
  partnerConfig?: IPartnerConfig;
  user?: IGroweasyUser;
}

/**
 * CTWA: Do not show BCN (GrowEasy Business) option
 * Fetch existing assigned pages and render as radio options in case of webview (BE checks token expiry)
 * In case of Web, FB SDK will do the same thing
 * In case of App, Reconnect/"Login with Fb" CTA will take user in browser
 * There user will connect Fb with GrowEasy and data will be saved in Firestore
 * On returning back to this page, assigned pages should get fetched from Firestore
 */
const FbPageContainer = (props: IFbPageContainerProps) => {
  const { campaignDetails, saveCampaignDetails, partnerConfig, user } = props;

  const DEFAULT_PAGE_ID =
    partnerConfig?.partner === GrowEasyPartners.AD_GLOBAL_AI
      ? AD_GLOBAL_AI_DEFAULT_PAGE_ID
      : GROWEASY_DEFAULT_PAGE_ID;

  const [growEasyOrPartnerFbPageSelected, setGrowEasyOrPartnerFbPageSelected] =
    useState(true);
  const [selectedFbPageId, setSelectedFbPageId] = useState(
    campaignDetails?.details?.config?.fb_page_id ?? DEFAULT_PAGE_ID,
  );

  const router = useRouter();

  const grantFbPageAccessMutation = useMutation(grantFbPageAccess);

  const campaignTypeCtwa =
    campaignDetails?.type === GROWEASY_CAMPAIGN_TYPE.CTWA;
  const insideWebview = typeof window !== 'undefined' && !!window?.bridge;

  const fbAssignedPagesResponse = useQuery(
    'getFbAssignedPages',
    () => {
      return getFbAssignedPages({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      cacheTime: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'FbPageContainer.getFbAssignedPages',
        );
      },
    },
  );

  useEffect(() => {
    const fbPageId = campaignDetails?.details?.config?.fb_page_id;
    if (fbPageId) {
      setSelectedFbPageId(fbPageId);
      setGrowEasyOrPartnerFbPageSelected(fbPageId === DEFAULT_PAGE_ID);
    }
  }, [campaignDetails]);

  useEffect(() => {
    // App flow: call api again when user returns from browser (post granting pages access)
    // refetchOnWindowFocus not working in webview?
    if (!insideWebview) {
      return;
    }
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        void fbAssignedPagesResponse.refetch();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const onSaveDetailsClick = async () => {
    logEvent(EVENT_NAMES.fb_page_save_clicked);

    if (selectedFbPageId !== DEFAULT_PAGE_ID) {
      try {
        await grantFbPageAccessMutation.mutateAsync({
          headers: getCommonHeaders(user),
          queryParams: router.query as Record<string, string>,
          body: { fb_page_id: selectedFbPageId },
        });
      } catch (error: unknown) {
        logApiErrorAndShowToastMessage(
          error as Error | FetchError,
          'FbPageContainer.grantFbPageAccessMutation',
        );
        // do not proceed if Page assignment fails, let user select BCN page
        return;
      }
    }
    saveCampaignDetails(
      {
        ...campaignDetails,
        details: {
          ...campaignDetails.details,
          config: {
            ...campaignDetails.details?.config,
            fb_page_id: selectedFbPageId,
          },
        },
      },
      OnboardingStepIds.FACEBOOK_PAGE,
    );
  };

  const fbPageOptions = [
    {
      label:
        partnerConfig?.partner === GrowEasyPartners.AD_GLOBAL_AI
          ? 'Ad Global AI (Recommended)'
          : 'GrowEasy Business (Recommended)',
      isGrowEasyOrPartnerPage: true,
    },
    {
      label: 'Your own Facebook Page',
      isGrowEasyOrPartnerPage: false,
    },
  ];

  return (
    <div className="px-4 pt-4 pb-8 flex flex-col flex-1 h-full">
      {campaignTypeCtwa ? null : (
        <div>
          <p className="text-sm">Run Ads using</p>
          {fbPageOptions.map((item, index) => {
            const selected = growEasyOrPartnerFbPageSelected
              ? item.isGrowEasyOrPartnerPage
              : !item.isGrowEasyOrPartnerPage;

            return (
              <div
                key={index}
                className="bg-white mt-3 rounded-lg px-3 cursor-pointer first:mt-0"
                onClick={() => {
                  setGrowEasyOrPartnerFbPageSelected(
                    item.isGrowEasyOrPartnerPage,
                  );
                  setSelectedFbPageId(
                    item.isGrowEasyOrPartnerPage ? DEFAULT_PAGE_ID : '',
                  );
                }}
              >
                <div className="flex items-center">
                  <div className="w-4 h-4 rounded-full border border-gray-light mr-3 flex items-center justify-center">
                    {selected ? (
                      <div className="w-2 h-2 rounded-full bg-primary" />
                    ) : null}
                  </div>
                  <div>
                    <p className="text-xs">{item.label}</p>
                  </div>
                </div>
              </div>
            );
          })}
          <hr className="my-8" />
        </div>
      )}
      <div className="flex flex-col overflow-y-scroll no-scrollbar flex-1">
        {growEasyOrPartnerFbPageSelected && !campaignTypeCtwa ? (
          <div>
            <div className="flex items-center">
              {partnerConfig?.partner === GrowEasyPartners.AD_GLOBAL_AI ? (
                <div className="w-40">
                  <Image
                    src={partnerConfig?.logoImage}
                    alt={partnerConfig?.name}
                    width="976"
                    height="156"
                  />
                </div>
              ) : (
                <Image
                  src="/images/groweasy-logo-square.png"
                  alt="GrowEasy"
                  width="40"
                  height="40"
                  className="rounded-full"
                />
              )}
              <p className="text-sm ml-3">
                {partnerConfig?.partner === GrowEasyPartners.AD_GLOBAL_AI
                  ? 'Ad Global AI'
                  : 'GrowEasy Business'}
              </p>
            </div>
            <p className="mt-5 text-xs">
              This is a generic Page managed by{' '}
              {partnerConfig?.name ?? 'GrowEasy'} to run Lead Ads on Facebook
              and Instagram.
            </p>
            <p className="mt-1 text-xs">
              This is an optimised Page and no action is required from your
              side.
            </p>
          </div>
        ) : (
          <FbLoginForPageAccessComp
            user={user}
            selectedFbPageId={selectedFbPageId}
            onFbPageChange={(fbPageId) => setSelectedFbPageId(fbPageId)}
            campaignType={campaignDetails?.type}
            assignedPagesAndUserDetailsFromDb={
              fbAssignedPagesResponse?.data?.data
            }
          />
        )}
      </div>
      <Button
        onClick={() => void onSaveDetailsClick()}
        className="mt-5"
        disabled={
          !selectedFbPageId ||
          (campaignTypeCtwa && selectedFbPageId === DEFAULT_PAGE_ID)
        }
      >
        <p>Save</p>
      </Button>
      {grantFbPageAccessMutation.isLoading ||
      fbAssignedPagesResponse.isLoading ? (
        <FullScreenLoader />
      ) : null}
    </div>
  );
};

export default FbPageContainer;
