import React, { useState, useEffect } from 'react';
import Button from '@/components/lib/Button';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import RotatingMessages from '@/components/lib/RotatingMessages';
import { getProgressWidthClass } from 'src/utils';

interface VideoConfirmationProps {
  videoUrl: string;
  isLoading: boolean;
  isConfirming: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

const VideoConfirmation: React.FC<VideoConfirmationProps> = ({
  videoUrl,
  isLoading,
  isConfirming,
  onConfirm,
  onCancel,
}) => {
  const [progress, setProgress] = useState(0);

  const loadingMessages = [
    'Preparing your video...',
    'Applying AI enhancements...',
    'Adding brand elements...',
    'Optimizing for social media...',
    'Finalizing your video...',
    'Almost there...',
    'Hang tight...',
  ];

  useEffect(() => {
    if (isLoading) {
      setProgress(0);

      const interval = setInterval(() => {
        setProgress((prev) => {
          return Math.min(prev + 8, 95);
        });
      }, 5000);

      return () => clearInterval(interval);
    } else {
      setProgress(100);
    }
  }, [isLoading]);

  return (
    <div className="flex flex-col flex-1 h-full">
      <div className="flex-1 overflow-y-scroll no-scrollbar my-2">
        {isLoading ? (
          <div className="flex flex-col items-center justify-center h-full py-40">
            <SpinnerLoader />
            <div className="mt-4">
              <RotatingMessages
                messages={loadingMessages}
                interval={5000}
                className="text-sm text-gray-dark"
              />
            </div>
            <div className="w-64 bg-gray-200 rounded-full h-2.5 mt-4 relative overflow-hidden">
              <div
                className={`bg-gradient-to-r from-primary via-primary/70 to-primary h-2.5 rounded-full transition-all duration-500 absolute top-0 left-0 ${getProgressWidthClass(
                  progress,
                )} bg-slide`}
              ></div>
            </div>
            <p className="text-xs text-gray-medium mt-2">
              {progress}% complete
            </p>
          </div>
        ) : (
          <div className="w-fit mx-auto">
            <video width="280" controls autoPlay className="rounded-lg">
              <source src={videoUrl} />
            </video>
          </div>
        )}
      </div>
      <Button onClick={onConfirm} disabled={isLoading || isConfirming}>
        {isConfirming ? (
          <div className="flex items-center justify-center">
            <SpinnerLoader size={20} />
            <p className="ml-2">Saving video...</p>
          </div>
        ) : (
          <p>Confirm video</p>
        )}
      </Button>
      <p
        className={`text-center text-sm mt-2 ${
          isConfirming ? 'text-gray-medium' : 'text-hyperlink cursor-pointer'
        }`}
        onClick={isConfirming ? undefined : onCancel}
      >
        Cancel
      </p>
    </div>
  );
};

export default VideoConfirmation;
