import { useRouter } from 'next/router';
import { useState, useEffect, useRef, ChangeEventHandler } from 'react';
import { useMutation, useQuery } from 'react-query';
import Button from '@/components/lib/Button';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { getCommonHeaders } from 'src/actions';
import {
  generateBannerBasedVideoAd,
  getAdVideosDetails,
  getBannerBasedVideoAdTemplate,
  uploadAdVideo,
  uploadAiGeneratedAdVideo,
} from 'src/actions/onboarding';
import { AdPlatforms, IGroweasyUser } from 'src/types';
import {
  ICampaign,
  IAdVideo,
  IBannerBasedVideoTemplate,
} from 'src/types/campaigns';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import { OnboardingStepIds, QueryParams } from 'src/constants';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import UploadedAdVideos from './UploadedAdVideos';
import Image from 'next/image';
import AiGeneratedVideoAd from './AiGeneratedVideoAd';
import FetchError from 'src/actions/FetchError';
import { showToastMessage } from 'src/modules/toast';

interface IAdVideosCompProps {
  campaignDetails: ICampaign;
  saveCampaignDetails: (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => void;
  user: IGroweasyUser;
  onNextPress: () => void;
}

const VIDEO_FILE_SIZE_LIMIT = 30; // 30 MB

const OldAdVideosComp = (props: IAdVideosCompProps) => {
  const { campaignDetails, saveCampaignDetails, user, onNextPress } = props;

  const [uploadAdVideosInProgress, setUploadAdVideosInProgress] =
    useState(false);
  const [existingAdVideos, setExistingAdVideos] = useState<IAdVideo[]>([]);
  const [selectedVideoFiles, setSelectedVideoFiles] = useState<File[]>([]);
  const [aivideoAdTemplate, setAiVideoAdTemplate] =
    useState<IBannerBasedVideoTemplate>();
  const [isAiVideoAdLoading, setIsAiVideoAdLoading] = useState(false);
  const [aiGeneratedVideoUrl, setAiGeneratedVideoUrl] = useState('');

  const isAiGeneratedVideoLoadingActive = useRef(false);

  const uploadVideoInputRef = useRef<HTMLInputElement>(null);

  const router = useRouter();
  const adPlatform = router.query[QueryParams.PLATFORM] ?? AdPlatforms.META;

  const adVideosResponse = useQuery(
    ['getAdVideosDetails', campaignDetails.id],
    () =>
      getAdVideosDetails({
        queryParams: {
          ...router.query,
          ids: existingAdVideos.map((item) => item.id)?.join(','),
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      enabled: !!existingAdVideos?.filter((item) => !!item.id)?.length,
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'AdVideosComp.adVideosResponse');
      },
    },
  );

  const getAiVideoAdTemplateMutation = useMutation(
    getBannerBasedVideoAdTemplate,
  );

  const uploadAdVideoMutation = useMutation(uploadAdVideo);

  const uploadAiGeneratedAdVideoMutation = useMutation(
    uploadAiGeneratedAdVideo,
  );

  const generateAiVideoAdMutation = useMutation(generateBannerBasedVideoAd);

  useEffect(() => {
    setExistingAdVideos(campaignDetails?.details?.ad_videos ?? []);
  }, [campaignDetails]);

  const onUploadIconPress = () => {
    uploadVideoInputRef.current?.click();
  };

  const onUploadImageInputChange: ChangeEventHandler<HTMLInputElement> = (
    e,
  ) => {
    const fileList = e.target?.files;
    const files = [];
    for (let i = 0; i < fileList.length; i++) {
      files.push(fileList[i]);
    }
    setSelectedVideoFiles(files);
  };

  const uploadAdVideoAndReturnDetails = async (): Promise<IAdVideo[]> => {
    const promises = [];
    const adVideos: IAdVideo[] = [];
    setUploadAdVideosInProgress(true);
    const queryParams = {
      ...(router.query as Record<string, string>),
      [QueryParams.AD_ACCOUNT_ID]:
        campaignDetails?.details?.config?.ad_account_id ?? '',
    };
    if (selectedVideoFiles?.length) {
      selectedVideoFiles.forEach((file) => {
        promises.push(
          uploadAdVideoMutation.mutateAsync({
            headers: getCommonHeaders(user),
            queryParams,
            blob: file,
            fileName: file.name,
            campaignType: campaignDetails?.type,
          }),
        );
      });
    }
    try {
      const uploadResponses = (await Promise.all(promises)) as Array<{
        data: IAdVideo;
      }>;
      uploadResponses.forEach((response) => {
        adVideos.push(response.data);
      });
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error as Error,
        'AdVideosComp.uploadAdVideoAndReturnDetails',
      );
    } finally {
      setUploadAdVideosInProgress(false);
    }
    return adVideos;
  };

  const saveUploadedVideosDetails = (adVideos: IAdVideo[]) => {
    if (!adVideos.length) {
      return;
    }
    const updatedCampaignDetails: ICampaign = {
      ...campaignDetails,
      details: {
        ...campaignDetails?.details,
        ad_videos: adVideos,
      },
    };
    saveCampaignDetails(updatedCampaignDetails, OnboardingStepIds.AD_BANNERS);
  };

  const onSaveDetailsClick = async () => {
    logEvent(EVENT_NAMES.ad_videos_save_clicked);
    const adVideos = await uploadAdVideoAndReturnDetails();
    saveUploadedVideosDetails(adVideos);
  };

  const onSkipThisStepClick = () => {
    logEvent(EVENT_NAMES.ad_videos_skip_clicked);
    onNextPress();
  };

  const handleAiGeneratedVideoClick = async () => {
    isAiGeneratedVideoLoadingActive.current = true;
    logEvent(EVENT_NAMES.use_ai_generated_video_clicked);

    try {
      setIsAiVideoAdLoading(true);
      setAiVideoAdTemplate(null);
      setAiGeneratedVideoUrl('');

      const res = await getAiVideoAdTemplateMutation.mutateAsync({
        headers: getCommonHeaders(user),
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails?.id,
          [QueryParams.VERSION]: 'v2',
        },
      });

      // Check if request was cancelled
      if (!isAiGeneratedVideoLoadingActive.current) {
        return;
      }

      setAiVideoAdTemplate(res.data);

      const aiVideoResponse = await generateAiVideoAdMutation.mutateAsync({
        headers: getCommonHeaders(user),
        data: { template: res.data },
        queryParams: {},
      });

      // Check if request was cancelled
      if (!isAiGeneratedVideoLoadingActive.current) {
        return;
      }

      setAiGeneratedVideoUrl(aiVideoResponse.data.url);
    } catch (error) {
      if (isAiGeneratedVideoLoadingActive.current) {
        if (error instanceof Error || error instanceof FetchError) {
          logApiErrorAndShowToastMessage(
            error,
            'AdVideosComp.handleAiGenerateVideoClick',
          );
        } else {
          showToastMessage('Unknown Error Occured', 'error');
        }
      }
    } finally {
      if (isAiGeneratedVideoLoadingActive.current) {
        setIsAiVideoAdLoading(false);
      }
      isAiGeneratedVideoLoadingActive.current = false;
    }
  };

  const cancelAiVideoLoading = () => {
    isAiGeneratedVideoLoadingActive.current = false;

    if (!aivideoAdTemplate || !aiGeneratedVideoUrl) {
      // if both the data were present this mean the cancel was made for loading due to eidt so no need to destory previous generated video
      setAiVideoAdTemplate(null);
      setAiGeneratedVideoUrl('');
    }
    setIsAiVideoAdLoading(false);
  };

  const onUseThisAiGeneratedVideoClick = async () => {
    setUploadAdVideosInProgress(true);

    try {
      const adVideos = await uploadAiGeneratedAdVideoMutation.mutateAsync({
        headers: getCommonHeaders(user),
        data: { file_url: aiGeneratedVideoUrl },
        queryParams: {
          ...router.query,
          [QueryParams.AD_ACCOUNT_ID]:
            campaignDetails?.details?.config?.ad_account_id ?? '',
        } as Record<string, string>,
        campaignType: campaignDetails.type,
      });

      saveUploadedVideosDetails([adVideos.data]);
    } catch (error) {
      if (error instanceof Error || error instanceof FetchError) {
        logApiErrorAndShowToastMessage(
          error,
          'AdVideosComp.onUseThisAiGeneratedVideoClick',
        );
      } else {
        showToastMessage('Unknown Error Occured', 'error');
      }
    } finally {
      setUploadAdVideosInProgress(false);
    }
  };

  /*const handleAiGeneratedVideoEdit = async (data: IAiVideoAdTemplate) => {
    isAiGeneratedVideoLoadingActive.current = true;

    try {
      setIsAiVideoAdLoading(true);

      const aiVideoResponse = await generateAiVideoAdMutation.mutateAsync({
        headers: getCommonHeaders(user),
        data: { template: data.template },
        queryParams: {},
      });

      // Check if request was cancelled
      if (!isAiGeneratedVideoLoadingActive.current) {
        return;
      }

      setAiGeneratedVideoUrl(aiVideoResponse.data.url);
    } catch (error) {
      if (isAiGeneratedVideoLoadingActive.current) {
        if (error instanceof Error || error instanceof FetchError) {
          logApiErrorAndShowToastMessage(
            error,
            'AdVideosComp.handleAiGeneratedVideoEdit',
          );
        } else {
          showToastMessage('Unknown Error Occured', 'error');
        }
      }
    } finally {
      if (isAiGeneratedVideoLoadingActive.current) {
        setAiVideoAdTemplate(data); // only if the edit request is not cancled merge the edited data in the original
      }
      setIsAiVideoAdLoading(false);
      isAiGeneratedVideoLoadingActive.current = false;
    }
  };*/

  // 25 MB limit on size
  const videoFileSizeExceeded =
    selectedVideoFiles.length !== 0 &&
    Math.ceil(selectedVideoFiles[0]?.size / (1024 * 1024)) >
      VIDEO_FILE_SIZE_LIMIT;

  // if banner data is available in ad images, show banner based Video option
  const enableBannerBasedVideoOption =
    !!campaignDetails?.details?.ad_banners?.find(
      (item) => !!item.banner_data,
    ) && adPlatform === AdPlatforms.META;

  return (
    <div className="px-4 pt-4 pb-4 flex flex-col flex-1 h-full">
      {existingAdVideos.length ? (
        adVideosResponse.isLoading ? (
          <div className="flex flex-col items-center mt-3">
            <SpinnerLoader />
          </div>
        ) : (
          <UploadedAdVideos
            videos={Object.values(adVideosResponse.data?.data ?? {})}
            onReset={() => {
              logEvent(EVENT_NAMES.ad_videos_reset_clicked);
              setExistingAdVideos([]);
            }}
            onNextPress={onNextPress}
            uploadedVideos={campaignDetails?.details?.ad_videos}
          />
        )
      ) : isAiVideoAdLoading || aivideoAdTemplate ? (
        <AiGeneratedVideoAd
          isLoading={isAiVideoAdLoading}
          aivideoAdTemplate={aivideoAdTemplate}
          aiGeneratedVideoUrl={aiGeneratedVideoUrl}
          onNextClick={saveUploadedVideosDetails}
          onSkipThisStepClick={onSkipThisStepClick}
          cancelRequest={cancelAiVideoLoading}
          onUseThisAiGeneratedVideoClick={() =>
            void onUseThisAiGeneratedVideoClick()
          }
          /*handleEdit={(data: IAiVideoAdTemplate) =>
            void handleAiGeneratedVideoEdit(data)
          }*/
        />
      ) : (
        <div className="flex flex-col flex-1 h-full">
          <div className="flex flex-col flex-1 h-full">
            <div className="flex-1 overflow-y-scroll no-scrollbar mt-2">
              <p className="text-sm text-black">Do you have a video?</p>
              <p className="text-xs text-gray-dark mt-3">
                A combination of image and video ads is recommended for optimal
                performance.
              </p>
              <p className="text-xs text-gray-dark mt-2">
                Video ads often outperform image ads, sometimes cutting lead
                costs by up to 50%.
              </p>
              <div
                className="rounded-xl mt-5 flex flex-col items-center justify-center p-12 border-dashed border cursor-pointer"
                onClick={onUploadIconPress}
              >
                <input
                  type="file"
                  ref={uploadVideoInputRef}
                  onChange={onUploadImageInputChange}
                  className="hidden"
                  accept="video/*"
                />
                <div className="flex">
                  <Image
                    src="/images/common/upload-icon.png"
                    width={52}
                    height={52}
                    alt="upload icon"
                  />
                </div>
                {selectedVideoFiles.length === 0 ? (
                  <p className="text-xs text-gray-medium font-medium mt-4">
                    Click here and select a video (Max size:{' '}
                    {VIDEO_FILE_SIZE_LIMIT} MB)
                  </p>
                ) : null}
                <div className="mt-4">
                  {selectedVideoFiles.map((file, index) => {
                    return (
                      <div key={index}>
                        <p className="text-xs text-gray-medium font-medium">{`${
                          file.name
                        }, ${Math.round(file.size / (1024 * 1024))} MB`}</p>
                      </div>
                    );
                  })}
                </div>
                {videoFileSizeExceeded ? (
                  <p className="text-xs mt-3 text-red text-center">
                    Please select a video with a size of less than{' '}
                    {VIDEO_FILE_SIZE_LIMIT} MB.
                  </p>
                ) : null}
              </div>
              {enableBannerBasedVideoOption ? (
                <div>
                  <p className="text-center mt-3 text-gray-medium">OR</p>
                  <div className="flex flex-row items-center justify-center mt-3">
                    <p
                      className="text-hyperlink text-center text-sm font-medium cursor-pointer mr-1"
                      onClick={() => void handleAiGeneratedVideoClick()}
                    >
                      Use AI Generated Video
                    </p>
                    <p className="text-xxs text-primary">(Beta)</p>
                  </div>
                </div>
              ) : null}
            </div>
            <Button
              onClick={() => void onSaveDetailsClick()}
              disabled={!selectedVideoFiles.length || videoFileSizeExceeded}
              className="mt-3"
            >
              <p>Upload</p>
            </Button>
            <p
              className="text-center text-sm text-hyperlink mt-2 cursor-pointer"
              onClick={onSkipThisStepClick}
            >
              Skip this Step
            </p>
          </div>
        </div>
      )}
      {uploadAdVideosInProgress ? <FullScreenLoader /> : null}
    </div>
  );
};

export default OldAdVideosComp;
