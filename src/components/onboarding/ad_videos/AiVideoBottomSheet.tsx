import {
  useState,
  useEffect,
  useCallback,
  useRef,
  ChangeEventHandler,
} from 'react';
import { ICampaign } from 'src/types/campaigns';
import { IGroweasyUser } from 'src/types';
import BottomSheet from '@/components/lib/BottomSheet';
import Button from '@/components/lib/Button';
import { IoClose, IoSearch, IoTrash } from 'react-icons/io5';
import { IoMdCloudUpload } from 'react-icons/io';
import { getCommonHeaders } from 'src/actions';
import {
  getRemotionVideoData,
  uploadAiGeneratedAdVideo,
  generateRemotionVideo,
  searchVideos,
} from 'src/actions/onboarding';
import { uploadFileAndGetS3Url } from 'src/actions/campaign_details';
import {
  IRemotionVideoData,
  IPexelsVideoData,
  IRemotionVideoDataResponse,
} from 'src/types/remotion';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import {
  logApiErrorAndShowToastMessage,
  getHdVideoUrlsFromPexelsData,
} from 'src/utils';
import { showToastMessage } from 'src/modules/toast';
import FetchError from 'src/actions/FetchError';
import VideoConfirmation from './VideoConfirmation';
import VideoThumbnail from './VideoThumbnail';
import TabButton from './TabButton';
import DragAndDrop from '@/components/lib/DragAndDrop';
import { IAdVideo } from 'src/types/campaigns';
import { QueryParams, VIDEO_FILE_SIZE_LIMIT_IN_KB } from 'src/constants';
import dynamic from 'next/dynamic';
import debounce from 'lodash.debounce';

const RotatingMessages = dynamic(
  () => import('@/components/lib/RotatingMessages'),
  {
    ssr: false,
  },
);

interface IAiVideoBottomSheetProps {
  onClose: () => void;
  onVideoGenerated: (adVideos: IAdVideo[]) => void;
  user: IGroweasyUser;
  campaignDetails: ICampaign;
  queryParams: Record<string, string>;
}

const AiVideoBottomSheet = ({
  onClose,
  onVideoGenerated,
  user,
  campaignDetails,
  queryParams,
}: IAiVideoBottomSheetProps) => {
  // we can chnage the Template from here!
  const [templateMode] = useState<'p10' | 'p11'>('p10');

  // P11 specific states (enabled when templateMode is 'p11')
  const [activeTab, setActiveTab] = useState<'upload' | 'select'>('select');
  const [selectedVideoFile, setSelectedVideoFile] = useState<File | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<IPexelsVideoData[]>([]);
  const [suggestedVideos, setSuggestedVideos] = useState<IPexelsVideoData[]>(
    [],
  );
  const [selectedSearchVideo, setSelectedSearchVideo] =
    useState<IPexelsVideoData | null>(null);
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([]);
  const [currentlyPlayingIndex, setCurrentlyPlayingIndex] = useState<
    number | null
  >(null);
  const [isDragging, setIsDragging] = useState(false);

  // Common states
  const [videoData, setVideoData] = useState<IRemotionVideoData | null>();
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);

  // Video generation states
  const [isShowingVideoConfirmation, setIsShowingVideoConfirmation] =
    useState(false);
  const [isGeneratingVideo, setIsGeneratingVideo] = useState(false);
  const [isConfirmingVideo, setIsConfirmingVideo] = useState(false);
  const [generatedVideoUrl, setGeneratedVideoUrl] = useState('');
  const [pendingVideoUrl, setPendingVideoUrl] = useState('');
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(true);

  const getVideoDataFromBackend = async () => {
    if (!campaignDetails?.id) return;

    try {
      setIsLoadingSuggestions(true);
      const response = await getRemotionVideoData({
        headers: getCommonHeaders(user),
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
          [QueryParams.TEMPLATE_ID]: templateMode,
        },
      });
      setVideoData(response.data.video_data);

      // For P11 mode, extract suggested videos from response
      if (templateMode === 'p11') {
        const videos = extractVideosFromResponse(response);
        setSuggestedVideos(videos);
      }

      // For P10 mode or when no base video is needed, generate AI video directly
      if (
        templateMode === 'p10' ||
        !response.data?.video_data?.base_assets?.some(
          (asset) => asset.type === 'video',
        )
      ) {
        await handleGenerateAiVideoWithoutBaseVideo(response.data.video_data);
        return;
      }
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error as Error,
        'AiVideoBottomSheet.getVideoDataFromBackend',
      );
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const extractVideosFromResponse = (
    response: IRemotionVideoDataResponse,
  ): IPexelsVideoData[] => {
    if (response.data?.media_suggestions?.videos) {
      return response.data.media_suggestions.videos;
    }

    return [];
  };

  useEffect(() => {
    void getVideoDataFromBackend();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [campaignDetails?.id, templateMode]);

  const onUploadVideoInputChange: ChangeEventHandler<HTMLInputElement> = (
    e,
  ) => {
    const file = e.target?.files?.[0];

    if (!file) return;

    // check for file type
    if (!file.type.startsWith('video')) {
      showToastMessage(
        'Invalid file type! Please upload a valid file.',
        'error',
      );
    } else {
      // check for file size
      const fileSizeInKb = file.size / 1024;
      if (fileSizeInKb > VIDEO_FILE_SIZE_LIMIT_IN_KB) {
        showToastMessage(
          `File "${file.name}" exceeds the maximum size of ${
            VIDEO_FILE_SIZE_LIMIT_IN_KB / 1024
          } MB.`,
          'error',
        );
      } else {
        setSelectedVideoFile(file);
      }
    }

    e.target.value = '';
  };

  const handleSelectSearchVideo = (video: IPexelsVideoData) => {
    setSelectedSearchVideo(video);
    setSelectedVideoFile(null);
  };

  const handleSearch = useCallback(
    async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        return;
      }

      try {
        setIsSearching(true);
        setSearchResults([]);

        const response = await searchVideos({
          headers: getCommonHeaders(user),
          queryParams: { query },
        });

        setSearchResults(response.data as unknown as IPexelsVideoData[]);
      } catch (error) {
        logApiErrorAndShowToastMessage(
          error as Error,
          'AiVideoBottomSheet.handleSearch',
        );
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    },
    [user],
  );

  const debouncedSearch = useRef(
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    debounce((query: string) => {
      void handleSearch(query);
    }, 1000) as {
      (query: string): void;
      cancel: () => void;
    },
  ).current;

  const handleSearchInputChange: ChangeEventHandler<HTMLInputElement> = (e) => {
    const newQuery = e.target.value;
    setSearchQuery(newQuery);
    debouncedSearch(newQuery);
  };

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const handleStartVideoGeneration = async () => {
    // For P11 mode, check if video selection is needed
    if (templateMode === 'p11') {
      const needsVideoSelection = videoData?.base_assets?.some(
        (asset) => asset.type === 'video',
      );

      if (
        needsVideoSelection &&
        ((activeTab === 'upload' && !selectedVideoFile) ||
          (activeTab === 'select' && selectedSearchVideo === null))
      ) {
        showToastMessage('Please select or upload a video first.', 'error');
        return;
      }
    }

    // For P10 mode or when no base video is needed, generate AI video directly
    if (
      templateMode === 'p10' ||
      !videoData?.base_assets?.some((asset) => asset.type === 'video')
    ) {
      if (videoData) {
        await handleGenerateAiVideoWithoutBaseVideo(videoData);
        return;
      }
    }

    // P11 mode video generation with base video
    if (templateMode === 'p11') {
      setGeneratedVideoUrl('');
      setPendingVideoUrl('');
      setIsGeneratingVideo(true);
      setIsShowingVideoConfirmation(true);
      setIsBottomSheetVisible(true);

      if (activeTab === 'select' && selectedSearchVideo) {
        try {
          const videoFiles = selectedSearchVideo.video_files || [];
          let videoLinkToUse: string | undefined;

          const hdVideo = getHdVideoUrlsFromPexelsData([selectedSearchVideo]);

          videoLinkToUse = hdVideo?.[0]?.url;

          if (!videoLinkToUse && videoFiles.length > 0) {
            videoLinkToUse = videoFiles[2].link;
          }

          if (videoLinkToUse) {
            if (videoData) {
              try {
                const remotionResponse = await generateRemotionVideo({
                  headers: getCommonHeaders(user),
                  data: {
                    campaign_id: campaignDetails.id,
                    template_data: {
                      ...videoData,
                      base_assets: [
                        {
                          type: 'video',
                          url: videoLinkToUse,
                          video_duration: selectedSearchVideo.duration || 0,
                        },
                      ],
                    },
                  },
                  queryParams: {},
                });

                setGeneratedVideoUrl(remotionResponse.data.s3_url);
                setPendingVideoUrl(videoLinkToUse);
                setIsGeneratingVideo(false);
              } catch (error) {
                logApiErrorAndShowToastMessage(
                  error as Error,
                  'AiVideoBottomSheet.handleStartVideoGeneration.remotion',
                );
                showToastMessage('Failed to generate video', 'error');
                setIsGeneratingVideo(false);
                setIsShowingVideoConfirmation(false);
              }
            } else {
              setPendingVideoUrl(videoLinkToUse);
              setGeneratedVideoUrl(videoLinkToUse);
              setIsGeneratingVideo(false);
            }
          } else {
            showToastMessage(
              'No suitable video file found for download',
              'error',
            );
            setIsShowingVideoConfirmation(false);
          }
        } catch (error) {
          logApiErrorAndShowToastMessage(
            error as Error,
            'AiVideoBottomSheet.handleStartVideoGeneration',
          );
          setIsGeneratingVideo(false);
          setIsShowingVideoConfirmation(false);
        }
      } else if (selectedVideoFile) {
        try {
          let videoDuration = 15; //falback video duration

          try {
            const getVideoDuration = (file: File): Promise<number> => {
              return new Promise((resolve) => {
                const video = document.createElement('video');
                video.preload = 'metadata';

                const objectUrl = URL.createObjectURL(file);

                video.onloadedmetadata = () => {
                  URL.revokeObjectURL(objectUrl);
                  resolve(video.duration);
                };

                video.onerror = () => {
                  URL.revokeObjectURL(objectUrl);
                  resolve(15);
                };

                const timeout = setTimeout(() => {
                  URL.revokeObjectURL(objectUrl);
                  resolve(15);
                }, 5000);

                video.onloadedmetadata = () => {
                  clearTimeout(timeout);
                  URL.revokeObjectURL(objectUrl);
                  resolve(video.duration);
                };

                video.src = objectUrl;
              });
            };

            videoDuration = await getVideoDuration(selectedVideoFile);
          } catch (error) {
            console.error('Error getting video duration:', error);
          }

          const s3Response = await uploadFileAndGetS3Url({
            headers: getCommonHeaders(user),
            data: { file: selectedVideoFile },
          });

          if (videoData && s3Response.data.s3_url) {
            try {
              const remotionResponse = await generateRemotionVideo({
                headers: getCommonHeaders(user),
                data: {
                  campaign_id: campaignDetails.id,
                  template_data: {
                    ...videoData,
                    base_assets: [
                      {
                        type: 'video',
                        url: s3Response.data.s3_url,
                        video_duration: videoDuration,
                      },
                    ],
                  },
                },
                queryParams: {},
              });

              setGeneratedVideoUrl(remotionResponse.data.s3_url);
              setPendingVideoUrl(s3Response.data.s3_url);
              setIsGeneratingVideo(false);
            } catch (error) {
              logApiErrorAndShowToastMessage(
                error as Error,
                'AiVideoBottomSheet.handleStartVideoGeneration.remotion',
              );
              showToastMessage('Failed to generate video', 'error');
              setIsGeneratingVideo(false);
              setIsShowingVideoConfirmation(false);
            }
          } else {
            setPendingVideoUrl(s3Response.data.s3_url);
            setGeneratedVideoUrl(s3Response.data.s3_url);
            setIsGeneratingVideo(false);
          }
        } catch (error) {
          if (error instanceof Error || error instanceof FetchError) {
            logApiErrorAndShowToastMessage(
              error,
              'AiVideoBottomSheet.handleStartVideoGeneration.videoFile',
            );
          }
          setIsGeneratingVideo(false);
          setIsShowingVideoConfirmation(false);
        }
      }
    }
  };

  const handleConfirmVideo = async () => {
    try {
      setIsConfirmingVideo(true);

      const videoUrlToUse = generatedVideoUrl || pendingVideoUrl;

      if (!videoUrlToUse) {
        showToastMessage('No video URL available', 'error');
        setIsConfirmingVideo(false);
        return;
      }

      const adVideoResponse = await uploadAiGeneratedAdVideo({
        headers: getCommonHeaders(user),
        data: { file_url: videoUrlToUse },
        queryParams: {
          ...queryParams,
          [QueryParams.AD_ACCOUNT_ID]:
            campaignDetails?.details?.config?.ad_account_id ?? '',
        } as Record<string, string>,
        campaignType: campaignDetails.type,
      });

      onVideoGenerated([adVideoResponse.data]);
      handleCloseConfirmation();
      onClose();
    } catch (error) {
      if (error instanceof Error || error instanceof FetchError) {
        logApiErrorAndShowToastMessage(
          error,
          'AiVideoBottomSheet.handleConfirmVideo',
        );
      } else {
        showToastMessage('Unknown Error Occurred', 'error');
      }
      setIsConfirmingVideo(false);
    }
  };

  const handleCancelVideoConfirmation = () => {
    setIsShowingVideoConfirmation(false);
    setIsConfirmingVideo(false);
    setGeneratedVideoUrl('');
    setPendingVideoUrl('');
  };

  const handleCloseConfirmation = () => {
    setIsShowingVideoConfirmation(false);
    setIsConfirmingVideo(false);
    setGeneratedVideoUrl('');
    setPendingVideoUrl('');
  };

  const handleGenerateAiVideoWithoutBaseVideo = async (
    videoData: IRemotionVideoData,
  ) => {
    setGeneratedVideoUrl('');
    setPendingVideoUrl('');
    setIsGeneratingVideo(true);
    setIsShowingVideoConfirmation(true);
    setIsBottomSheetVisible(true);

    try {
      const remotionResponse = await generateRemotionVideo({
        headers: getCommonHeaders(user),
        data: {
          campaign_id: campaignDetails.id,
          template_data: videoData,
        },
        queryParams: {},
      });

      setGeneratedVideoUrl(remotionResponse.data.s3_url);
      setIsGeneratingVideo(false);
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error as Error,
        'AiVideoBottomSheet.handleGenerateAiVideoWithoutBaseVideo',
      );
      showToastMessage('Failed to generate video', 'error');
      setIsGeneratingVideo(false);
      setIsShowingVideoConfirmation(false);
    }
  };

  if (!isBottomSheetVisible) {
    return null;
  }

  return (
    <BottomSheet onClose={onClose} className="h-4/5 flex-1 ">
      <div className="flex flex-col h-full">
        {isShowingVideoConfirmation ? (
          <VideoConfirmation
            videoUrl={generatedVideoUrl}
            isLoading={isGeneratingVideo}
            isConfirming={isConfirmingVideo}
            onConfirm={() => void handleConfirmVideo()}
            onCancel={handleCancelVideoConfirmation}
          />
        ) : isLoadingSuggestions ? (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="absolute top-4 right-4">
              <button
                type="button"
                onClick={onClose}
                className="text-gray-medium hover:text-gray-dark"
                aria-label="Close"
              >
                <IoClose size={24} />
              </button>
            </div>
            <div className="flex flex-col items-center justify-center py-36">
              <SpinnerLoader size={60} />
              <div className="text-sm text-gray-dark mt-6">
                <RotatingMessages
                  interval={4000}
                  messages={[
                    'Preparing video options...',
                    'Fetching the best suggestions...',
                    'Analyzing your campaign details...',
                    'Finalizing your video preview...',
                    'Almost done. Hang tight...',
                  ]}
                />
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="flex border-b border-gray-light mb-4 sticky top-0 bg-white z-10">
              <button
                type="button"
                onClick={onClose}
                className="absolute -right-2 -top-2 transform -translate-y-1/2 text-gray-medium hover:text-gray-dark"
                aria-label="Close"
              >
                <IoClose size={24} />
              </button>
              <h3 className="text-lg font-semibold text-gray-dark py-3">
                Generate AI Video
              </h3>
            </div>
            <div className="flex-1 overflow-y-auto no-scrollbar md:px-4 pb-4">
              {templateMode === 'p11' ? (
                <>
                  {/* P11 Mode - Tabs for Upload/Select */}
                  <div className="flex border-b border-gray-light mb-4">
                    <TabButton
                      tabId="select"
                      label="Search Base Video"
                      activeTab={activeTab}
                      setActiveTab={setActiveTab}
                    />
                    <TabButton
                      tabId="upload"
                      label="Upload Base Video"
                      activeTab={activeTab}
                      setActiveTab={setActiveTab}
                    />
                  </div>

                  {activeTab === 'upload' ? (
                    <div className="flex flex-col items-center">
                      <p className="text-sm text-gray-dark my-4">
                        Upload a base video, and let AI craft a customized
                        version
                      </p>
                      {selectedVideoFile ? (
                        <div className="w-full flex flex-col items-center">
                          <video
                            src={URL.createObjectURL(selectedVideoFile)}
                            controls
                            className="w-auto max-h-96 mx-auto rounded-xl overflow-hidden border border-gray-light"
                          />
                          <button
                            type="button"
                            onClick={() => setSelectedVideoFile(null)}
                            className="text-xs flex gap-2 items-center text-red hover:text-red-800 mt-2"
                          >
                            Remove video <IoTrash />
                          </button>
                        </div>
                      ) : (
                        <DragAndDrop
                          acceptedTypes={['video/*']}
                          handleUpload={onUploadVideoInputChange}
                          setIsDragging={setIsDragging}
                          maxFileSizeInKB={VIDEO_FILE_SIZE_LIMIT_IN_KB}
                        >
                          <div
                            className="border-2 border-dashed rounded-lg px-6 py-14 mt-6 w-full flex flex-col items-center justify-center cursor-pointer transition-all border-gray-light hover:border-primary data-[isDragging=true]:border-primary"
                            data-isDragging={isDragging}
                            onClick={() =>
                              document
                                .getElementById('video-upload-input')
                                ?.click()
                            }
                          >
                            <IoMdCloudUpload
                              size={40}
                              className="text-gray-medium"
                            />
                            <p className="text-sm mt-2 text-gray-dark">
                              Click or Drag to Upload
                            </p>
                            <p className="text-xs text-gray-medium mt-1">
                              MP4, MOV, AVI etc.
                            </p>
                          </div>
                        </DragAndDrop>
                      )}
                      <input
                        id="video-upload-input"
                        title="video"
                        type="file"
                        accept="video/*"
                        onChange={onUploadVideoInputChange}
                        className="hidden"
                      />
                    </div>
                  ) : (
                    <div>
                      <p className="text-sm text-gray-dark mb-2">
                        Use a stock video as your base, and let AI craft a
                        customized version
                      </p>
                      <div className="relative mb-4">
                        <input
                          type="text"
                          placeholder="e.g., 'office meeting', 'nature landscape'"
                          value={searchQuery}
                          onChange={handleSearchInputChange}
                          className="w-full p-3 border border-gray-light rounded-lg pr-12 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                        />
                        <button
                          type="button"
                          onClick={() => void handleSearch(searchQuery)}
                          className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-gray-medium hover:text-primary"
                          aria-label="Search videos"
                          disabled={isSearching}
                        >
                          {isSearching ? (
                            <SpinnerLoader size={20} />
                          ) : (
                            <IoSearch size={20} />
                          )}
                        </button>
                      </div>

                      {isSearching && searchResults.length === 0 ? (
                        <div className="flex justify-center items-center py-8">
                          <SpinnerLoader />
                        </div>
                      ) : searchResults.length > 0 ? (
                        <div>
                          <p className="text-sm text-gray-dark mb-3">
                            Search results:
                          </p>
                          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                            {searchResults.map((video, index) => (
                              <VideoThumbnail
                                key={`search-${video.id}-${index}`}
                                video={video}
                                index={index}
                                currentlyPlayingIndex={currentlyPlayingIndex}
                                setCurrentlyPlayingIndex={
                                  setCurrentlyPlayingIndex
                                }
                                videoRefs={videoRefs}
                                onClick={() => handleSelectSearchVideo(video)}
                              />
                            ))}
                          </div>
                        </div>
                      ) : searchQuery.trim() && !isSearching ? (
                        <p className="text-sm text-center text-gray-medium py-8">
                          No videos found. Try a different search term.
                        </p>
                      ) : suggestedVideos.length > 0 ? (
                        <div>
                          <p className="text-sm text-gray-dark mb-3">
                            Suggested videos for your campaign:
                          </p>
                          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                            {suggestedVideos.map((video, index) => (
                              <VideoThumbnail
                                key={`suggested-${video.id}-${index}`}
                                video={video}
                                index={searchResults.length + index}
                                currentlyPlayingIndex={currentlyPlayingIndex}
                                setCurrentlyPlayingIndex={
                                  setCurrentlyPlayingIndex
                                }
                                videoRefs={videoRefs}
                                onClick={() => handleSelectSearchVideo(video)}
                              />
                            ))}
                          </div>
                        </div>
                      ) : (
                        !searchQuery.trim() && (
                          <p className="text-sm text-center text-gray-medium py-8">
                            Enter a search term to find stock videos.
                          </p>
                        )
                      )}
                    </div>
                  )}
                </>
              ) : (
                /* P10 Mode - Simple AI Generation */
                <div className="flex flex-col items-center justify-center py-16">
                  <div className="text-center">
                    <h4 className="text-xl font-medium text-gray-dark mb-2">
                      AI Video Generation
                    </h4>
                    <p className="text-sm text-gray-medium mb-6 max-w-md">
                      Our AI will create a customized video for your campaign
                      using intelligent design and content optimization.
                    </p>
                  </div>
                </div>
              )}
            </div>
            <div className="px-4 py-3 border-t border-gray-200 sticky bottom-0 bg-white">
              <Button
                onClick={() => void handleStartVideoGeneration()}
                className="w-full"
              >
                <p>Start Generating</p>
              </Button>
            </div>
          </>
        )}
      </div>
    </BottomSheet>
  );
};

export default AiVideoBottomSheet;
