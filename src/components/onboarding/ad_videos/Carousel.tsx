import React from 'react';

interface ICarouselProps {
  children: React.ReactNode;
  currentIndex: number;
  setCurrentIndex: React.Dispatch<React.SetStateAction<number>>;
  divRef?: React.MutableRefObject<HTMLDivElement>;
}

const Carousel = (props: ICarouselProps) => {
  const { children, currentIndex, setCurrentIndex, divRef } = props;

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const containerOffsetLeft = container.offsetLeft;
    const scrollLeft = container.scrollLeft;
    const containerWidth = container.clientWidth;

    const items = container.querySelectorAll('.snap-center');
    if (!items.length) return;

    let newIndex = currentIndex;
    let minDistance = Infinity;
    const containerCenter =
      containerOffsetLeft + scrollLeft + containerWidth / 2;

    items.forEach((item, index) => {
      const itemElement = item as HTMLElement;
      const itemOffsetLeft = itemElement.offsetLeft;
      const itemWidth = itemElement.clientWidth;

      // Calculate the center of the item relative to the container's scroll position
      const itemCenter = itemOffsetLeft + itemWidth / 2;

      // Distance from the item's center to the container's center
      const distance = Math.abs(itemCenter - containerCenter);

      if (distance < minDistance) {
        minDistance = distance;
        newIndex = index;
      }
    });

    if (newIndex !== currentIndex) {
      setCurrentIndex(newIndex);
    }
  };

  return (
    <div
      className=" flex overflow-x-auto snap-x snap-mandatory no-scrollbar w-full "
      onScroll={handleScroll}
      ref={divRef}
    >
      {React.Children.map(children, (child) => (
        <div className={` snap-center w-full shrink-0 `}>{child}</div>
      ))}
    </div>
  );
};

export default Carousel;
