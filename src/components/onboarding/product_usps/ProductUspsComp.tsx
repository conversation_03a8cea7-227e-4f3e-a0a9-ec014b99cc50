import { useState, useEffect, useRef } from 'react';
import { IBannerElement, ICampaign } from 'src/types/campaigns';
import { OnboardingStepIds, QueryParams } from 'src/constants';
import Button from '../../lib/Button';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import { IGroweasyUser } from 'src/types';
import { useQuery } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import { fetchProductUspsAndBannerElements } from 'src/actions/onboarding';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { BiPlus } from 'react-icons/bi';
import { logApiErrorAndShowToastMessage } from 'src/utils';

interface IProductUspsCompProps {
  campaignDetails: ICampaign;
  saveCampaignDetails: (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => void;
  user: IGroweasyUser;
}

interface IUSP {
  value: string;
  selected: boolean;
}

const ProductUspsComp = (props: IProductUspsCompProps) => {
  const { campaignDetails, saveCampaignDetails, user } = props;

  const [usps, setUsps] = useState<IUSP[]>([]);
  const [customUsp, setCustomUsp] = useState<string>('');
  const [showAddCustomUsp, setShowAddCustomUsp] = useState<boolean>(false);

  const customUspInputRef = useRef<HTMLInputElement>(null);

  // Use query for fetching USP suggestions
  const productUspsAndBannerElementsResponse = useQuery(
    ['fetchProductUspsAndBannerElements', campaignDetails?.id, 'usps'],
    () =>
      fetchProductUspsAndBannerElements({
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails?.id,
          [QueryParams.FLOW_TYPE]: 'usps',
        },
        headers: getCommonHeaders(user),
      }),
    {
      enabled:
        !!campaignDetails?.id &&
        !campaignDetails?.details?.ai_assisted_product_usps?.length,
      onError: (error) => {
        logApiErrorAndShowToastMessage(
          error as Error,
          'ProductUspsComp.fetchProductUspsAndBannerElements',
        );
      },
      onSuccess: (response) => {
        const banners = response.data?.banners;
        const suggestedUsps = banners.map(
          (banner: IBannerElement) => banner.focused_usp,
        );
        // max 3 USPs
        const uspObjects = Array.from(new Set(suggestedUsps))
          .slice(0, 3)
          .map((usp: string) => ({
            value: usp,
            selected: true,
          }));

        setUsps(uspObjects);
      },
    },
  );

  useEffect(() => {
    if (campaignDetails?.details?.ai_assisted_product_usps) {
      const savedUsps = campaignDetails?.details?.ai_assisted_product_usps;
      const uspObjects = savedUsps.map((usp: string) => ({
        value: usp,
        selected: true,
      }));

      setUsps(uspObjects);
    }
  }, [campaignDetails]);

  useEffect(() => {
    if (showAddCustomUsp) {
      setTimeout(() => {
        customUspInputRef.current?.focus();
      }, 200);

      setTimeout(() => {
        customUspInputRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }, 1000);
    }
  }, [showAddCustomUsp]);

  const toggleUspSelection = (index: number) => {
    const updatedUsps = [...usps];
    updatedUsps[index].selected = !updatedUsps[index].selected;
    setUsps(updatedUsps);
  };

  const addCustomUsp = () => {
    if (customUsp.trim() !== '') {
      setUsps([...usps, { value: customUsp.trim(), selected: true }]);
      setCustomUsp('');
      setShowAddCustomUsp(false);
    }
  };

  const getSelectedUspsCount = () => {
    return usps.filter((usp) => usp.selected).length;
  };

  const onSaveUspsClick = () => {
    logEvent(EVENT_NAMES.product_usps_save_clicked);

    const selectedUsps = usps
      .filter((usp) => usp.selected)
      .map((usp) => usp.value);

    const updatedCampaignDetails: ICampaign = {
      ...campaignDetails,
      details: {
        ...campaignDetails?.details,
        ai_assisted_product_usps: selectedUsps,
      },
    };

    saveCampaignDetails(updatedCampaignDetails, OnboardingStepIds.PRODUCT_USPS);
  };

  const onReset = () => {
    setUsps([]);
    void productUspsAndBannerElementsResponse.refetch();
  };

  return (
    <div className="px-4 pt-4 pb-4 flex flex-col flex-1 h-full">
      <div className="flex flex-col h-full flex-1 overflow-y-scroll no-scrollbar pb-3">
        <h1 className="text-xl font-medium text-gray-900">
          Enter key benefits for your product
        </h1>
        <p className="text-sm text-gray-600 mt-2">
          Not sure what to add? A USP is a unique benefit that highlights why
          your product is better.
        </p>

        <div className="mt-6 flex-1 flex flex-col">
          <h2 className="text-sm font-medium text-gray-800">
            AI Suggested USPs
          </h2>

          {productUspsAndBannerElementsResponse.isFetching ? (
            <div className="flex justify-center items-center flex-1">
              <SpinnerLoader size={40} borderWidth={4} />
            </div>
          ) : (
            <div className="mt-4 space-y-3">
              {usps.map((usp, index) => (
                <div
                  key={index}
                  className="border border-gray-medium rounded-lg p-3 flex items-center"
                  onClick={() => toggleUspSelection(index)}
                >
                  <div className="mr-3">
                    <div className="custom-radio-button-container">
                      <input
                        type="checkbox"
                        id={`usp-${index}`}
                        checked={usp.selected}
                        onChange={() => toggleUspSelection(index)}
                        className="cursor-pointer"
                      />
                      <span className="checkmark"></span>
                    </div>
                  </div>
                  <label
                    htmlFor={`usp-${index}`}
                    className="flex-1 cursor-pointer text-sm"
                  >
                    {usp.value}
                  </label>
                </div>
              ))}

              {showAddCustomUsp ? (
                <div className="border border-dashed border-primary rounded-lg p-3">
                  <input
                    type="text"
                    value={customUsp}
                    onChange={(e) => setCustomUsp(e.target.value)}
                    placeholder="Enter your custom USP"
                    className="w-full outline-none text-sm"
                    ref={customUspInputRef}
                  />
                  <div className="flex justify-end mt-2">
                    <button
                      type="button"
                      className="text-sm text-gray-500 mr-3 hover:text-red"
                      onClick={() => setShowAddCustomUsp(false)}
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      className="text-sm text-primary font-medium"
                      onClick={addCustomUsp}
                      disabled={!customUsp.trim()}
                    >
                      Add
                    </button>
                  </div>
                </div>
              ) : (
                <div
                  className="border border-dashed border-gray-medium hover:border-primary bg-primary/5 rounded-lg p-3 flex items-center justify-center cursor-pointer"
                  onClick={() => setShowAddCustomUsp(true)}
                >
                  <div className="flex items-center">
                    <BiPlus className="mr-2 w-5 h-5" />
                    <span className="text-sm text-gray-700 font-medium">
                      Add USPs
                    </span>
                  </div>
                </div>
              )}

              <p className="text-sm text-gray-500 mt-4">
                Select at least 3 benefits for better result
              </p>
            </div>
          )}
        </div>
      </div>

      <Button
        onClick={onSaveUspsClick}
        className="mt-3"
        disabled={getSelectedUspsCount() < 3}
      >
        <p>Next</p>
      </Button>
      <p
        className="text-center text-sm text-hyperlink mt-2 cursor-pointer"
        onClick={onReset}
      >
        Reset
      </p>
    </div>
  );
};

export default ProductUspsComp;
