import { useState } from 'react';
import classNames from 'classnames';
import { AD_FORMATS, QueryParams } from 'src/constants';
import { IGroweasyUser } from 'src/types';
import { ICampaign } from 'src/types/campaigns';
import Button from '@/components/lib/Button';
import { useQuery } from 'react-query';
import { getAdPreview } from 'src/actions/onboarding';
import { useRouter } from 'next/router';
import { getCommonHeaders } from 'src/actions';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import SpinnerLoader from '@/components/lib/SpinnerLoader';

interface IAdPreviewContainerProps {
  campaignDetails: ICampaign;
  user: IGroweasyUser;
  onCtaClick: () => void;
}

const AdPreviewContainer = (props: IAdPreviewContainerProps) => {
  const { campaignDetails, user, onCtaClick } = props;

  const [selectedAdFormat, setSelectedAdFormat] = useState(
    AD_FORMATS[0].format,
  );

  const router = useRouter();

  const adPreviewResponse = useQuery(
    ['getAdPreview', selectedAdFormat],
    () =>
      getAdPreview({
        queryParams: {
          ...router.query,
          [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
          [QueryParams.AD_FORMAT]: selectedAdFormat,
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'AdPreviewContainer.getAdPreview',
        );
      },
    },
  );

  return (
    <div className="px-4 pt-4 pb-8 flex flex-col flex-1 h-full">
      <div className="flex flex-col h-full overflow-hidden  flex-1">
        {adPreviewResponse.isFetching ? (
          <div className="flex flex-col items-center mt-3">
            <SpinnerLoader />
            <p className="mt-5">Generating Preview</p>
          </div>
        ) : (
          <div className="flex-1 overflow-y-scroll no-scrollbar relative">
            <div className="absolute top-0 left-0 w-full h-full z-0 flex flex-col items-center">
              <SpinnerLoader className="mt-3" />
              <p className="mt-5">Loading Preview</p>
            </div>
            <div
              dangerouslySetInnerHTML={{
                __html: adPreviewResponse.data?.data?.[0]?.body,
              }}
              className="relative z-10"
            ></div>
          </div>
        )}
      </div>
      <div className="mt-3 flex items-center rounded-lg bg-gray-light">
        {AD_FORMATS.map((item, index) => {
          const selected = item.format === selectedAdFormat;
          return (
            <div
              key={index}
              className={classNames(
                'flex-1 p-2 text-center cursor-pointer rounded-lg',
                {
                  'bg-gray-dark text-white': selected,
                },
              )}
              onClick={() => setSelectedAdFormat(item.format)}
            >
              <p className="text-xs">{item.label}</p>
            </div>
          );
        })}
      </div>
      <Button onClick={onCtaClick} className="mt-3">
        <p>Next</p>
      </Button>
    </div>
  );
};

export default AdPreviewContainer;
