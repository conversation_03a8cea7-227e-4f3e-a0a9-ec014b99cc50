import { useState, useEffect, useRef } from 'react';
import classNames from 'classnames';
import { MIN_DAILY_BUDGET } from 'src/constants';
import BottomSheet from '../../lib/BottomSheet';
import Button from '../../lib/Button';
import { getCurrencySymbol } from 'src/utils';
import { Currency } from 'src/types/campaigns';

interface ICustomBudgetPlanProps {
  noOfDays: number;
  onNoOfDaysChange: (noOfDays: number) => void;
  dailyBudget: number;
  onDailyBudgetChange: (dailyBudget: number) => void;
  onClose: () => void;
  currency: Currency;
}

const CustomBudgetPlan = ({
  noOfDays,
  onNoOfDaysChange,
  dailyBudget,
  onDailyBudgetChange,
  onClose,
  currency,
}: ICustomBudgetPlanProps) => {
  const minDailyBudget = MIN_DAILY_BUDGET[currency];
  const currencySymbol = getCurrencySymbol(currency);
  const minNoOfDays = 3;

  const [localNoOfDays, setLocalNoOfDays] = useState(noOfDays);
  const [localDailyBudget, setLocalDailyBudget] = useState(dailyBudget);

  useEffect(() => {
    setLocalNoOfDays(noOfDays);
    setLocalDailyBudget(dailyBudget);
  }, [noOfDays, dailyBudget]);

  const isNoOfDaysValid = localNoOfDays >= minNoOfDays && localNoOfDays < 50;
  const isBudgetValid = localDailyBudget >= minDailyBudget;

  const totalCost =
    isBudgetValid && isNoOfDaysValid ? localDailyBudget * localNoOfDays : 0;

  const noOfDaysInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    noOfDaysInputRef.current?.focus();
  }, []);

  const handleSave = () => {
    if (isNoOfDaysValid && isBudgetValid) {
      onNoOfDaysChange(localNoOfDays);
      onDailyBudgetChange(localDailyBudget);
      onClose();
    }
  };

  return (
    <BottomSheet onClose={onClose}>
      <div className="flex flex-col py-4 px-2">
        <h3 className="text-base font-medium mb-1">Custom Plan</h3>
        <p className="text-xs text-gray-600 mb-4">
          Create your own custom campaign budget
        </p>

        <label className="text-xs font-medium text-gray-dark mb-1">
          Daily spend limit in {currency} (Min {currencySymbol}
          {minDailyBudget})
        </label>
        <input
          className={classNames(
            'w-full outline-none border rounded-md px-3 py-2 text-sm appearance-none',
            {
              'border-red/50': localDailyBudget < minDailyBudget,
              'border-gray-300': localDailyBudget >= minDailyBudget,
            },
          )}
          type="number"
          inputMode="numeric"
          pattern="[0-9]*"
          min={minDailyBudget}
          onChange={(e) => setLocalDailyBudget(parseInt(e.target.value) || 0)}
          value={localDailyBudget || ''}
          placeholder={`${currencySymbol}${minDailyBudget}`}
        />

        <label className="text-xs font-medium text-gray-dark mt-6 mb-2">
          How many days would you like the campaign to run? (Min {minNoOfDays})
        </label>
        <div className="flex items-center">
          <button
            onClick={() => setLocalNoOfDays(Math.max(3, localNoOfDays - 1))}
            className="w-10 h-10 rounded-s-md border border-gray-300 text-xl font-semibold text-gray-700 active:scale-95 flex items-center justify-center"
          >
            −
          </button>
          <div className="text-center flex-1 text-base font-medium border-y border-gray-300 h-10 flex items-center justify-center">
            <input
              ref={noOfDaysInputRef}
              type="number"
              value={localNoOfDays}
              className="w-10 mx-auto outline-none"
              onChange={(e) => setLocalNoOfDays(parseInt(e.target.value))}
            />
          </div>
          <button
            onClick={() => setLocalNoOfDays(localNoOfDays + 1)}
            className="w-10 h-10 rounded-e-md border border-gray-300 text-xl font-semibold text-gray-700 active:scale-95 flex items-center justify-center"
          >
            +
          </button>
        </div>

        <div className="mt-6 text-sm text-gray-700 font-medium">
          Total cost:{' '}
          <span className="text-black font-semibold">
            {currencySymbol}
            {totalCost}
          </span>
        </div>

        <Button
          onClick={handleSave}
          disabled={!isNoOfDaysValid || !isBudgetValid}
          className="mt-5 w-full"
        >
          Save
        </Button>
      </div>
    </BottomSheet>
  );
};

export default CustomBudgetPlan;
