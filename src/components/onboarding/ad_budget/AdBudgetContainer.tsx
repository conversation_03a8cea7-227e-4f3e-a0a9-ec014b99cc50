import { useState, useEffect } from 'react';
import {
  CURRENCY_TO_LOCALE,
  DIAL_CODE_TO_CURRENCY_MAP,
  GLOBALS,
  MIN_DAILY_BUDGET,
  OnboardingStepIds,
} from 'src/constants';
import { EVENT_NAMES } from 'src/constants/events';
import { logEvent } from 'src/modules/firebase';
import { GrowEasyPartners, IPartnerConfig } from 'src/types';
import {
  Currency,
  GROWEASY_CAMPAIGN_TYPE,
  ICampaign,
  ICurrencyBudget,
} from 'src/types/campaigns';
import {
  getCurrencySymbol,
  getDateDifferenceInDays,
  getFormattedDateString,
} from 'src/utils';
import Button from '../../lib/Button';
import CustomBudgetPlan from './CustomBudgetPlanBs';
import Dropdown from '@/components/lib/Dropdown';

interface IAdBudgetContainerProps {
  campaignDetails: ICampaign;
  saveCampaignDetails: (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => void;
  partnerConfig?: IPartnerConfig;
}

const INR_BUDGET_PLANS = [
  {
    noOfDays: 3,
    desc: 'Great for quick lead generation and initial traction',
    dailyBudget: MIN_DAILY_BUDGET.INR,
  },
  {
    noOfDays: 7,
    dailyBudget: 714.3,
    desc: 'Balanced duration for building brand visibility and leads',

    tag: {
      name: 'Popular',
      background: '#D95D41',
    },
  },
  {
    noOfDays: 30,
    dailyBudget: MIN_DAILY_BUDGET.INR,
    desc: 'Comprehensive campaign for high-converting performance',
    tag: {
      name: 'Recommended',
      background: '#8C4272',
    },
  },
];

const USD_BUDGET_PLANS = [
  {
    noOfDays: 3,
    desc: 'Quick campaign to drive immediate visibility and leads',
    dailyBudget: MIN_DAILY_BUDGET.USD,
    /*tag: {
      name: 'Popular',
      background: '#D95D41',
    },*/
  },
  {
    noOfDays: 15,
    dailyBudget: MIN_DAILY_BUDGET.USD,
    desc: 'Full-cycle campaign for maximum reach and conversion impact',
    tag: {
      name: 'Recommended',
      background: '#8C4272',
    },
  },
];

const IDR_BUDGET_PLANS = [
  {
    noOfDays: 3,
    desc: 'Cocok untuk pengujian cepat konten iklan dan audiens',
    dailyBudget: MIN_DAILY_BUDGET.IDR,
  },
  {
    noOfDays: 7,
    desc: 'Kampanye cepat untuk visibilitas dan prospek instan',
    dailyBudget: MIN_DAILY_BUDGET.IDR,
  },
];

// generic Budget Plans for south-east Asia
const generateBudgetPlans = (currencyKey: Currency) => {
  const dailyBudget = MIN_DAILY_BUDGET[currencyKey];
  return [
    {
      noOfDays: 3,
      dailyBudget,
      desc: 'Perfect for testing ideas or short-term promotions',
    },
    {
      noOfDays: 5,
      dailyBudget,
      desc: 'Balanced duration for building visibility and lead flow',
      tag: { name: 'Popular', background: '#D95D41' },
    },
    {
      noOfDays: 10,
      dailyBudget,
      desc: 'Ideal for consistent lead generation and audience engagement',
    },
    {
      noOfDays: 15,
      dailyBudget,
      desc: 'Full-funnel campaign for maximum reach and conversions',
      tag: { name: 'Recommended', background: '#8C4272' },
    },
  ];
};

const BUDGET_PLANS_BY_CURRENCY: Record<
  Currency,
  ReturnType<typeof generateBudgetPlans>
> = {
  [Currency.INR]: INR_BUDGET_PLANS,
  [Currency.USD]: USD_BUDGET_PLANS,
  [Currency.IDR]: IDR_BUDGET_PLANS,
  [Currency.PHP]: generateBudgetPlans(Currency.PHP),
  [Currency.THB]: generateBudgetPlans(Currency.THB),
  [Currency.VND]: generateBudgetPlans(Currency.VND),
  [Currency.MYR]: generateBudgetPlans(Currency.MYR),
};

const AdBudgetContainer = (props: IAdBudgetContainerProps) => {
  const { campaignDetails, saveCampaignDetails, partnerConfig } = props;

  const [dailyBudget, setDailyBudget] = useState(0);
  const [noOfDays, setNoOfDays] = useState(0);
  const [customPlanSelected, setCustomPlanSelected] = useState(false);
  const [customPlanBsVisible, setCustomPlanBsVisible] = useState(false);
  const [currency, setCurrency] = useState<Currency>(() => {
    const defaultCurrency =
      campaignDetails?.details?.config?.self_ad_account_configs?.currency ??
      campaignDetails?.details?.budget_and_scheduling?.currency;
    const dialCode = GLOBALS?.userProfile?.mobile_dial_code;

    if (defaultCurrency) return defaultCurrency;

    return DIAL_CODE_TO_CURRENCY_MAP[dialCode] ?? Currency.INR; // fallback to INR
  });

  useEffect(() => {
    if (campaignDetails?.details?.budget_and_scheduling?.end_time) {
      const endDate = new Date(
        campaignDetails?.details?.budget_and_scheduling?.end_time,
      );
      const currentDate = new Date();
      const daysDifference = getDateDifferenceInDays(currentDate, endDate);
      if (daysDifference > 0) {
        setNoOfDays(daysDifference);
      }
    }
    if (campaignDetails?.details?.budget_and_scheduling?.currency) {
      setCurrency(campaignDetails?.details?.budget_and_scheduling?.currency);
    }
  }, [campaignDetails]);

  useEffect(() => {
    const campaignBudgetAndScheduling =
      campaignDetails?.details?.budget_and_scheduling;
    if (!campaignBudgetAndScheduling) {
      return;
    }
    // set daily budget as per currency
    const getDailyBudget = () => {
      const currencyBudgetMap: Record<Currency, number> = {
        [Currency.INR]: campaignBudgetAndScheduling.daily_budget ?? 0,
        [Currency.USD]: campaignBudgetAndScheduling.usd?.daily_budget ?? 0,
        [Currency.IDR]: campaignBudgetAndScheduling.idr?.daily_budget ?? 0,
        [Currency.MYR]: campaignBudgetAndScheduling.myr?.daily_budget ?? 0,
        [Currency.PHP]: campaignBudgetAndScheduling.php?.daily_budget ?? 0,
        [Currency.THB]: campaignBudgetAndScheduling.thb?.daily_budget ?? 0,
        [Currency.VND]: campaignBudgetAndScheduling.vnd?.daily_budget ?? 0,
      };

      const value = currencyBudgetMap[currency] || 0;

      // Currencies without subunits
      const zeroDecimalCurrencies = [Currency.IDR, Currency.VND];
      return zeroDecimalCurrencies.includes(currency) ? value : value / 100;
    };

    setDailyBudget(getDailyBudget());
  }, [currency, campaignDetails]);

  const startDate = new Date();
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + noOfDays);

  const onSaveDetailsClick = () => {
    logEvent(EVENT_NAMES.ad_budget_save_clicked);

    const lifetimeBudget = Math.round(dailyBudget * noOfDays);

    const isZeroDecimal = [Currency.IDR, Currency.VND].includes(currency);
    const multiplier = isZeroDecimal ? 1 : 100;

    const updatedBudgetAndSchedulingNode = {
      ...campaignDetails?.details?.budget_and_scheduling,
      currency,
      start_time: startDate.toISOString(),
      end_time: endDate.toISOString(),
      daily_budget: currency === Currency.INR ? dailyBudget * 100 : undefined,
      lifetime_budget:
        currency === Currency.INR ? lifetimeBudget * 100 : undefined,
      [currency.toLowerCase()]:
        currency !== Currency.INR
          ? ({
              ...campaignDetails?.details?.budget_and_scheduling?.[
                currency.toLowerCase()
              ],
              daily_budget: dailyBudget * multiplier,
              lifetime_budget: lifetimeBudget * multiplier,
            } as ICurrencyBudget)
          : undefined,
    };

    saveCampaignDetails(
      {
        ...campaignDetails,
        details: {
          ...campaignDetails.details,
          budget_and_scheduling: updatedBudgetAndSchedulingNode,
        },
      },
      OnboardingStepIds.AD_BUDGET,
    );
  };

  const renderPackageTag = (tag?: { name: string; background: string }) => {
    return (
      <div>
        {tag ? (
          <div className="text-xs md:text-sm font-medium text-primary text-center bg-white border-2 border-primary rounded-2xl absolute -top-3 right-2 z-20 overflow-hidden">
            <div className="bg-primary/10 py-1 px-3">{tag.name}</div>
          </div>
        ) : null}
      </div>
    );
  };

  const isNoOfDaysValid = noOfDays > 0 && noOfDays < 365;
  let ctaDisabled = !noOfDays || !isNoOfDaysValid;
  if (!dailyBudget || dailyBudget < MIN_DAILY_BUDGET[currency]) {
    ctaDisabled = true;
  }
  // exception for 5K INR package
  if (currency === Currency.INR && Math.round(dailyBudget) === 714) {
    ctaDisabled = false;
  }

  const campaignType =
    campaignDetails?.type ?? GROWEASY_CAMPAIGN_TYPE.LEAD_FORM;

  return (
    <div className="px-4 pt-4 pb-5 flex flex-col flex-1 h-full">
      <h2 className="md:text-xl tracking-tight font-semibold text-primary">
        Select your marketing plan :
      </h2>
      <p className="text-xs md:text-base mt-1 text-gray-dark">
        Choose the perfect budget for your ad campaign
      </p>
      <div className="bg-white hover:border-primary/80 border border-primary/30 rounded-xl pl-4 px-2 py-1 first:mt-0 flex items-center shadow mt-3 mx-1">
        <p className="text-xs md:text-sm font-medium mr-auto">Currency:</p>
        <Dropdown
          value={currency}
          onChange={(value) => setCurrency(value as Currency)}
          className="!mt-0 !w-auto cursor-pointer text-sm"
          dropdownClassName="bg-white px-12 py-1"
          disabled={
            !!campaignDetails?.details?.config?.self_ad_account_configs
              ?.currency
          }
        >
          {Object.keys(Currency).map((curr) => {
            // Disable south-east Asian currencies for non AdGlobalAi
            if (
              ![Currency.INR, Currency.USD].includes(curr as Currency) &&
              partnerConfig?.partner !== GrowEasyPartners.AD_GLOBAL_AI
            ) {
              return null;
            }
            return (
              <option key={curr} value={curr}>
                {curr} ({getCurrencySymbol(curr as Currency)})
              </option>
            );
          })}
        </Dropdown>
      </div>
      <div className="mt-2 p-1 overflow-y-scroll no-scrollbar flex-1">
        <>
          <div className="space-y-4">
            {BUDGET_PLANS_BY_CURRENCY[currency]?.map((item, index) => {
              // only for INR
              const strikethrougBudget =
                currency === Currency.INR
                  ? (item.dailyBudget + 250) * item.noOfDays
                  : 0;
              const selected =
                item.noOfDays === noOfDays && item.dailyBudget === dailyBudget;
              return (
                <div
                  key={index}
                  className="bg-white hover:border-primary/80 border border-primary/30 rounded-xl p-4 cursor-pointer first:mt-0 flex items-center shadow-md relative"
                  onClick={() => {
                    setCustomPlanSelected(false);
                    setNoOfDays(item.noOfDays);
                    setDailyBudget(item.dailyBudget);
                  }}
                >
                  <div className="w-4 h-4 rounded-full border border-primary/60 mr-3 flex items-center justify-center">
                    {selected && !customPlanSelected ? (
                      <div className="w-2 h-2 rounded-full bg-primary" />
                    ) : null}
                  </div>
                  <div className="">
                    <p className="text-base font-medium tracking-tight">
                      Get{' '}
                      {campaignType === GROWEASY_CAMPAIGN_TYPE.META_SALES
                        ? 'Sales'
                        : 'Leads'}{' '}
                      for {item.noOfDays} days
                    </p>
                    <p className="text-xs text-gray-dark mb-2">{item.desc}</p>
                    <p className="text-sm mt-1 font-medium">
                      {strikethrougBudget ? (
                        <span className="mr-2 text-gray-dark/70 line-through">
                          {Math.round(strikethrougBudget).toLocaleString(
                            'en-IN',
                          )}
                        </span>
                      ) : null}
                      {getCurrencySymbol(currency)}
                      {Math.round(
                        item.noOfDays * item.dailyBudget,
                      ).toLocaleString(CURRENCY_TO_LOCALE[currency])}
                    </p>
                  </div>
                  {renderPackageTag(item.tag)}
                </div>
              );
            })}
          </div>
          <div
            className="bg-white hover:border-primary/80 border border-primary/30 rounded-xl p-4 cursor-pointer first:mt-0 flex items-center shadow-md relative mt-4"
            onClick={() => {
              setCustomPlanSelected(true);
              setCustomPlanBsVisible(true);
            }}
          >
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full border border-primary/60 mr-3 flex items-center justify-center">
                {customPlanSelected ? (
                  <div className="w-2 h-2 rounded-full bg-primary" />
                ) : null}
              </div>
              <div>
                <p className="text-base font-medium tracking-tight">Custom</p>
                <p className="text-xs text-gray-dark">Create your own plan</p>
              </div>
            </div>
          </div>
        </>
      </div>
      <div className="border-t">
        {dailyBudget > 0 && noOfDays > 0 ? (
          <p className="mt-5 text-xs">
            You will spend{' '}
            <span className="font-medium text-primary">
              {getCurrencySymbol(currency)}
              {Math.round(dailyBudget)}
            </span>{' '}
            daily and{' '}
            <span className="font-medium text-primary">
              {getCurrencySymbol(currency)}
              {Math.round(dailyBudget * noOfDays).toLocaleString(
                CURRENCY_TO_LOCALE[currency],
              )}
            </span>{' '}
            total on your Ad
          </p>
        ) : null}
      </div>
      {noOfDays > 0 ? (
        <p className="mt-3 text-xs">
          Your campaign will end on{' '}
          <span className="font-medium">{getFormattedDateString(endDate)}</span>{' '}
        </p>
      ) : null}
      <Button
        onClick={onSaveDetailsClick}
        disabled={ctaDisabled}
        className="mt-5"
      >
        <p>Save</p>
      </Button>
      {customPlanBsVisible ? (
        <CustomBudgetPlan
          noOfDays={noOfDays}
          onNoOfDaysChange={(days) => setNoOfDays(days)}
          dailyBudget={dailyBudget}
          onDailyBudgetChange={(budget) => {
            setDailyBudget(budget);
          }}
          onClose={() => setCustomPlanBsVisible(false)}
          currency={currency}
        />
      ) : null}
    </div>
  );
};

export default AdBudgetContainer;
