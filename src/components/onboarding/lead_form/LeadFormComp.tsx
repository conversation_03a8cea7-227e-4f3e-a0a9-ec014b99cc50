import Button from '@/components/lib/Button';
import { useEffect, useState } from 'react';
import { OnboardingStepIds } from 'src/constants';
import { AdPlatforms, IGroweasyUser } from 'src/types';
import { ICampaign, ILeadgenFormQuestion } from 'src/types/campaigns';
import CrossIcon from '@/images/common/cross-circular.svg';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import classNames from 'classnames';
import {
  getUnderscoreSeparatedBaseDomain,
  isValidURL,
  openUrlInNewTab,
} from 'src/utils';
import { HiExternalLink } from 'react-icons/hi';

interface ILeadFormCompProps {
  campaignDetails: ICampaign;
  saveCampaignDetails: (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => void;
  user: IGroweasyUser;
}

/*const GOOGLE_LANDING_PAGE_OPTIONS = [
  {
    title: 'GrowEasy-hosted landing page',
    subtitle: 'No setup required',
    groweasyHosted: true,
  },
  {
    title: 'Your custom landing page',
    subtitle: "You'll need to add a tracking snippet",
    groweasyHosted: false,
  },
];*/

// question1, 2, & 3 are reserved for FULL_NAME, EMAIL, PHONE (even if they are not being used)
const LeadFormComp = (props: ILeadFormCompProps) => {
  const { campaignDetails, saveCampaignDetails } = props;

  const [leadQuestions, setLeadQuestions] = useState<ILeadgenFormQuestion[]>(
    [],
  );
  // for google only
  const [groweasyHostedPageSelected, setGrowEasyHostedPageSelected] =
    useState(true);
  const [customLandingPageUrl, setCustomLandingPageUrl] = useState('');

  useEffect(() => {
    if (campaignDetails?.details?.leadgen_form) {
      const questions =
        campaignDetails?.details?.leadgen_form?.questions.filter(
          (item) => item.type === 'CUSTOM',
        );
      setLeadQuestions(questions);
    }
    if (campaignDetails?.platform === AdPlatforms.GOOGLE) {
      setCustomLandingPageUrl(
        campaignDetails?.google_ads_data?.lead_form_url ?? '',
      );
      setGrowEasyHostedPageSelected(false);
      /*if (
        !campaignDetails?.google_ads_data?.lead_form_url ||
        campaignDetails?.google_ads_data?.lead_form_url?.includes(
          'connectform.co/lead-forms',
        )
      ) {
        setGrowEasyHostedPageSelected(true);
      } else {
        setCustomLandingPageUrl(
          campaignDetails?.google_ads_data?.lead_form_url,
        );
        setGrowEasyHostedPageSelected(false);
      }*/
    }
  }, [campaignDetails]);

  const onQuestionsChange = (key: string, label: string) => {
    const targetQuestion = leadQuestions.find((item) => item.key === key);
    targetQuestion.label = label;
    delete targetQuestion.options; // editing an MCQ will make it free text

    setLeadQuestions(
      leadQuestions.map((item) => {
        if (item.key === key) {
          return {
            ...targetQuestion,
          };
        }
        return item;
      }),
    );
  };

  const onQuestionRemove = (key: string) => {
    setLeadQuestions(leadQuestions.filter((item) => item.key !== key));
  };

  const onAddMoreClick = () => {
    setLeadQuestions([
      ...leadQuestions,
      {
        type: 'CUSTOM',
        key: `question_${Date.now()}`,
        label: '',
      },
    ]);
  };

  const onSaveDetailsClick = () => {
    logEvent(EVENT_NAMES.lead_form_save_clicked);
    const updatedCampaignDetails: ICampaign = {
      ...campaignDetails,
      details: {
        ...campaignDetails?.details,
        leadgen_form: {
          ...campaignDetails?.details?.leadgen_form,
          questions: [
            ...(campaignDetails?.details?.leadgen_form?.questions?.filter(
              (item) => item.type !== 'CUSTOM',
            ) ?? []),
            ...leadQuestions,
          ],
        },
      },
    };
    if (
      !groweasyHostedPageSelected &&
      customLandingPageUrl &&
      updatedCampaignDetails.google_ads_data
    ) {
      updatedCampaignDetails.google_ads_data.lead_form_url =
        customLandingPageUrl;
      updatedCampaignDetails.details.config.google_custom_conversion_action_doc_id = `${getUnderscoreSeparatedBaseDomain(
        customLandingPageUrl,
      )}_lead`;
    } else {
      // deleting will trigger setting default lead_form_url in BE
      delete updatedCampaignDetails.google_ads_data.lead_form_url;
      delete updatedCampaignDetails.details.config
        .google_custom_conversion_action_doc_id;
    }
    saveCampaignDetails(updatedCampaignDetails, OnboardingStepIds.LEAD_FORM);
  };

  const isCustomLandingPageUrlValid =
    customLandingPageUrl &&
    isValidURL(customLandingPageUrl) &&
    customLandingPageUrl.startsWith('http'); // to fix google api error: The protocol (http:// or https://) is missing.

  const ctaDisabled = groweasyHostedPageSelected
    ? leadQuestions.some((item) => !item.label)
    : !isCustomLandingPageUrlValid;

  return (
    <div className="px-4 pt-4 pb-4 flex flex-col flex-1 h-full">
      <div className="flex-1 overflow-y-scroll h-full no-scrollbar">
        {/*campaignDetails.platform === AdPlatforms.GOOGLE ? (
          <div>
            <p className="text-sm">Where should users be redirected?</p>
            {GOOGLE_LANDING_PAGE_OPTIONS.map((item, index) => {
              const selected =
                item.groweasyHosted === groweasyHostedPageSelected;

              return (
                <div
                  key={index}
                  className="bg-white mt-3 rounded-lg px-3 cursor-pointer first:mt-0"
                  onClick={() => {
                    setGrowEasyHostedPageSelected(item.groweasyHosted);
                  }}
                >
                  <div className="flex items-center">
                    <div className="w-4 h-4 rounded-full border border-gray-light mr-3 flex items-center justify-center">
                      {selected ? (
                        <div className="w-2 h-2 rounded-full bg-primary" />
                      ) : null}
                    </div>
                    <div>
                      <p className="text-xs">{item.title}</p>
                      <p className="text-xxs text-gray-dark">{item.subtitle}</p>
                    </div>
                  </div>
                </div>
              );
            })}
            <hr className="my-8" />
          </div>
        ) : null*/}
        {groweasyHostedPageSelected ? (
          <div>
            <p className="text-sm text-black">Lead Qualifying Questions</p>
            <p className="text-xs text-gray-dark !font-medium mt-2">
              Apart from name, email, and contact information, we will also be
              asking the following questions to Ad respondents
            </p>
            {leadQuestions.map((item, index) => {
              return (
                <div key={index}>
                  <div className="flex items-center mt-5">
                    <p className="text-xs mr-2">
                      Question {index + 1}{' '}
                      <span className="text-xxs">
                        {item.options?.length ? '(MCQ)' : '(Free Text)'}
                      </span>
                    </p>
                    <CrossIcon
                      className="text-sm text-gray-dark cursor-pointer"
                      onClick={() => {
                        onQuestionRemove(item.key);
                      }}
                    />
                  </div>
                  <div className="mt-3">
                    <textarea
                      rows={3}
                      value={item.label}
                      onChange={(event) =>
                        onQuestionsChange(item.key, event.target.value)
                      }
                      className="outline-none border border-gray-medium rounded-lg px-2 py-2 text-sm w-full"
                    />
                    {item.options?.length ? (
                      <div className="flex items-center flex-wrap">
                        {item.options.map((option, innerIndex) => {
                          return (
                            <div
                              key={innerIndex}
                              className="mr-2 my-1 py-0.5 px-1 rounded border border-gray-medium"
                            >
                              <p className="text-xxs text-gray-dark">
                                {option?.value}
                              </p>
                            </div>
                          );
                        })}
                      </div>
                    ) : null}
                  </div>
                </div>
              );
            })}
            {leadQuestions.length < 2 ? (
              <p
                className={classNames(
                  'text-xs text-hyperlink text-center mt-2 font-medium cursor-pointer',
                  {
                    'mt-40': leadQuestions.length === 0,
                  },
                )}
                onClick={onAddMoreClick}
              >
                {leadQuestions.length === 0
                  ? '+ Add a lead qualifying question'
                  : '+ Add More'}
              </p>
            ) : null}
          </div>
        ) : (
          <div>
            <p className="text-sm text-black">Enter Your Landing Page URL</p>
            <div className="mt-3 flex flex-col">
              <input
                className={classNames(
                  'outline-none border border-gray-medium rounded-lg px-2 py-2 text-sm w-full',
                )}
                type="text"
                onChange={(event) =>
                  setCustomLandingPageUrl(event.target.value)
                }
                value={customLandingPageUrl}
                placeholder="Make sure that URL is valid and working."
              />
              {isCustomLandingPageUrlValid ? (
                <div onClick={() => openUrlInNewTab(customLandingPageUrl)}>
                  <p className="text-xxs text-hyperlink font-medium cursor-pointer">
                    <span className="inline-block">Preview URL </span>
                    <span>
                      <HiExternalLink className="inline-block ml-0.5 text-xs w-3 -mt-0.5" />
                    </span>
                  </p>
                </div>
              ) : null}
              {!isCustomLandingPageUrlValid ? (
                <p className="text-red text-xxs">
                  Please enter valid URL starting with http or https
                </p>
              ) : null}
              <p className="text-xxs text-gray-dark mt-2">
                We’ll email you the snippet to add to this page after launch.
              </p>
            </div>
          </div>
        )}
      </div>
      {groweasyHostedPageSelected ? (
        <div>
          <p className="mt-4 text-xxs text-red">
            Asking more questions in your form may reduce the number of leads,
            as some people might not complete it. However, if you don’t include
            any questions, you could end up with more leads, but many of them
            might not be relevant to your business.
          </p>
          <p className="mt-1 text-xxs text-primary">
            We recommend to keep at least 1 question
          </p>
        </div>
      ) : null}
      <Button
        onClick={onSaveDetailsClick}
        className="mt-4"
        disabled={ctaDisabled}
      >
        <p>Save</p>
      </Button>
    </div>
  );
};

export default LeadFormComp;
