import { useState, useEffect, useRef, ChangeEventHandler } from 'react';
import { GL<PERSON>BALS, OnboardingStepIds, QueryParams } from 'src/constants';
import { EVENT_NAMES } from 'src/constants/events';
import { logEvent } from 'src/modules/firebase';
import { GROWEASY_CAMPAIGN_TYPE, ICampaign } from 'src/types/campaigns';
import Button from '../../lib/Button';
import { HiExternalLink } from 'react-icons/hi';
import Tooltip from '@/components/lib/Tooltip';
import classNames from 'classnames';
import {
  getCloudflareSquareUrl,
  getUnderscoreSeparatedBaseDomain,
  isValidURL,
  logApiErrorAndShowToastMessage,
  openUrlInNewTab,
} from 'src/utils';
import DownArrow from '@/images/common/down-arrow.svg';
import SearchCategoryModal from './SearchCategoryModal';
import { useRouter } from 'next/router';
import { Ad<PERSON>lat<PERSON>s, IGroweasyUser } from 'src/types';
import { useMutation } from 'react-query';
import { uploadFileAndGetS3Url } from 'src/actions/campaign_details';
import { getCommonHeaders } from 'src/actions';
import { showToastMessage } from 'src/modules/toast';
import Image from 'next/image';
import UploadIcon from '@/images/icons/upload-icon.svg';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import CountryCodeDropdown from '@/components/lib/CountryCodeDropdown';

interface IBusinessDetailsCompProps {
  campaignDetails: ICampaign;
  saveCampaignDetails: (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => void;
  categories: string[];
  user: IGroweasyUser;
}

const BusinessDetailsComp = (props: IBusinessDetailsCompProps) => {
  const { campaignDetails, saveCampaignDetails, categories, user } = props;

  const [category, setCategory] = useState('');
  const [description, setDescription] = useState('');
  const [offersOrUsp, setOffersOrUsp] = useState('');
  const [website, setWebsite] = useState('');
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [countryCode, setCountryCode] = useState(
    GLOBALS.userProfile?.mobile_dial_code || '+91',
  );
  const [mobileWithoutCountryCode, setMobileWithoutCountryCode] = useState(
    GLOBALS.userProfile?.mobile,
  );

  const [businessName, setBusinessName] = useState('');
  const [businessSquareLogoUrl, setBusinessSquareLogoUrl] = useState('');
  const [logoDimensions, setLogoDimensions] = useState({ width: 0, height: 0 });
  const [logoFile, setLogoFile] = useState<File | null>(null);

  const uploadImageInputRef = useRef<HTMLInputElement>();

  const uploadLogoImageMutation = useMutation(uploadFileAndGetS3Url);

  const router = useRouter();
  const adPlatform =
    (router.query[QueryParams.PLATFORM] as AdPlatforms) ?? AdPlatforms.META;
  const campaignType =
    (router.query[QueryParams.TYPE] as GROWEASY_CAMPAIGN_TYPE) ??
    GROWEASY_CAMPAIGN_TYPE.LEAD_FORM;

  useEffect(() => {
    setCategory(
      campaignDetails?.details?.business_details?.business_category ?? '',
    );
    setDescription(
      campaignDetails?.details?.business_details
        ?.product_or_service_description ?? '',
    );
    setOffersOrUsp(
      campaignDetails?.details?.business_details
        ?.product_or_service_offers_or_usp ?? '',
    );
    setWebsite(campaignDetails?.details?.business_details?.website ?? '');

    if (campaignDetails.details?.business_details?.country_code) {
      setCountryCode(campaignDetails?.details.business_details?.country_code);
    }
    if (
      campaignDetails.details?.business_details?.mobile_without_country_code
    ) {
      setMobileWithoutCountryCode(
        campaignDetails.details?.business_details?.mobile_without_country_code,
      );
    }

    if (campaignDetails?.details?.business_details?.business_name) {
      setBusinessName(
        campaignDetails?.details?.business_details?.business_name,
      );
    }
    if (
      campaignDetails?.details?.business_details?.business_logo?.square?.url
    ) {
      setBusinessSquareLogoUrl(
        campaignDetails?.details?.business_details?.business_logo.square.url,
      );
    }
  }, [campaignDetails]);

  useEffect(() => {
    if (logoFile) {
      // upload to BE and get url
      uploadLogoImageMutation
        .mutateAsync({
          headers: getCommonHeaders(user),
          data: {
            file: logoFile,
          },
          queryParams: router.query as Record<string, string>,
        })
        .then((response) => {
          // construct a square url
          setBusinessSquareLogoUrl(
            getCloudflareSquareUrl(response.data.s3_url, {
              width: logoDimensions.width,
            }),
          );
        })
        .catch((error) => {
          logApiErrorAndShowToastMessage(
            error as Error,
            'BusinessDetailsComp.uploadLogoImageMutation',
          );
          // reset in case of error
          setLogoFile(null);
        });
    }
  }, [logoFile, logoDimensions]);

  const onUploadImageInputChange: ChangeEventHandler<HTMLInputElement> = (
    e,
  ) => {
    const file = e.target?.files?.[0];
    if (file) {
      // Check file size (less than 1 MB)
      const isValidSize = file.size < 1 * 1024 * 1024; // 1 MB in bytes

      if (!isValidSize) {
        showToastMessage('Logo size should be less than 1 MB', 'error');
        return;
      }
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new window.Image();
        img.onload = () => {
          setLogoDimensions({ width: img.width, height: img.height });
          setLogoFile(file);
        };
        img.src = e.target.result as string;
      };
      reader.readAsDataURL(file);
    }
    // Reset the input's value to allow selecting the same file again
    e.target.value = '';
  };

  const onSaveDetailsClick = () => {
    if (GLOBALS.userProfile?.is_affiliate_marketing) {
      // not firing this event because we don't want to signla Meta
      // we are running performance marketing on this event
    } else {
      logEvent(EVENT_NAMES.business_details_save_clicked);

      /* event for google search conversion */
      const eventLabel = 'AW-***********/ik8TCJCCy5YaEOvNvIEr';
      if (window?.gtag) {
        window.gtag('event', 'conversion', {
          send_to: eventLabel,
        });
      }
      /* Conversion event snippet ends here */
    }
    saveCampaignDetails(
      {
        ...campaignDetails,
        platform: adPlatform,
        type: campaignType,
        details: {
          ...campaignDetails.details,
          business_details: {
            business_category: category,
            product_or_service_description: description,
            product_or_service_offers_or_usp: offersOrUsp,
            website,
            mobile: `${countryCode}${mobileWithoutCountryCode}`,
            mobile_without_country_code: mobileWithoutCountryCode,
            country_code: countryCode,
            business_name: businessName,
            business_logo: {
              ...campaignDetails.details?.business_details?.business_logo,
              square: {
                url: businessSquareLogoUrl,
                width: logoDimensions.width,
                height: logoDimensions.width,
              },
            },
          },
          config: {
            ...campaignDetails.details?.config,
            meta_sales_purchase_event_name: `${getUnderscoreSeparatedBaseDomain(
              website,
            )}_purchase`,
            // this can be overridden while updating custom lead_form_url
            google_custom_conversion_action_doc_id: `${getUnderscoreSeparatedBaseDomain(
              website,
            )}${
              campaignType === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX
                ? '_purchase'
                : '_lead'
            }`,
          },
        },
      },
      OnboardingStepIds.BUSINESS_DETAILS,
    );
  };

  const isWebsiteValid = !website || isValidURL(website);
  const ctaDisabled =
    !category ||
    !description ||
    !isWebsiteValid ||
    (adPlatform === AdPlatforms.GOOGLE &&
      (!businessName || businessName.length > 25 || !businessSquareLogoUrl)) ||
    (campaignType === GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL &&
      !mobileWithoutCountryCode) ||
    ([
      GROWEASY_CAMPAIGN_TYPE.META_SALES,
      GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
    ].includes(campaignType) &&
      !website);

  return (
    <div className="px-4 pt-4 pb-4 flex flex-col flex-1 h-full">
      <div className="flex-1 overflow-y-scroll h-full no-scrollbar">
        <p className="text-sm text-black">
          How do you classify your business as?
        </p>
        <div
          className={`mt-3 border border-gray-medium rounded-lg p-2 w-full flex items-center `}
          onClick={() => setShowCategoryModal(true)}
        >
          <div className="flex-1">{category || 'Select'}</div>
          <div>
            <DownArrow width="24" height="24" className="text-gray-dark" />
          </div>
        </div>
        {showCategoryModal && (
          <SearchCategoryModal
            categories={categories}
            onChange={(value: string) => setCategory(value)}
            hideModal={() => setShowCategoryModal(false)}
          />
        )}

        {adPlatform === AdPlatforms.GOOGLE ? (
          <div>
            <p className="text-sm text-gray-dark mt-6">
              Business Name (Max 25 chars)
            </p>
            <div className="mt-3 flex">
              <input
                className={classNames(
                  'outline-none border border-gray-medium rounded-lg px-2 py-2 text-sm w-60',
                  {
                    'border-red': businessName.length > 25,
                  },
                )}
                type="text"
                onChange={(event) => setBusinessName(event.target.value)}
                value={businessName}
              />
            </div>
            <p className="text-sm text-gray-dark mt-6">
              <span className="mr-1">Business Logo</span>
              {campaignType === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX ? (
                <span>(Square, Min 128 x 128)</span>
              ) : (
                <span>(Square Preferred)</span>
              )}
            </p>
            <div
              className="mt-3"
              onClick={() => {
                uploadImageInputRef.current?.click();
              }}
            >
              {businessSquareLogoUrl ? (
                <div className="w-24 rounded-md cursor-pointer border border-gray-dark overflow-hidden">
                  <Image
                    src={businessSquareLogoUrl}
                    alt=""
                    width={96}
                    height={96}
                  />
                </div>
              ) : (
                <div className="w-24 h-24 flex items-center justify-center bg-gray-medium rounded-md cursor-pointer">
                  <UploadIcon className="text-black h-8 w-8" />
                </div>
              )}
            </div>
            <input
              type="file"
              ref={uploadImageInputRef}
              onChange={onUploadImageInputChange}
              className="hidden"
              accept="image/*"
            />
          </div>
        ) : null}
        <p className="text-sm text-black mt-6">
          What do you want to sell or promote?
        </p>
        <textarea
          className="w-full border border-gray-medium mt-3 rounded-lg outline-none p-2"
          rows={6}
          value={description}
          onChange={(event) => {
            setDescription(event.target.value);
          }}
          placeholder="Describe your product or service here in detail."
        ></textarea>
        {/* <p className="text-sm text-black mt-4">
          Do you have any offers or a Product USP that you want to highlight?
        </p>
        <textarea
          className="w-full border border-gray-medium mt-3 rounded-lg outline-none p-2 text-gray-dark"
          rows={3}
          value={offersOrUsp}
          onChange={(event) => {
            setOffersOrUsp(event.target.value);
          }}
          placeholder="Enter something like Limited Stocks, 50% limited time discount, etc"
        ></textarea> */}

        <div className="flex items-center mt-4">
          {campaignType === GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL ? (
            <div>
              <div className="flex items-center">
                <p className="text-sm text-black mr-1">Phone Number</p>
                <Tooltip title="Your leads will reach out to you on this number." />
              </div>
              <p className="text-xxs">
                Enter the phone number that you would like to show in your ad.
              </p>
            </div>
          ) : (
            <div>
              <div className="flex items-center">
                <p className="text-sm text-black mr-1">
                  WhatsApp Number (Recommended)
                </p>
                <Tooltip title="We will send you a WhatsApp notification when you receive a new lead." />
              </div>
              <p className="text-xxs">Enter your phone number</p>
            </div>
          )}
        </div>
        <div className="mt-3 flex gap-2">
          <CountryCodeDropdown
            countryCode={countryCode}
            setCountryCode={setCountryCode}
          />
          <input
            className="outline-none border border-gray-medium rounded-lg px-2 py-2 text-sm w-full"
            type="tel"
            onChange={(event) =>
              setMobileWithoutCountryCode(event.target.value)
            }
            placeholder="7206XXXXXX"
            value={mobileWithoutCountryCode}
          />
        </div>

        <div className="flex items-center mt-4">
          <p className="text-sm text-black mr-1">
            {[
              GROWEASY_CAMPAIGN_TYPE.META_SALES,
              GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
            ].includes(campaignType) ? (
              'Enter your Landing Page URL'
            ) : (
              <span>Enter your website (Optional)</span>
            )}
          </p>
          <Tooltip
            title={
              [
                GROWEASY_CAMPAIGN_TYPE.META_SALES,
                GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
              ].includes(campaignType)
                ? 'Customers will land on this page. We will provide Meta / Google snippets for event tracking setup.'
                : 'Customers can visit this after submitting their contact details. Keep it blank in case you do not have a Website.'
            }
          />
        </div>
        <div className="mt-3 flex flex-col">
          <input
            className={classNames(
              'outline-none border rounded-lg px-2 py-2 text-sm w-full',
              {
                'border-gray-medium': isWebsiteValid,
                'border-red': !isWebsiteValid,
              },
            )}
            type="text"
            onChange={(event) => setWebsite(event.target.value?.toLowerCase())}
            placeholder="https://example.com"
            value={website}
          />
          {!isWebsiteValid ? (
            <p className="text-red text-xxs">
              Please enter valid URL or keep it empty
            </p>
          ) : null}
          {[
            GROWEASY_CAMPAIGN_TYPE.META_SALES,
            GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
          ].includes(campaignType) ? (
            <div className="mt-1" onClick={() => openUrlInNewTab(website)}>
              <p className="text-xxs px-2 text-hyperlink font-medium cursor-pointer">
                Preview URL{' '}
                <span>
                  <HiExternalLink className="inline-block ml-0.5 text-xs w-3" />
                </span>
              </p>
            </div>
          ) : null}
        </div>
      </div>
      <Button
        onClick={onSaveDetailsClick}
        disabled={ctaDisabled}
        className="mt-4"
      >
        <p>Save</p>
      </Button>
      {uploadLogoImageMutation.isLoading ? <FullScreenLoader /> : null}
    </div>
  );
};

export default BusinessDetailsComp;
