import { useEffect, useState } from 'react';
import CrossIcon from '@/images/common/cross.svg';
import DownArrow from '@/images/common/down-arrow.svg';

interface IDropdownProps {
  categories: string[];
  onChange: (value: string) => void;
  hideModal: () => void;
}

const FEATURED_CATEGORIES = [
  'Advertising and Marketing',
  //'Health and Wellness',
  'Education and E-Learning',
  'Real Estate and Property Management',
  'Travel and Tourism',
];

const SearchCategoryModal = (props: IDropdownProps) => {
  const { categories, onChange, hideModal } = props;

  const [filteredCategories, setFilteredCategories] =
    useState<string[]>(categories);
  const [isTopCategoryVisible, setIsTopCategoryVisible] = useState(true);
  const [searchInput, setSearchInput] = useState('');

  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      const filtered = categories.filter((category) =>
        category.toLowerCase().includes(searchInput.trim().toLowerCase()),
      );
      setFilteredCategories(filtered);
    }, 200);

    return () => {
      clearTimeout(debounceTimeout);
    };
  }, [searchInput, categories]);

  const handleCategoryClick = (category: string) => {
    onChange(category);
    hideModal();
  };

  return (
    <div className="fixed top-0 left-0 h-full w-full backdrop-blur-sm z-10 flex justify-center">
      <div className="w-[425px] px-8 py-14">
        <div className="bg-white h-full p-4 rounded-lg w-full shadow-lg border-2 grid grid-rows-[auto,auto,auto,3fr]">
          {/* Search and close button section */}
          <div className="flex justify-between items-center gap-x-2 border rounded-lg px-3">
            <input
              className="outline-none py-2 text-sm w-full"
              type="text"
              placeholder="Enter to search"
              value={searchInput}
              onChange={(e) => {
                setSearchInput(e.target.value);
                setIsTopCategoryVisible(false);
              }}
            />
            <CrossIcon
              width="14"
              height="14"
              className="text-gray-dark cursor-pointer"
              onClick={hideModal}
            />
          </div>

          {/* Toggle featured categories section */}
          <div className="flex flex-col overflow-auto no-scrollbar mt-4">
            <div
              className="flex justify-between px-2 items-center cursor-pointer"
              onClick={() => setIsTopCategoryVisible((prev) => !prev)}
            >
              <p className="text-xs text-gray-medium select-none">
                Top Categories
              </p>

              <DownArrow
                width="16"
                height="16"
                className={`text-gray-dark transition-transform duration-150 ${
                  isTopCategoryVisible ? 'rotate-180' : 'rotate-0'
                }`}
              />
            </div>

            {isTopCategoryVisible && (
              <div className="overflow-auto max-h-[130px] no-scrollbar mt-2">
                {FEATURED_CATEGORIES.map((item, index) => (
                  <div
                    className="w-full py-1 hover:bg-gray-light cursor-pointer text-sm capitalize"
                    onClick={() => handleCategoryClick(item)}
                    key={index}
                  >
                    {item}
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="border-t my-4"></div>

          {/* Display filtered categories */}
          <div className="flex flex-col rounded-sm overflow-auto no-scrollbar">
            {filteredCategories.length > 0 ? (
              filteredCategories.map((item, index) => (
                <div
                  className="w-full py-1 hover:bg-gray-light cursor-pointer text-sm capitalize"
                  onClick={() => handleCategoryClick(item)}
                  key={index}
                >
                  {item}
                </div>
              ))
            ) : (
              <div className="text-sm text-center text-gray-dark select-none">
                No Categories Found!
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchCategoryModal;
