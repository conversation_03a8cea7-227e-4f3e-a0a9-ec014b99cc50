import Button from '@/components/lib/Button';
import Image from 'next/image';
import { ICampaign } from 'src/types/campaigns';

interface ISelectedAdBannersProps {
  images: Array<{
    permalink_url: string;
    height: number;
    width: number;
    hash: string;
  }>;
  onReset: () => void;
  onNextPress: () => void;
  campaignDetails: ICampaign;
}

const SelectedAdBanners = (props: ISelectedAdBannersProps) => {
  const { images, onReset, onNextPress, campaignDetails } = props;

  return (
    <div className="flex flex-col items-center mt-3 h-full">
      <div className="flex-1 overflow-y-scroll no-scrollbar">
        {images
          ?.filter((item) => {
            // hide hidden banners
            const adBanner = campaignDetails?.details?.ad_banners?.find(
              (banner) => banner.image?.hash === item.hash,
            );
            if (adBanner?.hidden) {
              return false;
            }
            return true;
          })
          ?.map((item, index) => {
            return (
              <div key={index} className="mt-2 relative">
                <div className="absolute top-0 left-0 w-full h-full z-0 flex items-center justify-center">
                  <div className="bg-gray-medium animate-pulse rounded-lg w-full h-full" />
                </div>
                <Image
                  src={item.permalink_url}
                  width={item.width}
                  height={item.height}
                  alt=""
                  className="relative rounded-lg z-10"
                />
              </div>
            );
          })}
      </div>
      <div className="w-full">
        <Button onClick={onNextPress} className="mt-3 w-full">
          <p>Next</p>
        </Button>
        <p
          className="text-center text-sm text-hyperlink mt-2 cursor-pointer"
          onClick={onReset}
        >
          Reset
        </p>
      </div>
    </div>
  );
};

export default SelectedAdBanners;
