import React, { useState, useEffect, useCallback } from 'react';
import { getCommonHeaders } from 'src/actions';
import Button2 from '@/components/lib/ButtonSecondary';
import {
  generateAiAdBanner,
  fetchProductUspsAndBannerElements,
} from 'src/actions/onboarding';
import { IGroweasyUser } from 'src/types';
import {
  IBannerElement,
  IAdBanner,
  ICampaign,
  GenerateAiBannerResponse,
  AdLanguage,
} from 'src/types/campaigns';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import Button from '@/components/lib/Button';
import { QueryParams } from 'src/constants';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import { IoSparkles } from 'react-icons/io5';
import { GrAlert } from 'react-icons/gr';

interface IGenerateAiBannersProps {
  campaignDetails: ICampaign;
  user: IGroweasyUser;
  onBannersGenerated: (adBanners: IAdBanner[]) => void;
  onCancel: () => void;
  onGoToAdDesigner: () => void; // Added to directly go to ad designer
  onUploadCustomImagesClick: () => void;
}

const GenerateAiBanners: React.FC<IGenerateAiBannersProps> = ({
  campaignDetails,
  user,
  onBannersGenerated,
  onCancel,
  onGoToAdDesigner,
  onUploadCustomImagesClick,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [bannerElements, setBannerElements] = useState<IBannerElement[]>([]);
  const [generatedBanners, setGeneratedBanners] = useState<IAdBanner[]>([]);
  const [showNotAllowedMessage, setShowNotAllowedMessage] = useState(false);
  const [notAllowedReason, setNotAllowedReason] = useState<string>('');
  const [generationFailed, setGenerationFailed] = useState(false);

  useEffect(() => {
    const isEnglish =
      (campaignDetails?.details?.ad_language ?? AdLanguage.ENGLISH) ===
      AdLanguage.ENGLISH;
    const hasExistingAiBanners = campaignDetails?.details?.ad_banners?.some(
      (banner) => banner.banner_data?.template_id?.startsWith('ai-generated-'),
    );

    if (!isEnglish) {
      setShowNotAllowedMessage(true);
      setNotAllowedReason(
        'AI banner generation is only available for English language campaigns.',
      );
    } else if (hasExistingAiBanners) {
      setShowNotAllowedMessage(true);
      setNotAllowedReason(
        'You have already generated AI banners for this campaign. Please use banner templates instead.',
      );
    } else {
      setShowNotAllowedMessage(false);
      setNotAllowedReason('');
    }
  }, [campaignDetails]);

  useEffect(() => {
    // Skip API call if we already have generated banners or banner elements or if generation failed
    if (
      generatedBanners.length > 0 ||
      bannerElements.length > 0 ||
      generationFailed
    ) {
      return;
    }

    const fetchBannerElements = async () => {
      try {
        setIsLoading(true);
        const response = await fetchProductUspsAndBannerElements({
          queryParams: {
            [QueryParams.CAMPAIGN_ID]: campaignDetails?.id,
            [QueryParams.FLOW_TYPE]: 'banners',
          },
          headers: getCommonHeaders(user),
        });

        if (response?.data?.banners) {
          setBannerElements(response.data.banners.slice(0, 3));
        }
      } catch (error) {
        logApiErrorAndShowToastMessage(
          error as Error,
          'GenerateAiBanners.fetchBannerElements',
        );
      } finally {
        setIsLoading(false);
      }
    };

    if (campaignDetails?.id) {
      void fetchBannerElements();
    }
  }, [
    campaignDetails?.id,
    user,
    bannerElements.length,
    generatedBanners.length,
    generationFailed, // Add generationFailed to dependency array
  ]);

  const generateBanners = useCallback(async () => {
    // Skip if we don't have banner elements or if banners are already generated or if generation failed
    if (
      !bannerElements.length ||
      generatedBanners.length > 0 ||
      generationFailed
    )
      return;

    try {
      setIsLoading(true);
      setGenerationFailed(false);
      logEvent(EVENT_NAMES.ad_banners_ai_banner_generation_started);

      const bannerSizes = ['square', 'square', 'portrait'] as const;
      let adBanners: IAdBanner[] = [];
      const promises: Promise<GenerateAiBannerResponse>[] = [];

      for (let i = 0; i < Math.min(bannerElements.length, 3); i++) {
        const size = bannerSizes[i];
        const bannerElement = bannerElements[i];

        promises.push(
          generateAiAdBanner({
            headers: getCommonHeaders(user),
            queryParams: {},
            data: {
              campaign_id: campaignDetails.id,
              size,
              banner_details: bannerElement,
              //vendor: i === 0 ? 'openai' : 'ideogram'
            },
          }),
        );
      }

      const results = await Promise.all(promises);

      adBanners = results.map((result, index) => {
        const bannerElement = bannerElements[index];
        const responseData = result.data;

        return {
          image: {
            hash: responseData?.hash,
            width: responseData?.width,
            height: responseData?.height,
            s3_url: responseData?.s3_url,
          },
          banner_data: {
            creative_title: bannerElement.creative_title,
            call_out: bannerElement.call_out,
            call_to_action: bannerElement.call_to_action,
            creative_image_url: responseData?.s3_url,
            size: bannerSizes[index],
            template_id: `ai-generated-${index}`,
          },
        };
      });

      setGeneratedBanners(adBanners);
      logEvent(EVENT_NAMES.ad_banners_ai_banner_generation_completed);
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error as Error,
        'GenerateAiBanners.generateBanners',
      );
      setGenerationFailed(true);
    } finally {
      setIsLoading(false);
    }
  }, [
    bannerElements,
    campaignDetails.id,
    user,
    generatedBanners.length,
    generationFailed,
  ]);

  const handleSaveToCampaign = () => {
    if (generatedBanners.length > 0) {
      logEvent(EVENT_NAMES.ad_banners_ai_banner_used);
      onBannersGenerated(generatedBanners);
    }
  };

  const handleUseCustomAITemplates = () => {
    logEvent(EVENT_NAMES.ad_banners_go_to_designer_clicked);
    // Directly go to the ad designer, skipping the AiBannerPreview step
    onGoToAdDesigner();
  };

  const handleRetryGeneration = () => {
    setGenerationFailed(false); // Reset failure state
    // The useEffect hook will trigger generateBanners again if bannerElements exist
  };

  useEffect(() => {
    if (
      bannerElements.length > 0 &&
      !isLoading &&
      generatedBanners.length === 0 &&
      !generationFailed // Only generate if not failed
    ) {
      void generateBanners();
    }
  }, [
    bannerElements,
    isLoading,
    generatedBanners.length,
    generateBanners,
    generationFailed,
  ]);

  return (
    <div className="flex flex-col h-full">
      {showNotAllowedMessage ? (
        <div className="flex flex-col items-center justify-center h-full">
          <div className="text-center mb-4">
            <h3 className="text-xl font-medium mb-2">Not Available</h3>
            <p className="text-gray-600">{notAllowedReason}</p>
          </div>
          <Button2 onClick={onGoToAdDesigner}>Go to Banner Templates</Button2>
        </div>
      ) : isLoading ? (
        <div className="flex flex-col h-full">
          <div className="flex-1 overflow-y-auto pt-2 pb-4">
            <h3 className="text-xl font-medium mb-4">Generating AI Banners</h3>
            <div className="flex flex-col gap-6">
              {[1, 2, 3].map((_, index) => (
                <div key={index} className="relative">
                  <div className="relative mx-auto w-5/6 rounded-lg overflow-hidden group shadow animate-pulse bg-[length:200%_200%] animate-bg-pan bg-gradient-to-r from-gray-200 to-gray-200 via-green-50/70 h-96"></div>
                  <IoSparkles className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-40 h-40 text-primary/30 animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : generatedBanners.length > 0 ? (
        <div className="flex flex-col h-full">
          <div className="flex-1 overflow-y-auto pb-4 pt-2">
            <h3 className="text-xl font-medium mb-4">AI Generated Banners</h3>
            <div className="flex flex-col gap-6">
              {generatedBanners
                .filter((banner) => !banner.hidden)
                .map((banner, index) => (
                  <div key={index} className="flex flex-col">
                    <div className="relative mx-auto w-5/6 rounded-lg overflow-hidden group shadow">
                      <img
                        src={banner.image?.s3_url}
                        alt={`AI Generated Banner ${index + 1}`}
                        className="w-full h-auto group-hover:scale-110 transition-all duration-700"
                      />
                    </div>
                  </div>
                ))}
            </div>
          </div>
          <div className="flex flex-col gap-1 mt-4">
            <Button onClick={handleSaveToCampaign}>Save to Campaign</Button>
            <p className="text-sm  py-1 text-center">
              Didn&apos;t like it?&nbsp;
              <span
                className="text-hyperlink cursor-pointer font-medium hover:underline"
                onClick={handleUseCustomAITemplates}
              >
                Use AI editor
              </span>
              <span className="px-1">Or</span>
              <br className=" min-[405px]:hidden " />
              <span
                className="text-hyperlink cursor-pointer font-medium hover:underline"
                onClick={onUploadCustomImagesClick}
              >
                Upload your own Banners
              </span>
            </p>
          </div>
        </div>
      ) : generationFailed ? (
        <div className="flex flex-col items-center justify-center h-full">
          <GrAlert className="text-red w-10 h-10 mb-4" />
          <p className="text-red mb-4">
            Failed to generate AI banners. Please try again.
          </p>
          <Button onClick={handleRetryGeneration}>Retry</Button>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center h-full">
          <p>No banner elements available to generate banners.</p>
          <Button2 onClick={onCancel}>Go Back</Button2>
        </div>
      )}
    </div>
  );
};

export default GenerateAiBanners;
