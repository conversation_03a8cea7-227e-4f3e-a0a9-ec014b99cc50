import { useState, useRef, ChangeEventHandler } from 'react';
import BottomSheet from '@/components/lib/BottomSheet';
import CrossIcon from '@/images/common/cross.svg';
import Button from '@/components/lib/Button';
import Image from 'next/image';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import { AdPlatforms } from 'src/types';

interface IUploadCustomCreativeBsProps {
  uploadAdImages: (blobs: Blob[]) => Promise<void>;
  onClose: () => void;
  uploadInProgress: boolean;
  adPlatform: AdPlatforms;
}

const UploadCustomCreativeBs = (props: IUploadCustomCreativeBsProps) => {
  const { uploadAdImages, onClose, uploadInProgress, adPlatform } = props;

  const [selectedImageFiles, setSelectedImageFiles] = useState<File[]>([]);

  const uploadImageInputRef = useRef<HTMLInputElement>(null);

  const onUploadIconPress = () => {
    uploadImageInputRef.current?.click();
  };

  const onUploadImageInputChange: ChangeEventHandler<HTMLInputElement> = (
    e,
  ) => {
    const fileList = e.target?.files;
    const files = [];
    for (let i = 0; i < fileList.length; i++) {
      files.push(fileList[i]);
    }
    setSelectedImageFiles(files);
  };

  const onStartUploadingClick = () => {
    logEvent(EVENT_NAMES.upload_custom_banners_clicked);
    void uploadAdImages(selectedImageFiles);
  };

  return (
    <BottomSheet
      onClose={onClose}
      className="h-4/5"
      contentClassName="h-full flex flex-col flex-1"
      loading={uploadInProgress}
    >
      <div className="flex flex-col flex-1 overflow-hidden">
        <div className="flex items-center">
          <p className="text-base text-gray-dark flex-1">
            Upload your own Banners
          </p>
          <CrossIcon
            width="14"
            height="14"
            className="text-gray-dark cursor-pointer"
            onClick={onClose}
          />
        </div>
        <p className="text-xs mt-5">
          For optimal performance, we recommend using two square images (1080 x
          1080), 2 portrait images (1080 x 1920) and 2 landscape images (1200 x
          628).
        </p>
        <div className="mt-2 flex-1 flex-col overflow-y-scroll no-scrollbar">
          <div
            className="rounded-xl mt-5 flex flex-col items-center justify-center p-12 border-dashed border cursor-pointer"
            onClick={onUploadIconPress}
          >
            <input
              type="file"
              ref={uploadImageInputRef}
              onChange={onUploadImageInputChange}
              className="hidden"
              accept=".jpg, .jpeg, .png"
              multiple
            />
            <div className="flex">
              <Image
                src="/images/common/upload-icon.png"
                width={52}
                height={52}
                alt="upload icon"
              />
            </div>
            {selectedImageFiles.length === 0 ? (
              <p className="text-xs text-gray-medium font-medium mt-4">
                Click here and choose at least 3 Banners
              </p>
            ) : null}
            <div className="mt-4">
              {selectedImageFiles.map((file, index) => {
                return (
                  <div key={index}>
                    <p className="text-xs text-gray-medium font-medium">{`${
                      file.name
                    }, ${Math.ceil(file.size / 1024)} KB`}</p>
                  </div>
                );
              })}
            </div>
          </div>
          {selectedImageFiles.length !== 0 && selectedImageFiles.length < 3 ? (
            <p className="text-xs mt-3 text-red">Select at least 3 Banners</p>
          ) : null}
        </div>
        {adPlatform === AdPlatforms.GOOGLE ? (
          <p className="text-xxs text-red mt-3">
            At least 1 landscape image (1200 x 628) and 1 square image (1080 x
            1080) is mandatory for Google
          </p>
        ) : null}
        <div className="mt-6 flex flex-col">
          <Button
            onClick={onStartUploadingClick}
            disabled={uploadInProgress || selectedImageFiles.length < 3}
          >
            <p>Upload Now</p>
          </Button>
        </div>
      </div>
    </BottomSheet>
  );
};

export default UploadCustomCreativeBs;
