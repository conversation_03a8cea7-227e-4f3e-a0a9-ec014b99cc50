import { useRouter } from 'next/router';
import { useState, useEffect, useRef } from 'react';
import { useMutation, useQuery } from 'react-query';
import Button from '@/components/lib/Button';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { getCommonHeaders } from 'src/actions';
import {
  getAdImages,
  uploadAdImage,
  uploadAdImageV3,
  getBannerTemplates,
} from 'src/actions/onboarding';
import { IGroweasyUser } from 'src/types';
import { IBannerTemplate } from 'src/types/banner_templates';
import {
  IAdBanner,
  ICampaign,
  IUploadAdBannerResponse,
  AdLanguage,
  GROWEASY_CAMPAIGN_TYPE,
} from 'src/types/campaigns';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import SelectedAdBanners from './SelectedAdBanners';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import { OnboardingStepIds, QueryParams } from 'src/constants';
import UploadCustomCreativeBs from './UploadCustomCreativesBs';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import UploadIcon from '@/images/icons/upload-icon.svg';
import EditBannerTemplateBsV2 from './EditBannerTemplateBsV2';
import BannerImageContainer from './BannerImageContainer';
import { getBannerDataFromTemplate } from 'src/utils/banner_image';
import dynamic from 'next/dynamic';
import GenerateAiBanners from './GenerateAiBanners';

const RotatingMessages = dynamic(
  () => import('@/components/lib/RotatingMessages'),
  {
    ssr: false,
  },
);

interface IAdBannersCompV2Props {
  campaignDetails: ICampaign;
  saveCampaignDetails: (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => void;
  user: IGroweasyUser;
  onNextPress: () => void;
}

const DESIRED_BANNERS_COUNT = 3;

const AdBannersCompV2 = (props: IAdBannersCompV2Props) => {
  const { campaignDetails, saveCampaignDetails, user, onNextPress } = props;

  const [templates, setTemplates] = useState<IBannerTemplate[]>([]);
  const [selectedBannersIds, setSelectedBannerIds] = useState<string[]>([]);
  const [existingAdBanners, setExistingAdBanners] = useState<
    IAdBanner[] | null
  >(null);
  const [selectedTemplateForEditing, setSelectedTemplateForEditing] =
    useState<IBannerTemplate | null>(null);
  const [uploadCustomImagesBsVisible, setUploadCustomImagesVisibleBs] =
    useState(false);
  const [uploadAdImagesInProgress, setUploadAdImagesInProgress] =
    useState(false);
  // Initialize based on language - default to false until we check the language
  const [showAiTemplatizedDesigner, setShowAiTemplatizedDesigner] =
    useState(false);
  const [showAiBannerGenerator, setShowAiBannerGenerator] = useState(false);

  const scrollAnimationDivRef = useRef<HTMLDivElement>(null);

  const router = useRouter();

  const customTemplatesResponse = useQuery(
    ['getBannerTemplates', campaignDetails?.id, showAiTemplatizedDesigner],
    () =>
      getBannerTemplates({
        queryParams: router.query as Record<string, string>,
        headers: getCommonHeaders(user),
        data: {
          business_details: campaignDetails?.details?.business_details,
          targeting: campaignDetails?.details?.targeting,
          google_geo_locations: campaignDetails?.google_ads_data?.geo_locations,
          ad_language: campaignDetails?.details?.ad_language,
          ai_assisted_product_usps:
            campaignDetails?.details?.ai_assisted_product_usps,
        },
      }),
    {
      retry: 1,
      enabled:
        !!campaignDetails?.details?.business_details &&
        showAiTemplatizedDesigner === true &&
        (!existingAdBanners || !existingAdBanners.length),
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'AdBannersCompV2.getBannerTemplates',
        );
      },
      onSuccess: (response) => {
        setTemplates(response.data?.templates || []);
      },
    },
  );

  const adImagesResponse = useQuery(
    ['getAdImages', campaignDetails.id],
    () =>
      getAdImages({
        queryParams: {
          ...router.query,
          hashes: JSON.stringify(
            existingAdBanners?.map((item) => item.image?.hash),
          ),
          [QueryParams.AD_ACCOUNT_ID]:
            campaignDetails?.details?.config?.ad_account_id ?? '',
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      enabled: !!existingAdBanners?.length,
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'AdBannersCompV2.getAdImages');
      },
    },
  );

  const uploadAdImageMutation = useMutation(uploadAdImage);
  const uploadAdImageMutationV3 = useMutation(uploadAdImageV3);

  useEffect(() => {
    const banners = campaignDetails?.details?.ad_banners ?? [];
    setExistingAdBanners(banners);
    if (banners.length > 0) {
      setShowAiTemplatizedDesigner(false);
      setShowAiBannerGenerator(false);
    }
  }, [campaignDetails?.details?.ad_banners]);

  useEffect(() => {
    // Only run this effect if there are no existing banners
    if (existingAdBanners && existingAdBanners.length > 0) {
      return;
    }

    const isEnglish =
      (campaignDetails?.details?.ad_language ?? AdLanguage.ENGLISH) ===
      AdLanguage.ENGLISH;

    const hadAiGeneratedBanners = campaignDetails?.details?.ad_banners?.some(
      (banner) => banner.banner_data?.template_id?.startsWith('ai-generated-'),
    );

    // Set the appropriate view based on language, previous AI banner generation and type
    if (
      !isEnglish ||
      hadAiGeneratedBanners ||
      campaignDetails?.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX
    ) {
      setShowAiBannerGenerator(false);
      setShowAiTemplatizedDesigner(true);
    } else {
      setShowAiBannerGenerator(true);
      setShowAiTemplatizedDesigner(false);
    }
  }, [
    existingAdBanners,
    campaignDetails?.details?.ad_language,
    campaignDetails?.details?.ad_banners,
  ]);

  const onToggleSelection = (bannerId: string) => {
    if (selectedBannersIds.includes(bannerId)) {
      setSelectedBannerIds(selectedBannersIds.filter((id) => id !== bannerId));
    } else if (selectedBannersIds.length < DESIRED_BANNERS_COUNT) {
      setSelectedBannerIds([...selectedBannersIds, bannerId]);
    }
  };

  const uploadAdImagesAndReturnDetails = async (payload: {
    files?: File[];
    templates?: IBannerTemplate[];
  }): Promise<IAdBanner[]> => {
    const { files, templates } = payload;
    const promises = [];
    const adBanners: IAdBanner[] = [];
    setUploadAdImagesInProgress(true);
    const queryParams = {
      ...(router.query as Record<string, string>),
      [QueryParams.AD_ACCOUNT_ID]:
        campaignDetails?.details?.config?.ad_account_id ?? '',
    };
    if (files?.length) {
      files.forEach((file) => {
        promises.push(
          uploadAdImageMutation.mutateAsync({
            headers: getCommonHeaders(user),
            queryParams,
            file,
          }),
        );
      });
    } else if (templates?.length) {
      templates.forEach((template) => {
        promises.push(
          uploadAdImageMutationV3.mutateAsync({
            headers: getCommonHeaders(user),
            queryParams,
            template,
          }),
        );
      });
    }
    try {
      const uploadResponses = (await Promise.all(promises)) as Array<
        IUploadAdBannerResponse | IUploadAdBannerResponse[]
      >;
      const flattenedResponses: IUploadAdBannerResponse[] = [];
      uploadResponses.forEach(
        (response: IUploadAdBannerResponse | IUploadAdBannerResponse[]) => {
          if (response instanceof Array) {
            flattenedResponses.push(...response);
          } else {
            flattenedResponses.push(response);
          }
        },
      );
      flattenedResponses.forEach((response) => {
        const responseData = Object.values(response.images)[0];
        adBanners.push({
          image: {
            hash: responseData.hash,
            width: responseData.width,
            height: responseData.height,
            s3_url: response.s3_url,
          },
        });
      });
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error as Error,
        'AdBannersCompV2.uploadAdImagesAndReturnDetails',
      );
    } finally {
      setUploadAdImagesInProgress(false);
    }
    return adBanners;
  };

  const saveUploadedBannerDetails = (adBanners: IAdBanner[]) => {
    if (!adBanners.length) {
      return;
    }
    const updatedCampaignDetails: ICampaign = {
      ...campaignDetails,
      details: {
        ...campaignDetails?.details,
        ad_banners: adBanners,
      },
    };
    saveCampaignDetails(updatedCampaignDetails, OnboardingStepIds.AD_BANNERS);
  };

  const uploadAdImagesAndSaveDetails = async (files: File[]) => {
    const adBanners = await uploadAdImagesAndReturnDetails({
      files,
    });
    saveUploadedBannerDetails(adBanners);
  };

  const onTemplateUpdate = (template: IBannerTemplate) => {
    const updatedTemplates = templates.map((item) => {
      if (item.id === template.id) {
        return template;
      }
      return item;
    });
    // console.log(updatedTemplates);
    setTemplates(updatedTemplates);
  };

  const onSaveDetailsClick = async () => {
    logEvent(EVENT_NAMES.ad_banners_save_clicked);
    const squareTemplates: IBannerTemplate[] = [];
    for (const bannerId of selectedBannersIds) {
      const template = templates.find((item) => item.id === bannerId);
      if (template) {
        squareTemplates.push(template);
      }
    }
    let uploadedAdBanners = await uploadAdImagesAndReturnDetails({
      templates: squareTemplates,
    });
    if (!uploadedAdBanners.length) {
      return;
    }
    let index = 0;
    uploadedAdBanners = uploadedAdBanners.map((item) => {
      const squareSize = item.image?.width === item?.image?.height;
      const adBanner = {
        ...item,
        hidden: !squareSize, // since portrait & landscape are for optimisation, do not show to user
      };
      // will be used to generate AI ad videos
      if (squareSize) {
        const template = squareTemplates[index++ % squareTemplates.length];
        adBanner.banner_data = getBannerDataFromTemplate(template);
      }
      return adBanner;
    });
    saveUploadedBannerDetails(uploadedAdBanners);
  };

  const saveCtaEnabled = selectedBannersIds.length === DESIRED_BANNERS_COUNT;
  const stockImages = customTemplatesResponse?.data?.data?.images || [];

  useEffect(() => {
    if (existingAdBanners?.length) return;
    if (
      showAiTemplatizedDesigner &&
      !customTemplatesResponse?.isFetching &&
      scrollAnimationDivRef.current
    ) {
      const div = scrollAnimationDivRef.current;
      div.classList.add('initial-bouncy-animation');
    }
  }, [
    customTemplatesResponse?.isFetching,
    existingAdBanners?.length,
    showAiTemplatizedDesigner,
  ]);

  const handleAiBannersGenerated = (adBanners: IAdBanner[]) => {
    if (adBanners.length > 0) {
      saveUploadedBannerDetails(adBanners);
      setShowAiBannerGenerator(false);
      setShowAiTemplatizedDesigner(true);
    }
  };

  const handleGoToAdDesigner = () => {
    logEvent(EVENT_NAMES.ad_banners_go_to_designer_clicked);
    setShowAiTemplatizedDesigner(true);
    setShowAiBannerGenerator(false);
    setSelectedBannerIds([]);
  };

  return (
    <div className="px-4 pt-4 pb-4 flex flex-col flex-1 h-full">
      {existingAdBanners?.length ? (
        adImagesResponse.isLoading ? (
          <div className="flex flex-col items-center mt-3">
            <SpinnerLoader />
          </div>
        ) : (
          <SelectedAdBanners
            images={adImagesResponse.data?.data}
            onReset={() => {
              logEvent(EVENT_NAMES.ad_banners_reset_clicked);
              setExistingAdBanners([]);
              setShowAiTemplatizedDesigner(true);
              setShowAiBannerGenerator(false);
            }}
            onNextPress={onNextPress}
            campaignDetails={campaignDetails}
          />
        )
      ) : (
        <div className="flex flex-col flex-1 h-full">
          {showAiTemplatizedDesigner &&
          customTemplatesResponse?.isFetching &&
          (!existingAdBanners || !existingAdBanners.length) ? (
            <div className="flex flex-col pt-4 gap-5 items-center mt-16">
              <SpinnerLoader />
              <RotatingMessages
                interval={3900}
                messages={[
                  'Analyzing your campaign details...',
                  'Creating personalized ad banners...',
                  'Optimized by intelligent automation.',
                  'Almost done. Hang tight...',
                ]}
              />
            </div>
          ) : showAiBannerGenerator ? (
            <GenerateAiBanners
              campaignDetails={campaignDetails}
              user={user}
              onBannersGenerated={handleAiBannersGenerated}
              onCancel={() => setShowAiBannerGenerator(false)}
              onGoToAdDesigner={handleGoToAdDesigner}
              onUploadCustomImagesClick={() =>
                setUploadCustomImagesVisibleBs(true)
              }
            />
          ) : showAiTemplatizedDesigner ? (
            <div className="flex flex-col flex-1 h-full">
              <p className="text-sm text-black">
                Select any 3 designs that you like for the ad banners
              </p>
              <div className="flex-1 overflow-y-scroll no-scrollbar mt-3">
                <div
                  className=" flex flex-col gap-5 "
                  ref={scrollAnimationDivRef}
                >
                  {templates
                    ?.filter((item) => item.size === 'square')
                    ?.map((item) => (
                      <BannerImageContainer
                        item={item}
                        selectedBannersIds={selectedBannersIds}
                        stockImages={stockImages}
                        onTemplateUpdate={onTemplateUpdate}
                        setSelectedTemplateForEditing={
                          setSelectedTemplateForEditing
                        }
                        onToggleSelection={onToggleSelection}
                        key={item.id}
                        user={user}
                      />
                    ))}
                </div>
              </div>
              <div className=" pt-3 relative">
                <div className=" absolute top-0 -left-4 -right-4 border-t border-[#C5C5C5] " />
                <div className=" flex items-center justify-between ">
                  <div>
                    <p className=" text-sm font-medium ">
                      {selectedBannersIds.length}/3 Selected
                    </p>
                  </div>
                  <Button
                    onClick={() => setUploadCustomImagesVisibleBs(true)}
                    className=" bg-white text-[#C5C5C5] shadow-[inset_0_0_0_1px] shadow-[#225E56] rounded-lg flex justify-center items-center gap-1.5 !p-0 h-10 !px-3 "
                  >
                    <UploadIcon className=" text-black h-3.5 w-3.5  " />
                    <p className=" text-sm text-black font-normal ">
                      Upload your own Banners
                    </p>
                  </Button>
                </div>
                <Button
                  onClick={() => void onSaveDetailsClick()}
                  disabled={!saveCtaEnabled}
                  className="mt-3 w-full "
                >
                  <p>Continue</p>
                </Button>
              </div>
            </div>
          ) : null}
        </div>
      )}
      {selectedTemplateForEditing ? (
        <EditBannerTemplateBsV2
          template={selectedTemplateForEditing}
          onTemplateUpdate={onTemplateUpdate}
          onClose={() => setSelectedTemplateForEditing(null)}
        />
      ) : null}
      {uploadCustomImagesBsVisible ? (
        <UploadCustomCreativeBs
          uploadAdImages={uploadAdImagesAndSaveDetails}
          onClose={() => setUploadCustomImagesVisibleBs(false)}
          uploadInProgress={uploadAdImagesInProgress}
          adPlatform={campaignDetails?.platform}
        />
      ) : null}
      {uploadAdImagesInProgress ? <FullScreenLoader /> : null}
    </div>
  );
};

export default AdBannersCompV2;
