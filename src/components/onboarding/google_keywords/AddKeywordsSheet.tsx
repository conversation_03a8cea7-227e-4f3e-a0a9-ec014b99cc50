import BottomSheet from '@/components/lib/BottomSheet';
import Button from '@/components/lib/Button';
import { useState } from 'react';
import CrossIcon from '@/images/common/cross.svg';
import KeywordTag from './KeywordTag';

interface IAddKeywordsSheetProps {
  onSaveClick: (selectedSearchKeywordsArr: string[]) => void;
  onClose: () => void;
}

const AddKeywordsSheet = (props: IAddKeywordsSheetProps) => {
  const { onSaveClick, onClose } = props;

  const [searchKeywords, setSearchKeywords] = useState('');

  const selectedSearchKeywordsArr = Array.from(
    new Set(
      searchKeywords
        ?.split(',')
        .map((item) => item.trim()) // remove leading & trailing whitespaces
        .filter((item) => item !== ''), // Remove empty strings
    ),
  );

  const onSaveCtaClick = () => {
    onSaveClick(selectedSearchKeywordsArr);
    onClose();
  };

  const onRemoveKeyword = (keyword: string) => {
    const updatedKeywordsArr = selectedSearchKeywordsArr.filter(
      (item) => item !== keyword,
    );
    setSearchKeywords(updatedKeywordsArr.join(', '));
  };

  return (
    <BottomSheet
      className="h-4/5"
      contentClassName="h-full flex flex-col flex-1"
      onClose={onClose}
    >
      <div className="flex flex-col flex-1 overflow-hidden">
        <div className="flex items-center">
          <p className="text-base text-gray-dark flex-1">Add Keywords</p>
          <CrossIcon
            width="14"
            height="14"
            className="text-gray-dark cursor-pointer"
            onClick={onClose}
          />
        </div>
        <div className="flex-1 flex-col overflow-y-scroll no-scrollbar">
          <p className="text-xs text-gray-dark flex-1 mt-3">
            Enter Keywords Your Target Customer Search for (Comma Separated)
          </p>
          <div className="flex">
            <textarea
              className="w-full border border-gray-medium mt-3 rounded-lg outline-none p-2 text-gray-dark text-sm"
              rows={4}
              value={searchKeywords}
              onChange={(event) => {
                setSearchKeywords(event.target.value);
              }}
              placeholder="e.g. Plumbing services near me, Best project management tools, Online coding courses, Top-rated legal services etc"
            ></textarea>
          </div>
          {searchKeywords ? (
            <div className="mt-5">
              <div className="flex items-center flex-wrap gap-2">
                {selectedSearchKeywordsArr.map((item, index) => {
                  if (!item.trim()) {
                    return null;
                  }
                  return (
                    <KeywordTag
                      keyword={item}
                      key={index}
                      onRemove={onRemoveKeyword}
                    />
                  );
                })}
              </div>
            </div>
          ) : null}
        </div>
        <Button className="mt-2" onClick={onSaveCtaClick}>
          <p>Save</p>
        </Button>
      </div>
    </BottomSheet>
  );
};

export default AddKeywordsSheet;
