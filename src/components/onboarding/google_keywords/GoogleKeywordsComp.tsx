import { useState, useEffect } from 'react';
import { ICampaign } from 'src/types/campaigns';
import { OnboardingStepIds } from 'src/constants';
import Button from '../../lib/Button';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import AddKeywordsSheet from './AddKeywordsSheet';
import KeywordTag from './KeywordTag';
import { IGroweasyUser } from 'src/types';
import { useMutation } from 'react-query';
import { getGoogleKeywordIdeas } from 'src/actions/onboarding';
import { getCommonHeaders } from 'src/actions';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import FullScreenLoader from '@/components/lib/FullScreenLoader';

interface IGoogleKeywordsCompProps {
  campaignDetails: ICampaign;
  saveCampaignDetails: (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => void;
  user: IGroweasyUser;
}

/**
 * Although Google P-Max does not accept age & gender, still accepting to power OpenAI prompts
 */
const GoogleKeywordsComp = (props: IGoogleKeywordsCompProps) => {
  const { campaignDetails, saveCampaignDetails, user } = props;

  const googleAdsData = campaignDetails?.google_ads_data;

  const [searchKeywords, setSearchKeywords] = useState(
    googleAdsData?.search_keywords ?? [],
  );
  const [keywordIdeas, setKeywordIdeas] = useState<string[]>([]);

  const [addKeywordsSheetVisible, setAddKeywordsSheetVisible] = useState(false);

  const aiSuggestedKeywords = Object.values(
    googleAdsData?.search_keywords_suggestions ?? {},
  ).flat();

  const googleKeywordIdeasMutation = useMutation(getGoogleKeywordIdeas);

  useEffect(() => {
    if (googleAdsData?.search_keywords?.length) {
      setSearchKeywords(googleAdsData.search_keywords);
    } else if (googleAdsData?.search_keywords_suggestions) {
      setSearchKeywords(aiSuggestedKeywords);

      // for first time, fetch high volume keywords from Google and make it part of AI suggested keywords
      googleKeywordIdeasMutation
        .mutateAsync({
          headers: getCommonHeaders(user),
          queryParams: {},
          // send only 5 locations, Google sometimes throw invalid geo_target_constants value error
          data: {
            seed_keywords: aiSuggestedKeywords,
            url: campaignDetails?.details?.business_details?.website,
            geo_target_constants: Array.from(
              new Set(
                (googleAdsData?.geo_locations ?? [])
                  .map((item) => item.geoTargetConstant?.resourceName)
                  .filter(Boolean), // Remove undefined/null
              ),
            ).slice(0, 5),
          },
        })
        .then((response) => {
          const googleKeywordIdeas = response.data?.keyword_ideas?.map(
            (item) => item.text,
          );
          setKeywordIdeas(googleKeywordIdeas);
          setSearchKeywords([...aiSuggestedKeywords, ...googleKeywordIdeas]);
        })
        .catch((error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'GoogleKeywordsComp.googleKeywordIdeasMutation',
          );
        });
    }
  }, [campaignDetails]);

  const onSaveDetailsClick = () => {
    logEvent(EVENT_NAMES.google_keywords_save_clicked);
    const updatedCampaignDetails: ICampaign = {
      ...campaignDetails,
      google_ads_data: {
        ...googleAdsData,
        search_keywords: searchKeywords,
      },
    };
    saveCampaignDetails(updatedCampaignDetails, OnboardingStepIds.AUDIENCE);
  };

  const onManualKeywordsAddition = (manualKeywordsArr: string[]) => {
    // Keyword cannot be more than 80 chars
    setSearchKeywords(
      Array.from(
        new Set([
          ...searchKeywords,
          ...manualKeywordsArr.filter((item) => item.length <= 80),
        ]),
      ),
    );
  };

  const manuallyAddedKeywords = searchKeywords.filter(
    (item) =>
      !aiSuggestedKeywords.includes(item) && !keywordIdeas.includes(item),
  );

  return (
    <div className="px-4 pt-4 pb-4 flex flex-col flex-1 h-full">
      <div className="flex flex-col h-full flex-1 overflow-y-scroll no-scrollbar pb-3">
        <div>
          <p className="text-sm">Keywords Targeting</p>
          <p className="text-xxs mt-1">
            What are some words or phrases people use when searching for your
            products or services?
          </p>
          <p className="text-xxs text-gray-dark">
            These keywords will signal Google’s search data to discover
            customers who are more likely to convert. We recommend adding 20-25
            keywords.
          </p>

          <div>
            <p className="mt-4 text-xs">Remove the non relevant keywords</p>
            <div className="flex items-center flex-wrap gap-2 mt-3">
              {searchKeywords
                .filter(
                  (item) =>
                    aiSuggestedKeywords.includes(item) ||
                    keywordIdeas.includes(item),
                )
                .map((item, index) => {
                  return (
                    <KeywordTag
                      keyword={item}
                      key={index}
                      onRemove={(keyword) =>
                        setSearchKeywords(
                          searchKeywords.filter((item) => item !== keyword),
                        )
                      }
                    />
                  );
                })}
            </div>
          </div>
          {manuallyAddedKeywords?.length ? (
            <div>
              <p className="mt-4 text-xs">Keywords added by you</p>
              <div className="flex items-center flex-wrap gap-2 mt-3">
                {manuallyAddedKeywords.map((item, index) => {
                  return (
                    <KeywordTag
                      keyword={item}
                      key={index}
                      onRemove={(keyword) =>
                        setSearchKeywords(
                          searchKeywords.filter((item) => item !== keyword),
                        )
                      }
                    />
                  );
                })}
              </div>
            </div>
          ) : null}
        </div>
      </div>
      <Button
        onClick={() => setAddKeywordsSheetVisible(true)}
        className="mt-3 !bg-white border border-primary !text-primary"
      >
        <p>+ Add more Keywords</p>
      </Button>
      <Button
        onClick={onSaveDetailsClick}
        disabled={!searchKeywords.length}
        className="mt-3"
      >
        <p>Save</p>
      </Button>
      {addKeywordsSheetVisible ? (
        <AddKeywordsSheet
          onClose={() => setAddKeywordsSheetVisible(false)}
          onSaveClick={onManualKeywordsAddition}
        />
      ) : null}
      {googleKeywordIdeasMutation.isLoading ? <FullScreenLoader /> : null}
    </div>
  );
};

export default GoogleKeywordsComp;
