import CrossIcon from '@/images/common/cross.svg';
import classNames from 'classnames';

interface IKeywordTagProps {
  keyword: string;
  onRemove?: (keyword: string) => void;
}

const KeywordTag = (props: IKeywordTagProps) => {
  const { keyword, onRemove } = props;

  return (
    <div
      className={classNames(
        'flex items-center py-1 px-2 rounded-full border border-primary bg-primary/10',
        {
          'border-red': keyword.length > 80,
        },
      )}
    >
      <p className="text-xxs text-primary">{keyword}</p>
      {onRemove ? (
        <div className="ml-2">
          <CrossIcon
            height="8"
            width="8"
            onClick={() => onRemove(keyword)}
            className="cursor-pointer text-primary"
          />
        </div>
      ) : null}
    </div>
  );
};

export default KeywordTag;
