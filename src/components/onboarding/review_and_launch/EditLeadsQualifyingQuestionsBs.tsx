import BottomSheet from '@/components/lib/BottomSheet';
import Button from '@/components/lib/Button';
import { useState } from 'react';
import { ILeadgenFormQuestion } from 'src/types/campaigns';
import CrossIcon from '@/images/common/cross.svg';

interface IEditLeadsQualifyingQuestionsBsProps {
  leadgenFormQuestions: ILeadgenFormQuestion[];
  onSaveClick: (leadgenFormQuestions: ILeadgenFormQuestion[]) => void;
  onClose: () => void;
  campaignUpdateInProgress: boolean;
}

const EditLeadsQualifyingQuestionsBs = (
  props: IEditLeadsQualifyingQuestionsBsProps,
) => {
  const {
    leadgenFormQuestions,
    onSaveClick,
    onClose,
    campaignUpdateInProgress,
  } = props;

  const customQuestions = leadgenFormQuestions.filter(
    (item) => item.type === 'CUSTOM',
  );

  const [question1, setQuestion1] = useState(customQuestions[0]?.label ?? '');
  const [question2, setQuestion2] = useState(customQuestions[1]?.label ?? '');

  const onSaveCtaClick = () => {
    const leadgenFormQuestionsCopy = leadgenFormQuestions.filter(
      (item) => item.type !== 'CUSTOM',
    );
    // question1, 2, & 3 are reserved for FULL_NAME, EMAIL, PHONE (even if they are not being used)
    leadgenFormQuestionsCopy.push({
      label: question1,
      type: 'CUSTOM',
      key: `question4`,
    });
    leadgenFormQuestionsCopy.push({
      label: question2,
      type: 'CUSTOM',
      key: `question5`,
    });
    onSaveClick(leadgenFormQuestionsCopy);
    onClose();
  };

  return (
    <BottomSheet
      className="h-4/5"
      contentClassName="h-full flex flex-col flex-1"
      onClose={onClose}
      loading={campaignUpdateInProgress}
    >
      <div className="flex flex-col flex-1 overflow-hidden">
        <div className="flex items-center">
          <p className="text-base text-gray-dark flex-1">
            Edit Lead Qualifying Questions
          </p>
          <CrossIcon
            width="14"
            height="14"
            className="text-gray-dark cursor-pointer"
            onClick={onClose}
          />
        </div>
        <div className="flex-1 flex-col overflow-y-scroll no-scrollbar">
          <p className="text-xs mt-5">Question 1</p>
          <div className="mt-3 flex">
            <textarea
              rows={3}
              value={question1}
              onChange={(event) => setQuestion1(event.target.value)}
              className="outline-none border border-gray-medium rounded-lg px-2 py-2 text-sm w-full"
            />
          </div>
          <p className="text-xs mt-5">Question 2</p>
          <div className="mt-3 flex">
            <textarea
              rows={3}
              value={question2}
              onChange={(event) => setQuestion2(event.target.value)}
              className="outline-none border border-gray-medium rounded-lg px-2 py-2 text-sm w-full"
            />
          </div>
        </div>
        <Button
          className="mt-2"
          onClick={onSaveCtaClick}
          disabled={campaignUpdateInProgress}
        >
          <p>Save</p>
        </Button>
      </div>
    </BottomSheet>
  );
};

export default EditLeadsQualifyingQuestionsBs;
