import { useState, useEffect } from 'react';
import {
  ConsumerType,
  ICampaign,
  IGeoLocations,
  ILocationDetails,
} from 'src/types/campaigns';
import ChevronRightIcon from '@/images/common/chevron-right.svg';
import Image from 'next/image';
import {
  OnboardingStepIds,
  QueryParams,
  TARGETING_DEFAULT_MAX_AGE,
  TARGETING_DEFAULT_MIN_AGE,
} from 'src/constants';
import Button from '../../lib/Button';
import SelectAgeAndGenderCompBs from './SelectAgeAndGenderBs';
import SelectLocationBs from './SelectLocationBs';
import { AdPlatforms, IGroweasyUser } from 'src/types';
import SelectedGeoLocations from './SelectedGeoLocations';
import classNames from 'classnames';
import {
  getGenderDetailsString,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import {
  getGeoLocationCategoryType,
  getUpdatedGeoLocationsAfterAddingNewLocation,
} from 'src/utils/target_locations';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import { useMutation, useQuery } from 'react-query';
import { getDeliveryEstimate, getIdealCustomers } from 'src/actions/onboarding';
import { getCommonHeaders } from 'src/actions';
import FetchError from 'src/actions/FetchError';
import { useRouter } from 'next/router';
import GoogleSelectLocationsComp from '@/components/campaign_details/google/GoogleSelectLocationsComp';

interface ITargetAudienceCompProps {
  campaignDetails: ICampaign;
  saveCampaignDetails: (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => void;
  user: IGroweasyUser;
}

/**
 * Although Google P-Max does not accept age & gender, still accepting to power OpenAI prompts
 */
const TargetAudienceComp = (props: ITargetAudienceCompProps) => {
  const { campaignDetails, saveCampaignDetails, user } = props;

  const router = useRouter();
  const adPlatform = router.query[QueryParams.PLATFORM] ?? AdPlatforms.META;

  const googleAdsData = campaignDetails?.google_ads_data;

  const [minAge, setMinAge] = useState<number | null>(null);
  const [maxAge, setMaxAge] = useState<number | null>(null);

  // Defaults to all. 1 targets males, 2 targets females.
  const [genders, setGenders] = useState<number[] | null>(null);

  const [consumerType, setConsumerType] = useState<ConsumerType>();
  const [idealCustomers, setIdealCustomers] = useState('');

  const [metaGeoLocations, setMetaGeoLocations] = useState<IGeoLocations>({
    location_types: ['recent', 'home'],
  });

  const [googleGeoLocations, setGoogleGeoLocations] = useState(
    googleAdsData?.geo_locations ?? [],
  );

  const [showSelectAgeAndGenderBs, setShowSelectAgeAndGenderBs] =
    useState(false);
  const [showSelectLocationBs, setShowSelectLocationBs] = useState(false);

  const [deliveryEstimateErrorMsg, setDeliveryEstimateErrorMsg] = useState('');

  useEffect(() => {
    if (campaignDetails?.details?.targeting?.genders) {
      setGenders(campaignDetails?.details?.targeting?.genders);
    }
    if (campaignDetails?.details?.targeting?.age_min) {
      setMinAge(campaignDetails?.details?.targeting?.age_min);
    }
    if (campaignDetails?.details?.targeting?.age_max) {
      setMaxAge(campaignDetails?.details?.targeting?.age_max);
    }
    if (campaignDetails?.details?.targeting?.geo_locations) {
      setMetaGeoLocations(campaignDetails?.details?.targeting?.geo_locations);
    }
    if (campaignDetails?.details?.business_details?.consumer_type) {
      setConsumerType(
        campaignDetails?.details?.business_details?.consumer_type,
      );
    }
    if (campaignDetails?.details?.business_details?.ideal_customers) {
      setIdealCustomers(
        campaignDetails?.details?.business_details?.ideal_customers,
      );
    }
    if (googleAdsData?.geo_locations) {
      setGoogleGeoLocations(campaignDetails.google_ads_data.geo_locations);
    }
  }, [campaignDetails]);

  const getDeliveryEstimateMutation = useMutation(getDeliveryEstimate);

  useQuery(
    ['getIdealCustomers', campaignDetails?.id],
    () =>
      getIdealCustomers({
        headers: getCommonHeaders(user),
        queryParams: { [QueryParams.CAMPAIGN_ID]: campaignDetails?.id },
      }),
    {
      enabled:
        campaignDetails?.id &&
        !campaignDetails?.details?.business_details?.ideal_customers,
      onError(err: Error) {
        logApiErrorAndShowToastMessage(
          err,
          'TargetAudienceComp.getIdealCustomers',
        );
      },
      onSuccess(response) {
        const data = response.data;
        if (data?.ideal_customers) {
          // only fill if the user hasn't started to fill
          if (idealCustomers === '') {
            setIdealCustomers(data.ideal_customers);
          }
          if (!minAge && !maxAge && !genders) {
            setMinAge(data.age_min);
            setMaxAge(data.age_max);
            setGenders(data.genders);
          }
          if (!consumerType) {
            setConsumerType(data.consumer_type as ConsumerType);
          }
        }
      },
    },
  );

  const getAgeAndGenderString = (): string => {
    const str =
      `${minAge ?? TARGETING_DEFAULT_MIN_AGE} - ${
        maxAge ?? TARGETING_DEFAULT_MAX_AGE
      } years, ` + getGenderDetailsString(genders);
    return str;
  };

  useEffect(() => {
    // overlapping issue still exists, so validate asynchoronously, happening only when places is involved
    if (!metaGeoLocations?.places) {
      return;
    }

    // deep copy to avoid mutating original object
    const geoLocationsCopy = JSON.parse(
      JSON.stringify(metaGeoLocations),
    ) as IGeoLocations;

    ['regions', 'cities', 'neighborhoods', 'places'].forEach((key) => {
      geoLocationsCopy[key as keyof IGeoLocations]?.forEach(
        (item: ILocationDetails) => delete item.type,
      );
    });

    const queryParams = {
      optimization_goal: 'LEAD_GENERATION',
      targeting_spec: JSON.stringify({
        geo_locations: geoLocationsCopy,
      }),
    };
    getDeliveryEstimateMutation
      .mutateAsync({
        queryParams,
        headers: getCommonHeaders(user),
      })
      .then(() => {
        setDeliveryEstimateErrorMsg('');
      })
      .catch((error: Error) => {
        const errorMessage = (
          (error as FetchError)?.response?.error as {
            error_user_msg: string;
          }
        )?.error_user_msg;
        if (errorMessage) {
          setDeliveryEstimateErrorMsg(errorMessage);
        }
      });
  }, [metaGeoLocations]);

  /**
   * 
   * 1. If Country level, only countries - No overlapping
     2. if type = city, club under cities
        if type = region, club under regions and so on
  */
  const onNewLocationAdd = (location: ILocationDetails) => {
    const updatedGeoLocations = getUpdatedGeoLocationsAfterAddingNewLocation(
      location,
      metaGeoLocations,
    );
    setMetaGeoLocations(updatedGeoLocations);
  };

  const onLocationRemove = (location: ILocationDetails | string) => {
    if (typeof location === 'string') {
      setMetaGeoLocations({
        ...metaGeoLocations,
        countries: metaGeoLocations.countries?.filter(
          (item) => item !== location,
        ),
      });
      return;
    }
    const type = getGeoLocationCategoryType(location);
    if (!metaGeoLocations[type]) {
      return;
    }
    const updatedLocationsOfThisType = (
      metaGeoLocations[type] as ILocationDetails[]
    )?.filter((item) => item.key !== location.key);
    setMetaGeoLocations({
      ...metaGeoLocations,
      [type]: updatedLocationsOfThisType,
    });
  };

  const onSaveDetailsClick = () => {
    logEvent(EVENT_NAMES.target_audience_save_clicked);
    const updatedCampaignDetails: ICampaign = {
      ...campaignDetails,
      details: {
        ...campaignDetails?.details,
        business_details: {
          ...campaignDetails?.details?.business_details,
          consumer_type: consumerType || ConsumerType.All,
          ideal_customers: idealCustomers,
        },
        targeting: {
          ...campaignDetails?.details?.targeting,
          geo_locations: metaGeoLocations,
        },
      },
      google_ads_data: {
        ...googleAdsData,
        geo_locations: googleGeoLocations,
      },
    };
    if (minAge) {
      updatedCampaignDetails.details.targeting.age_min = minAge;
    }
    if (maxAge) {
      updatedCampaignDetails.details.targeting.age_max = maxAge;
    }
    if (genders) {
      updatedCampaignDetails.details.targeting.genders = genders;
    }
    saveCampaignDetails(updatedCampaignDetails, OnboardingStepIds.AUDIENCE);
  };

  const ageAndGenderSelected = minAge || maxAge || genders;
  const geoLocationsSelected =
    adPlatform === AdPlatforms.META
      ? Object.keys(metaGeoLocations).length > 1
      : Object.keys(googleGeoLocations).length > 0;

  return (
    <div className="px-4 pt-4 pb-4 flex flex-col flex-1 h-full">
      <div className="flex flex-col h-full flex-1 overflow-y-scroll no-scrollbar pb-3">
        {adPlatform === AdPlatforms.META ? (
          <div
            className="mt-3 border border-gray-medium rounded-lg px-3 py-2 w-full cursor-pointer"
            onClick={() => {
              setShowSelectLocationBs(true);
            }}
          >
            <div className="flex items-center">
              <p className="flex-1">Select Locations</p>
              <div>
                {geoLocationsSelected ? (
                  <Image
                    src="/images/common/tick-icon.png"
                    width="16"
                    height="16"
                    alt=""
                  />
                ) : (
                  <ChevronRightIcon />
                )}
              </div>
            </div>
            <SelectedGeoLocations
              geoLocations={metaGeoLocations}
              className="mt-2"
              onRemoveLocationClick={onLocationRemove}
            />
            {deliveryEstimateErrorMsg ? (
              <p className="text-red mt-2 text-xxs">
                {deliveryEstimateErrorMsg}
              </p>
            ) : null}
            {geoLocationsSelected ? (
              <p className="text-xs text-hyperlink text-center mt-2 font-medium">
                + Add More
              </p>
            ) : null}
          </div>
        ) : (
          <GoogleSelectLocationsComp
            geoLocations={googleGeoLocations}
            onLocationsChange={(locations) => setGoogleGeoLocations(locations)}
            user={user}
          />
        )}

        <div
          className="mt-5 border border-gray-medium rounded-lg px-3 py-2 w-full cursor-pointer"
          onClick={() => {
            setShowSelectAgeAndGenderBs(true);
          }}
        >
          <div className="flex items-center">
            <p className="flex-1">Select Age and Gender</p>
            <div>
              {ageAndGenderSelected ? (
                <Image
                  src="/images/common/tick-icon.png"
                  width="16"
                  height="16"
                  alt=""
                />
              ) : (
                <ChevronRightIcon />
              )}
            </div>
          </div>
          {ageAndGenderSelected ? (
            <p className="text-gray-dark text-sm mt-1">
              {getAgeAndGenderString()}
            </p>
          ) : null}
        </div>

        <p className="text-sm text-gray-dark mt-8">Your customers type:</p>
        <div className="mt-3 flex items-center rounded-lg bg-gray-light">
          {[ConsumerType.B2B, ConsumerType.B2C, ConsumerType.All].map(
            (item, index) => {
              return (
                <div
                  key={index}
                  className={classNames(
                    'flex-1 p-2 text-center cursor-pointer rounded-lg',
                    {
                      'bg-gray-dark text-white':
                        item === (consumerType || ConsumerType.All),
                    },
                  )}
                  onClick={() => setConsumerType(item)}
                >
                  {item}
                </div>
              );
            },
          )}
        </div>

        {adPlatform === AdPlatforms.META ? (
          <div>
            <p className="text-sm text-gray-dark mt-8">
              Describe your ideal customers
            </p>
            <p className="text-xxs mt-1">We will show ads to these people</p>
            <div className="mt-3 flex">
              <textarea
                className="outline-none border border-gray-dark rounded-lg px-2 py-2 text-sm mr-4 flex-1"
                onChange={(event) => setIdealCustomers(event.target.value)}
                placeholder="e.g. Hoteliers/Students/Corporate Employees/House wives"
                value={idealCustomers}
                rows={5}
              />
            </div>
          </div>
        ) : null}
      </div>
      <Button
        onClick={onSaveDetailsClick}
        disabled={!ageAndGenderSelected || !geoLocationsSelected}
        className="mt-3"
      >
        <p>Save</p>
      </Button>
      {showSelectAgeAndGenderBs ? (
        <SelectAgeAndGenderCompBs
          minAge={minAge}
          maxAge={maxAge}
          onMinAgeChange={(age) => setMinAge(age)}
          onMaxAgeChange={(age) => setMaxAge(age)}
          genders={genders}
          onGendersChange={(genders) => setGenders(genders)}
          onClose={() => setShowSelectAgeAndGenderBs(false)}
        />
      ) : null}
      {showSelectLocationBs ? (
        <SelectLocationBs
          onLocationAdd={onNewLocationAdd}
          onClose={() => setShowSelectLocationBs(false)}
          user={user}
        />
      ) : null}
    </div>
  );
};

export default TargetAudienceComp;
