import React, { useState } from 'react';

const PLAN_COLORS = {
  Starter: '#225E56',
  Growth: '#225E56',
  Agency: '#225E56',
};

const GrowEasyPricingDesktop = () => {
  const [currency, setCurrency] = useState('INR');

  const plans = [
    {
      name: 'Starter',
      idealFor: 'SMBs / Solopreneurs / Freelancers',
      description:
        'Perfect for small and medium businesses\ngetting started with digital marketing',
      price: { INR: '1,499', USD: '19' },
      brands: '1 Brand / Business',
      adSpends: { INR: 'Up to 30K INR', USD: 'Up to $360' },
      adAccount: 'GrowEasy Ad Accounts (20% Service Charge)',
      adOptimisation: 'Yes',
      channels: 'Meta Leads & Get Calls via Google Ads',
      aiBanners: 'Yes',
      aiVideos: 'Yes',
      aiCopy: 'Yes',
      aiInsights: 'Yes',
      color: PLAN_COLORS.Starter,
    },
    {
      name: 'Growth',
      idealFor: 'Funded Startups & Growing D2C',
      description: 'Ideal for growing startups and\ndirect-to-consumer brands',
      price: { INR: '9,999', USD: '129' },
      brands: '1 Brand / Business',
      adSpends: { INR: 'Up to 100K INR', USD: 'Up to $1,200' },
      adAccount:
        'Connect Your Own Ad Accounts or GrowEasy Ad Accounts (20% Service Charge)',
      adOptimisation: 'Yes',
      channels: 'All Campaign Types and Channels',
      aiBanners: 'Yes',
      aiVideos: 'Yes',
      aiCopy: 'Yes',
      aiInsights: 'Yes',
      color: PLAN_COLORS.Growth,
    },
    {
      name: 'Agency',
      idealFor: 'Digital Marketing Agencies',
      description: 'Complete solution for agencies\nmanaging multiple clients',
      price: { INR: '24,999', USD: '299' },
      brands: 'Unlimited Brand / Business',
      adSpends: { INR: 'Unlimited', USD: 'Unlimited' },
      adAccount:
        'Connect Your Own Ad Accounts or GrowEasy Ad Accounts (20% Service Charge)',
      adOptimisation: 'Yes',
      channels: 'All Campaign Types and Channels',
      aiBanners: 'Yes',
      aiVideos: 'Yes',
      aiCopy: 'Yes',
      aiInsights: 'Yes',
      color: PLAN_COLORS.Agency,
    },
  ];

  const features = [
    { name: 'Brands / Business', key: 'brands' },
    { name: 'Monthly Ad Spends', key: 'adSpends' },
    { name: 'Ad Account', key: 'adAccount' },
    { name: 'Ad Optimisation', key: 'adOptimisation' },
    { name: 'Channels', key: 'channels' },
    { name: 'AI Ad Banners', key: 'aiBanners' },
    { name: 'AI Ad Videos', key: 'aiVideos' },
    { name: 'AI Powered Copy & Content', key: 'aiCopy' },
    { name: 'AI Powered Campaign Insights', key: 'aiInsights' },
  ];

  return (
    <div className="px-4 py-8 md:px-8 lg:px-16 xl:px-36 max-w-screen-2xl mx-auto bg-white">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4" style={{ color: '#0A071A' }}>
          Choose Your <span style={{ color: '#225E56' }}>Growth Plan</span>
        </h1>
        <div className="flex justify-end items-center mb-8">
          <div className="flex items-center gap-3">
            <span className="text-[#000000] font-medium text-base">
              Currency:
            </span>
            <div className="flex bg-[#F5F5F5] rounded-xl p-1">
              <button
                onClick={() => setCurrency('INR')}
                className={`px-7 py-2 rounded-lg font-semibold transition-all
          ${
            currency === 'INR'
              ? 'bg-[#225E56] text-white shadow'
              : 'bg-transparent text-black'
          }
        `}
                style={{
                  boxShadow:
                    currency === 'INR'
                      ? '0 2px 8px rgba(34,94,86,0.08)'
                      : undefined,
                }}
              >
                INR
              </button>
              <button
                onClick={() => setCurrency('USD')}
                className={`px-7 py-2 rounded-lg font-semibold transition-all
          ${
            currency === 'USD'
              ? 'bg-[#225E56] text-white shadow'
              : 'bg-transparent text-black'
          }
        `}
                style={{
                  boxShadow:
                    currency === 'USD'
                      ? '0 2px 8px rgba(34,94,86,0.08)'
                      : undefined,
                }}
              >
                USD
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-separate" style={{ borderSpacing: 0 }}>
          <thead>
            <tr>
              <th className="p-4 text-left align-top"></th>
              {plans.map((plan) => (
                <th
                  key={plan.name}
                  className="p-4 text-left align-top"
                  style={{
                    minWidth: 320,
                  }}
                >
                  <div className="mb-4 flex flex-col items-start text-left">
                    {/* Plan Name */}
                    <div
                      className="text-xl font-bold mb-3"
                      style={{
                        color: plan.color,
                        fontFamily: 'Poppins, sans-serif',
                      }}
                    >
                      {plan.name}
                    </div>

                    {/* Ideal For */}
                    <div className="mb-3 flex flex-col items-start">
                      <span
                        className="text-xs font-semibold"
                        style={{
                          color: '#F57141',
                          fontFamily: 'Poppins, sans-serif',
                        }}
                      >
                        Ideal for:
                      </span>
                      <span
                        className="text-xs font-medium mt-1"
                        style={{
                          color: '#F57141',
                          fontFamily: 'Poppins, sans-serif',
                        }}
                      >
                        {plan.idealFor}
                      </span>
                    </div>

                    {/* Description */}
                    <div
                      className="text-sm font-normal whitespace-pre-line"
                      style={{
                        color: '#767676',
                        fontFamily: 'Poppins, sans-serif',
                      }}
                    >
                      {plan.description}
                    </div>
                  </div>

                  {/* Separator line with transparency */}
                  <div className="w-full h-px bg-[#0000001A] my-3"></div>

                  <div className="text-xl font-bold mb-2">
                    <span style={{ color: '#000000' }}>
                      {currency === 'INR'
                        ? `₹${plan.price.INR}`
                        : `$${plan.price.USD}`}
                    </span>
                    <span
                      className="text-base font-normal"
                      style={{ color: '#767676' }}
                    >
                      {' '}
                      per month
                    </span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {features.map((feature, rowIdx) => (
              <tr
                key={feature.key}
                className={rowIdx % 2 === 0 ? 'bg-[#FAFAFA]' : 'bg-white'}
              >
                <td
                  className="p-4 font-medium border-b text-left"
                  style={{ minWidth: 180, color: '#767676' }}
                >
                  {feature.name}
                </td>
                {plans.map((plan) => (
                  <td
                    key={plan.name}
                    className="p-4 text-left border-b text-[#444444]"
                  >
                    {feature.key === 'adSpends'
                      ? currency === 'INR'
                        ? plan.adSpends.INR
                        : plan.adSpends.USD
                      : plan[feature.key]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default GrowEasyPricingDesktop;
