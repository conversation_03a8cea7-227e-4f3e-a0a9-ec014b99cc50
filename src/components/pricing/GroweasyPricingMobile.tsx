import React, { useState } from 'react';

const GrowEasyPricingMobile = () => {
  const [currency, setCurrency] = useState('INR');

  const plans = [
    {
      name: 'Starter',
      idealFor: 'SMBs / Solopreneurs / Freelancers',
      description: 'Perfect for small and medium businesses getting started with digital marketing',
      price: { INR: '1,499', USD: '19' },
      features: [
        { name: 'Brands / Business', value: '1 Brand / Business' },
        {
          name: 'Monthly Ad Spends',
          value: { INR: 'Up to 30K INR', USD: 'Up to $360' },
        },
        {
          name: 'Ad Account',
          value: 'GrowEasy Ad Accounts (20% Service Charge)',
          fullWidth: true,
        },
        {
          name: 'Channels',
          value: 'Meta Leads & Get Calls via Google Ads',
          fullWidth: true,
        },
        { name: 'Ad Optimisation', value: 'Yes' },
        { name: 'AI Ad Banners', value: 'Yes' },
        { name: 'AI Ad Videos', value: 'Yes' },
        { name: 'AI Powered Copy & Content', value: 'Yes' },
        { name: 'AI Powered Campaign Insights', value: 'Yes' },
      ],
      color: '#225E56',
    },
    {
      name: 'Growth',
      idealFor: 'Funded Startups & Growing D2C',
      description: 'Ideal for growing startups and direct-to-consumer brands',
      price: { INR: '9,999', USD: '129' },
      features: [
        { name: 'Brands / Business', value: '1 Brand / Business' },
        {
          name: 'Monthly Ad Spends',
          value: { INR: 'Up to 100K INR', USD: 'Up to $1,200' },
        },
        {
          name: 'Ad Account',
          value: 'Connect Your Own Ad Accounts or GrowEasy Ad Accounts (20% Service Charge)',
          fullWidth: true,
        },
        {
          name: 'Channels',
          value: 'All Campaign Types and Channels',
          fullWidth: true,
        },
        { name: 'Ad Optimisation', value: 'Yes' },
        { name: 'AI Ad Banners', value: 'Yes' },
        { name: 'AI Ad Videos', value: 'Yes' },
        { name: 'AI Powered Copy & Content', value: 'Yes' },
        { name: 'AI Powered Campaign Insights', value: 'Yes' },
      ],
      color: '#225E56',
    },
    {
      name: 'Agency',
      idealFor: 'Digital Marketing Agencies',
      description: 'Complete solution for agencies managing multiple clients',
      price: { INR: '24,999', USD: '299' },
      features: [
        { name: 'Brands / Business', value: 'Unlimited Brand / Business' },
        {
          name: 'Monthly Ad Spends',
          value: { INR: 'Unlimited', USD: 'Unlimited' },
        },
        {
          name: 'Ad Account',
          value: 'Connect Your Own Ad Accounts or GrowEasy Ad Accounts (20% Service Charge)',
          fullWidth: true,
        },
        {
          name: 'Channels',
          value: 'All Campaign Types and Channels',
          fullWidth: true,
        },
        { name: 'Ad Optimisation', value: 'Yes' },
        { name: 'AI Ad Banners', value: 'Yes' },
        { name: 'AI Ad Videos', value: 'Yes' },
        { name: 'AI Powered Copy & Content', value: 'Yes' },
        { name: 'AI Powered Campaign Insights', value: 'Yes' },
      ],
      color: '#225E56',
    },
  ];

  return (
    <div className="min-h-screen bg-white py-6 px-4">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Choose Your <span style={{ color: '#225E56' }}>Growth Plan</span>
        </h1>

        {/* Currency Toggle */}
        <div className="flex justify-center mb-6">
          <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setCurrency('INR')}
              className={`px-5 py-1.5 rounded-md text-sm font-medium transition-colors ${
                currency === 'INR' ? 'text-white' : 'text-gray-700'
              }`}
              style={{
                backgroundColor: currency === 'INR' ? '#225E56' : 'transparent',
              }}
            >
              INR
            </button>
            <button
              onClick={() => setCurrency('USD')}
              className={`px-5 py-1.5 rounded-md text-sm font-medium transition-colors ${
                currency === 'USD' ? 'text-white' : 'text-gray-700'
              }`}
              style={{
                backgroundColor: currency === 'USD' ? '#225E56' : 'transparent',
              }}
            >
              USD
            </button>
          </div>
        </div>
      </div>

      {/* Plans */}
      <div className="space-y-8">
        {plans.map((plan, index) => (
          <div
            key={index}
            className="border border-gray-200 rounded-2xl overflow-hidden"
          >
            {/* Plan Header */}
            <div className="p-5 bg-white">
              <h2 className="text-2xl font-bold mb-2" style={{ color: '#225E56' }}>
                {plan.name}
              </h2>

              <div className="mb-3">
                <span className="text-xs font-semibold" style={{ color: '#F57141' }}>
                  Ideal for :
                </span>
                <span className="text-sm font-medium ml-1" style={{ color: '#F57141' }}>
                  {plan.idealFor}
                </span>
              </div>

              <p className="text-sm text-gray-600 mb-4">{plan.description}</p>

              <div className="flex items-end gap-2 mb-4">
                <span className="text-2xl font-bold text-gray-900">
                  {currency === 'INR' ? `₹${plan.price.INR}` : `$${plan.price.USD}`}
                </span>
                <span className="text-sm text-gray-500 mb-1">per month</span>
              </div>
            </div>

            {/* Features */}
            <div className="border-t border-gray-200">
              <div className="p-0">
                {plan.features.map((feature, idx) => (
                  <div key={idx}>
                    <div className={`p-3 ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                      {feature.fullWidth ? (
                        <div className="flex flex-col">
                          <div className="text-sm font-medium mb-1" style={{ color: '#767676' }}>
                            {feature.name}
                          </div>
                          <div className="text-xs font-medium text-gray-800 mt-1 leading-tight">
                            {typeof feature.value === 'object'
                              ? currency === 'INR'
                                ? feature.value.INR
                                : feature.value.USD
                              : feature.value}
                          </div>
                        </div>
                      ) : (
                        <div className="flex justify-between">
                          <div className="text-sm font-medium pr-2" style={{ color: '#767676' }}>
                            {feature.name}
                          </div>
                          <div className="text-xs font-medium text-gray-800 text-right flex-1 max-w-[60%] leading-tight">
                            {typeof feature.value === 'object'
                              ? currency === 'INR'
                                ? feature.value.INR
                                : feature.value.USD
                              : feature.value}
                          </div>
                        </div>
                      )}
                    </div>
                    {idx !== plan.features.length - 1 && (
                      <div className="h-px bg-gray-200 w-full"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default GrowEasyPricingMobile;
