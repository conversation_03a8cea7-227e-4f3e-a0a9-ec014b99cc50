'use client';
import React, { useState } from 'react';

const FAQ_DATA = [
  {
    heading: 'Who is this course for?',
    body: 'This course is ideal for startup founders, small business owners, freelancers, and aspiring entrepreneurs looking to understand and implement digital marketing strategies for rapid growth.',
  },
  {
    heading: 'What will I learn in this masterclass?',
    body: 'You will learn: \n- How to kickstart digital marketing for your business. \n- Techniques to identify and engage with your target audience effectively. \n- Choosing the right digital marketing channels. \n- Crafting impactful communication and creative strategies.',
  },
  {
    heading: 'Why is this masterclass unique?',
    body: 'The masterclass combines actionable insights with real-world experience from <PERSON><PERSON>, who has helped over 50 startups achieve impactful growth. Learn growth-focused techniques tailored for startups with limited budgets.',
  },
  {
    heading: 'How long is the masterclass?',
    body: 'The masterclass is a 1-hour session packed with practical strategies and real-world examples.',
  },
  {
    heading: 'Will I get a recording of the session?',
    body: 'Yes, all attendees will receive a recording of the masterclass to revisit the strategies shared.',
  },
  {
    heading: 'Is there a money-back guarantee?',
    body: 'Unfortunately, we do not offer refunds. However, this masterclass is packed with high-value insights designed to deliver exceptional ROI.',
  },
  {
    heading: ' How do I join the session?',
    body: 'After registration, you will receive an email with a link to join the live session. Ensure you register early to secure your spot.',
  },
];

const FAQs = () => {
  return (
    <div>
      <h3 className=" text-[32px] sm:text[36px] lg:text-[42px] font-bold min-w-[25%] text-center ">
        <span className=" text-[#F57141]">FAQs: </span>
        Here&apos;s everything you may ask...
      </h3>
      <div className=" mt-6 sm:mt-9 lg:mt-[50px]  ">
        {FAQ_DATA.map((item, index) => (
          <FaqCard {...item} key={index} />
        ))}
      </div>
    </div>
  );
};

function FaqCard({ heading, body }: { heading: string; body: string }) {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <div
      className={` group flex flex-col py-6 px-6 border-b cursor-pointer backdrop-blur-sm  transition-all duration-500 overflow-hidden `}
      onClick={() => setIsOpen(!isOpen)}
    >
      <div className="flex justify-between items-center  gap-4 group-hover:scale-[100.5%] group-hover:-translate-y-0.5 transition-all duration-200 ">
        <div
          className={` text-[20px] sm:text-[22px] lg:text-[24px] select-none transition-all duration-500 ${
            isOpen && ' !text-secondary '
          } `}
        >
          {heading}
        </div>

        <div className=" relative w-4 h-4 flex-shrink-0 ">
          <div className=" absolute top-1/2 translate-y-1/2 left-0 w-full border-b-2 border-black " />
          <div
            className={`  absolute top-1/2 translate-y-1/2 left-0 w-full border-b-2 border-black transition-all duration-500 ${
              isOpen ? 'rotate-0' : 'rotate-90'
            }`}
          />
        </div>
      </div>
      <div
        className={`${
          isOpen ? 'max-h-[500px] ' : 'max-h-0  '
        } transition-all duration-500 `}
      >
        <div className=" h-5 " />
        <div
          className={` text-[18px] sm:text-[20px] lg:text-[22px] text-[#767676] whitespace-pre-wrap `}
        >
          {body}
        </div>
      </div>
    </div>
  );
}

export default FAQs;
