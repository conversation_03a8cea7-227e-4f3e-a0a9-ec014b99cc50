import Image from 'next/image';
import React from 'react';
import TickIcon from '@/images/common/tick.svg';

const MENTOR_POINT_DATA = [
  {
    body: '<PERSON><PERSON>dya’s masterclass equips startup founders with actionable marketing strategies.',
  },
  {
    body: 'Focused on overcoming brand-building challenges unique to startups.',
  },
  {
    body: 'Provides growth-focused techniques that deliver real results, even on a limited budget.',
  },
  {
    body: 'Combines practical insights with <PERSON><PERSON>’s real-world experience to drive immediate impact for early-stage entrepreneurs.',
  },
  {
    body: 'Draws from <PERSON><PERSON> Pandya’s extensive experience in modern marketing and branding.',
  },
];

const MeetMentor = () => {
  return (
    <div>
      <h3 className=" text-[32px] sm:text[36px] lg:text-[42px] font-bold min-w-[25%] text-center ">
        Meet Your <span className=" text-[#F57141]">Mentor</span>
      </h3>
      <div className=" mt-6 sm:mt-9 lg:mt-[50px] p-4 sm:p-9 lg:p-[50px] border border-[#c5c5c5] rounded-xl shadow-lg shadow-[rgba(245,113,65,0.8)] flex max-lg:flex-col lg:flex-row-reverse gap-x-9 gap-y-5 ">
        <div className=" relative flex shrink-0 w-full sm:w-[60%] lg:w-[40%] max-lg:self-center ">
          <Image
            src="/images/digital-marketing-masterclass/tej-masterclass-image-large.jpg"
            width={758}
            height={818}
            alt="Tej Pandya, Co-Founder and CEO"
            className="object-cover w-full h-full rounded-lg "
          />
          <div className=" absolute bottom-[5%] space-y-3 lg:space-y-5 left-1/2 -translate-x-1/2 ">
            <div className=" flex max-[375px]:text-[12px] text-sm md:text-base lg:text-xl gap-1.5 p-3 justify-center bg-[#131C1B] text-white border border-white rounded-lg ">
              <strong className=" whitespace-nowrap">7+ yr </strong>
              <p className="whitespace-nowrap text-[#BFBFBF] ">
                {' '}
                Startup Experience
              </p>
            </div>
            <div className=" flex max-[375px]:text-[12px] text-sm md:text-base lg:text-xl gap-1.5 p-3 justify-center bg-[#131C1B] text-white border border-white rounded-lg ">
              <strong className="whitespace-nowrap">3+ cr</strong>{' '}
              <p className="whitespace-nowrap text-[#BFBFBF] ">
                {' '}
                Monthly Budget Managed
              </p>
            </div>
            <div className=" flex max-[375px]:text-[12px] text-sm md:text-base lg:text-xl gap-1.5 p-3 justify-center bg-[#131C1B] text-white border border-white rounded-lg ">
              <strong className="whitespace-nowrap">50+</strong>{' '}
              <p className="whitespace-nowrap text-[#BFBFBF] ">
                {' '}
                Startups Helped
              </p>
            </div>
          </div>
        </div>
        <div className=" space-y-4 md:space-y-6 lg:space-y-8 ">
          <p className=" text-[24px] sm:text-[28px] lg:text-[32px] font-medium ">
            Hi! My name is Tej Pandya
          </p>
          <p className=" text-[20px] sm:text-[22px] lg:text-[24px] ">
            Tej Pandya is founder to AI powered digital marketing app GrowEasy
            and digital creator known for empowering young entrepreneurs with
            actionable insights on business, growth, and marketing. With his
            expertise in Digital Marketing, he’s helped countless startups
            thrive in today’s competitive landscape.
          </p>
          <p className=" text-[24px] sm:text-[28px] lg:text-[32px] font-medium ">
            Why This Masterclass with Tej Pandya?
          </p>
          <div className=" space-y-6">
            {MENTOR_POINT_DATA.map((item, index) => (
              <MentorPoint body={item.body} key={index} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

function MentorPoint({ body }: { body: string }) {
  return (
    <div className=" flex gap-5 items-baseline ">
      <div className=" w-[30px] h-[30px] rounded-full bg-[#F57141] flex-shrink-0 flex items-center justify-center ">
        <TickIcon className=" w-4 h-4 text-white " />
      </div>
      <p className=" text-[20px] sm:text-[22px] lg:text-[24px] ">{body}</p>
    </div>
  );
}

export default MeetMentor;
