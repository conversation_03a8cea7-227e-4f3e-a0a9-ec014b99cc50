import React from 'react';
import { DialogTrigger } from '../lib/Dialog';
import { getFormattedDateString } from 'src/utils';
import { getMasterClassDate } from 'src/utils/master_class';

const Footer = () => {
  const masterclassDateString = getFormattedDateString(getMasterClassDate(), {
    weekday: 'long',
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  });

  return (
    <footer className="  max-sm:fixed bottom-0 w-full bg-secondary py-5 lg:py-9  ">
      <div className=" max-w-7xl mx-auto max-[380px]:px-2 px-4 sm:px-6 flex gap-2 justify-between items-center ">
        <div>
          <p className=" text-[12px] sm:text-[16px] lg:text-[20px] font-medium text-white ">
            Join Now for{' '}
            <span className=" text-[16px] font-bold sm:text-[24px] lg:text-[38px]">
              ₹199
            </span>{' '}
            <s>₹499</s>
          </p>
          <p className=" text-[12px] sm:text-[16px] lg:text-[20px] font-medium text-white ">
            Deadline <span className=" font-bold">{masterclassDateString}</span>
          </p>
        </div>
        <div className=" relative flex-shrink-0 self-end ">
          <DialogTrigger>
            <button className=" uppercase text-[12px] sm:text-[20px] lg:text-[28px] bg-white py-2.5 sm:py-5 max-[380px]:px-8 px-10 sm:px-[5.625rem] rounded-full font-medium tracking-widest ">
              Join Now
            </button>
            <button className=" absolute text-[8px] sm:text-[14px] lg:text-[20px]  bottom-7 sm:bottom-12 lg:bottom-16 shadow-lg  right-3 px-3.5 sm:px-8 py-1.5 sm:py-3 rounded-full bg-[#131C1B] text-white ">
              <strong>20</strong> Seats Left
            </button>
          </DialogTrigger>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
