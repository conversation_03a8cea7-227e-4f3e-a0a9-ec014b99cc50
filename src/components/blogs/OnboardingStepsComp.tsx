import Image from 'next/image';
import Link from 'next/link';

interface IOnboardingStepsCompProps {
  className?: string;
}

const OnboardingStepsComp = (props: IOnboardingStepsCompProps) => {
  const { className = '' } = props;

  return (
    <div className={`mt-3 text-sm ${className}`}>
      <p className="mt-3">
        1. First step is to{' '}
        <Link href="/login" className="text-hyperlink">
          Login to GrowEasy
        </Link>
      </p>
      <div className="w-60 mt-3">
        <Image
          src="/images/blogs/onboarding-steps/google-login.jpeg"
          width="540"
          height="1206"
          alt="groweasy-login"
        />
      </div>

      <p className="mt-5">
        2. Next, create a new campaign by clicking the plus icon on the
        dashboard. You will need to choose the platforms: Facebook/Instagram,
        WhatsApp, or Google.
      </p>
      <div className="w-60 mt-3">
        <Image
          src="/images/blogs/onboarding-steps/choose-campaign-type.png"
          width="540"
          height="1206"
          alt="groweasy-choose-campaign-type"
        />
      </div>

      <p className="mt-5">3. Fill in your business details.</p>
      <div className="w-60 mt-3">
        <Image
          src="/images/blogs/onboarding-steps/fill-business-details.jpeg"
          width="540"
          height="1206"
          alt="groweasy-fill-business-details"
        />
      </div>

      <p className="mt-5">4. Choose demographics and specify your audience.</p>
      <div className="w-60 mt-3">
        <Image
          src="/images/blogs/onboarding-steps/select-target-audience.jpeg"
          width="540"
          height="1206"
          alt="groweasy-select-target-audience"
        />
      </div>

      <p className="mt-5">
        5. Based on your details, AI will generate ad creatives. Choose 3
        banners from the generated options, or upload your own custom creatives.
      </p>

      <p className="mt-5">6. Choose an ad budget plan.</p>
      <div className="w-60 mt-3">
        <Image
          src="/images/blogs/onboarding-steps/choose-ad-budget.jpeg"
          width="540"
          height="1206"
          alt="groweasy-choose-ad-budget"
        />
      </div>

      <p className="mt-5">
        7. The last step is to launch the campaign by completing your payment.
      </p>

      <p className="mt-5">
        8. Once your ad campaign is live, we will deliver generated leads in
        real-time to your email and WhatsApp. You can also visit the dashboard
        and download all leads in CSV format.
      </p>
    </div>
  );
};

export default OnboardingStepsComp;
