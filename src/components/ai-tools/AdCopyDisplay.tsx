import React from 'react';
import { FaCopy } from 'react-icons/fa';
import { IAdCopy } from '../../../src/types/ad_copywriter';
import { showToastMessage } from 'src/modules/toast';

interface AdCopyDisplayProps {
  adCopies?: IAdCopy[];
}

export const AdCopyDisplay: React.FC<AdCopyDisplayProps> = ({ adCopies }) => {
  const copyToClipboard = async (text: string) => {
    await navigator.clipboard.writeText(text);
    showToastMessage('Text copied!', 'success');
  };

  if (!adCopies || !Array.isArray(adCopies) || adCopies.length === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        No ad copies available
      </div>
    );
  }

  const renderAdCopy = (adCopy: IAdCopy, index: number) => {
    const isGoogleFormat =
      adCopy.short_headline || adCopy.long_headline || adCopy.short_description;

    const getDisplayData = () => {
      if (isGoogleFormat) {
        return {
          headline:
            adCopy.long_headline ||
            adCopy.headline ||
            adCopy.short_headline ||
            '',
          shortHeadline: adCopy.short_headline || '',
          primaryText: adCopy.description || '',
          description: adCopy.short_description || '',
          cta: null,
        };
      } else {
        return {
          headline: adCopy.headline || '',
          shortHeadline: null,
          primaryText: adCopy.primary_text || '',
          description: adCopy.description || '',
          cta: adCopy.call_to_action_type || '',
        };
      }
    };

    const displayData = getDisplayData();

    const getCopyText = () => {
      if (isGoogleFormat) {
        const parts = [];
        if (displayData.shortHeadline)
          parts.push(`Short Headline: ${displayData.shortHeadline}`);
        if (displayData.headline)
          parts.push(`Long Headline: ${displayData.headline}`);
        if (displayData.primaryText)
          parts.push(`Description: ${displayData.primaryText}`);
        if (displayData.description)
          parts.push(`Short Description: ${displayData.description}`);
        return parts.join('\n');
      } else {
        const parts = [];
        if (displayData.headline)
          parts.push(`Headline: ${displayData.headline}`);
        if (displayData.primaryText)
          parts.push(`Primary Text: ${displayData.primaryText}`);
        if (displayData.description)
          parts.push(`Description: ${displayData.description}`);
        if (displayData.cta) parts.push(`CTA: ${displayData.cta}`);
        return parts.join('\n');
      }
    };

    return (
      <div
        key={index}
        className="bg-gradient-to-br from-teal-50 to-blue-50 border border-teal-200 rounded-xl p-6 mb-4"
      >
        <div className="flex justify-between items-start mb-4">
          <h4 className="text-lg font-semibold text-teal-700">
            {isGoogleFormat ? 'Google Ad' : 'Ad'} #{index + 1}
          </h4>
          <button
            type="button"
            onClick={() => {
              const copyText = getCopyText();
              if (copyText.trim()) {
                void copyToClipboard(copyText).catch((error) => {
                  console.error('Failed to copy to clipboard:', error);
                });
              }
            }}
            className="text-teal-600 hover:text-teal-800 transition-colors"
            title="Copy to clipboard"
          >
            <FaCopy className="w-4 h-4" />
          </button>
        </div>

        <div className="space-y-3">
          {isGoogleFormat ? (
            <>
              {displayData.shortHeadline && (
                <div>
                  <span className="text-sm font-medium text-gray-600">
                    Short Headline:
                  </span>
                  <p className="text-gray-900 font-semibold font-bricolage-grotesque text-lg">
                    {displayData.shortHeadline}
                  </p>
                </div>
              )}

              {displayData.headline && (
                <div>
                  <span className="text-sm font-medium text-gray-600">
                    Long Headline:
                  </span>
                  <p className="text-gray-900 font-semibold font-bricolage-grotesque text-xl">
                    {displayData.headline}
                  </p>
                </div>
              )}

              {displayData.primaryText && (
                <div>
                  <span className="text-sm font-medium text-gray-600/80">
                    Description:
                  </span>
                  <p className="text-gray-900">{displayData.primaryText}</p>
                </div>
              )}

              {displayData.description && (
                <div>
                  <span className="text-sm font-medium text-gray-600/80">
                    Short Description:
                  </span>
                  <p className="text-gray-900">{displayData.description}</p>
                </div>
              )}
            </>
          ) : (
            <>
              {displayData.headline && (
                <div>
                  <span className="text-sm font-medium text-gray-600">
                    Headline:
                  </span>
                  <p className="text-gray-900 font-semibold font-bricolage-grotesque text-xl">
                    {displayData.headline}
                  </p>
                </div>
              )}

              {displayData.primaryText && (
                <div>
                  <span className="text-sm font-medium text-gray-600/80">
                    Primary Text:
                  </span>
                  <p className="text-gray-900">{displayData.primaryText}</p>
                </div>
              )}

              {displayData.description && (
                <div>
                  <span className="text-sm font-medium text-gray-600/80">
                    Description:
                  </span>
                  <p className="text-gray-900">{displayData.description}</p>
                </div>
              )}

              {displayData.cta && (
                <div>
                  <span className="text-sm font-medium text-gray-600">
                    CTA :{' '}
                  </span>
                  <span className="inline-block bg-teal-100 text-teal-800 px-4 py-2 border border-teal-600 rounded-full text-sm font-bold cursor-pointer hover:scale-105 transition-all active:scale-95">
                    {typeof displayData.cta === 'string'
                      ? displayData.cta.replaceAll('_', ' ')
                      : displayData.cta}
                  </span>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {adCopies.map((adCopy, index) => renderAdCopy(adCopy, index))}
    </div>
  );
};
