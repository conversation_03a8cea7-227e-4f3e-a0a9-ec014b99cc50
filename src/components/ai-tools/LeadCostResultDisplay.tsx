import React from 'react';

interface LeadCostResult {
  costRange: string;
  industry: string;
  city: string;
  channel: string;
  productPrice: string;
}

interface LeadCostResultDisplayProps {
  result: LeadCostResult;
}

export const LeadCostResultDisplay: React.FC<LeadCostResultDisplayProps> = ({
  result,
}) => {
  return (
    <div className="bg-gray-50 rounded-xl p-4 sm:p-6 border border-gray-100">
      <div className="text-center mb-4 sm:mb-6">
        <div className="text-2xl sm:text-3xl font-bold text-primary2 mb-1">
          {result.costRange}
        </div>
        <div className="text-xs sm:text-sm text-gray-600">Industry Average</div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 text-xs sm:text-sm mb-4 sm:mb-6">
        <div className="flex justify-between">
          <span className="text-gray-600">City:</span>
          <span className="font-medium ml-1">{result.city}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Channel:</span>
          <span className="font-medium ml-1">{result.channel}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Product Price:</span>
          <span className="font-medium ml-1">{result.productPrice}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600">Industry:</span>
          <span className="font-medium ml-1">{result.industry}</span>
        </div>
      </div>
    </div>
  );
};
