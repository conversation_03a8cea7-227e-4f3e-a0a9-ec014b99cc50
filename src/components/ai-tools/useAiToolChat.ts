import { useState, useRef, useEffect } from 'react';
import { BaseMessage, BaseSession } from './AiToolChatInterface';

export function useAiToolChat<
  TMessage extends BaseMessage,
  TSession extends BaseSession,
>() {
  const [chatStarted, setChatStarted] = useState(false);
  const [messages, setMessages] = useState<TMessage[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [session, setSession] = useState<TSession | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return {
    chatStarted,
    setChatStarted,
    messages,
    setMessages,
    currentInput,
    setCurrentInput,
    showEmailModal,
    setShowEmailModal,
    session,
    setSession,
    messagesEndRef,
  };
}
