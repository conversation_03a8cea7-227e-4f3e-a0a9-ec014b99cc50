'use client';

import { useEffect, useRef, ReactNode } from 'react';
import { FaPaperPlane } from 'react-icons/fa';
import { UseMutationResult } from 'react-query';
import EmailModal from '@/components/agents/EmailModal';
import AiToolsLayout from './AiToolsLayout';

export interface BaseMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  loading?: boolean;
  apiResponse?: boolean;
}

export interface BaseSession {
  sessionId: string;
  email: string;
  conversationData: Record<string, unknown>;
}

interface AiToolChatInterfaceProps<
  TMessage extends BaseMessage,
  TSession extends BaseSession,
> {
  toolName: string;
  toolIcon: ReactNode;
  toolDescription: string;
  welcomeTitle: string;
  welcomeDescription: string;
  messages: TMessage[];
  setMessages: React.Dispatch<React.SetStateAction<TMessage[]>>;
  currentInput: string;
  setCurrentInput: React.Dispatch<React.SetStateAction<string>>;
  chatStarted: boolean;
  setChatStarted: React.Dispatch<React.SetStateAction<boolean>>;
  session: TSession | null;
  setSession: React.Dispatch<React.SetStateAction<TSession | null>>;
  showEmailModal: boolean;
  setShowEmailModal: React.Dispatch<React.SetStateAction<boolean>>;
  sendMessageMutation: UseMutationResult<unknown, Error, unknown, unknown>;
  onEmailSubmit: (email: string) => void;
  onSendMessage: (content: string) => void;
  renderMessageContent: (message: TMessage) => ReactNode;
  children?: ReactNode;
}

export default function AiToolChatInterface<
  TMessage extends BaseMessage,
  TSession extends BaseSession,
>({
  toolName,
  toolIcon,
  toolDescription,
  welcomeTitle,
  welcomeDescription,
  messages,
  setMessages, // eslint-disable-line @typescript-eslint/no-unused-vars
  currentInput,
  setCurrentInput,
  chatStarted,
  setChatStarted, // eslint-disable-line @typescript-eslint/no-unused-vars
  session, // eslint-disable-line @typescript-eslint/no-unused-vars
  setSession, // eslint-disable-line @typescript-eslint/no-unused-vars
  showEmailModal,
  setShowEmailModal,
  sendMessageMutation,
  onEmailSubmit,
  onSendMessage,
  renderMessageContent,
  children,
}: AiToolChatInterfaceProps<TMessage, TSession>) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = () => {
    if (!currentInput.trim()) return;
    onSendMessage(currentInput);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <AiToolsLayout>
      {/* Header */}
      <div className="bg-white border-b px-4 sm:px-6 py-4 shadow">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary2 rounded-full flex items-center justify-center">
            {toolIcon}
          </div>
          <div className="min-w-0">
            <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">
              {toolName}
            </h1>
            <p className="text-xs sm:text-sm text-gray-600 truncate">
              {toolDescription}
            </p>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-y-auto">
        {!chatStarted ? (
          /* Welcome Screen */
          <div className="flex flex-col items-center justify-center h-full px-4 sm:px-6">
            <div className="text-center max-w-3xl">
              <h2 className="text-2xl sm:text-4xl md:text-5xl tracking-tight font-semibold mb-4">
                {welcomeTitle}
              </h2>
              <p className="text-lg sm:text-xl text-gray-600 mt-6 sm:mt-10 mb-6">
                {welcomeDescription}
              </p>

              <div className="flex gap-3 max-w-2xl mx-auto relative">
                <input
                  type="text"
                  value={currentInput}
                  onChange={(e) => setCurrentInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSendMessage();
                    }
                  }}
                  placeholder="Type your message..."
                  className="flex-1 px-4 sm:px-8 py-3 sm:py-5 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent placeholder-gray-500 w-full text-sm sm:text-base"
                  disabled={sendMessageMutation.isLoading}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={
                    !currentInput.trim() || sendMessageMutation.isLoading
                  }
                  className="px-4 sm:px-6 py-3 bg-primary2 hover:bg-teal-800 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-full transition-colors flex items-center justify-center absolute h-full right-0"
                >
                  {sendMessageMutation.isLoading ? (
                    <div className="animate-spin w-4 h-4 sm:w-5 sm:h-5 border-2 border-white border-t-transparent rounded-full"></div>
                  ) : (
                    <FaPaperPlane className="w-4 h-4 sm:w-5 sm:h-5" />
                  )}
                </button>
              </div>
            </div>
          </div>
        ) : (
          /* Chat Messages */
          <div className="p-4 sm:p-6 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex animate-fade-in gap-2 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.role === 'assistant' && (
                  <img
                    src={'/images/groweasy-logo-square.png'}
                    alt="GE"
                    width={40}
                    height={40}
                    className="w-8 h-8 sm:w-10 sm:h-10 shrink-0 rounded-full border border-gray-200 overflow-hidden"
                  />
                )}

                <div
                  className={`max-w-2xl ${
                    message.role === 'user' ? 'order-1' : ''
                  }`}
                >
                  <div
                    className={`rounded-2xl text-sm md:text-base px-4 sm:px-6 py-3 transition-all shadow-sm hover:shadow ${
                      message.role === 'user'
                        ? 'bg-primary2 text-white ml-auto rounded-tr-none'
                        : 'bg-white border border-gray-200 rounded-tl-none'
                    }`}
                  >
                    {message.loading ? (
                      <div className="flex items-center gap-3">
                        <div className="animate-spin w-4 h-4 border-2 border-teal-500 border-t-transparent rounded-full"></div>
                        <span className="text-gray-700 italic">
                          {message.content}
                        </span>
                      </div>
                    ) : (
                      renderMessageContent(message)
                    )}
                  </div>
                </div>

                {message.role === 'user' && (
                  <img
                    src={`https://xvatar.vercel.app/api/avatar/${session?.email}.svg?rounded=25&size=50&userLogo=true`}
                    alt="u"
                    width={40}
                    height={40}
                    className="w-8 h-8 sm:w-10 sm:h-10 rounded-full border border-gray-200 overflow-hidden order-2"
                  />
                )}
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Input Area for Chat */}
      {chatStarted && (
        <div className="border-t backdrop-blur-md bg-off-white/60 p-4 sm:p-6">
          <div className="flex">
            <textarea
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              className="w-full xs:flex-1 border border-gray-300 rounded-s-2xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-primary2 focus:border-transparent resize-none max-h-[120px] text-sm sm:text-base"
              rows={1}
              disabled={sendMessageMutation.isLoading}
            />
            <button
              onClick={handleSendMessage}
              disabled={!currentInput.trim() || sendMessageMutation.isLoading}
              className="w-fit bg-primary2 hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-3 rounded-e-2xl transition-all flex items-center justify-center active:scale-95"
            >
              <FaPaperPlane className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      <EmailModal
        isOpen={showEmailModal}
        onClose={() => setShowEmailModal(false)}
        onSubmit={onEmailSubmit}
        loading={sendMessageMutation.isLoading}
      />

      {children}
    </AiToolsLayout>
  );
}
