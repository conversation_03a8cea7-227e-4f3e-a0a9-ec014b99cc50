'use client';
import Image from 'next/image';
import Link from 'next/link';
import { BiBook, BiSearch } from 'react-icons/bi';
import { BsPeople } from 'react-icons/bs';
import { FaChartBar, FaMoneyBill, FaNewspaper } from 'react-icons/fa';

const AiToolsLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex h-screen font-poppins">
      <div className="hidden lg:flex w-80 bg-primary2 text-white p-6 flex-col">
        <div className="flex items-center mb-8">
          <Image
            src={'/images/groweasy-logo-square.png'}
            alt="Groweasy Logo"
            width={40}
            height={40}
          />
          <p className="text-xl font-semibold tracking-tight ml-3">GrowEasy</p>
        </div>

        <nav className="space-y-4 flex-1">
          <Link href="/blogs">
            <button className="w-full flex items-center justify-start px-4 py-3 text-white hover:bg-teal-600 rounded-lg transition-colors">
              <BiBook className="w-5 h-5 mr-3" />
              Free Blogs by GrowEasy
            </button>
          </Link>
          <Link href="/cost-per-lead-by-industry-and-marketing-channels">
            <button className="w-full flex items-center justify-start px-4 py-3 text-white hover:bg-teal-600 rounded-lg transition-colors">
              <FaChartBar className="w-5 h-5 mr-3" />
              CPL Benchmark Explorer
            </button>
          </Link>
          <Link href="/marketing-ai-tools/lead-cost-calculator">
            <button className="w-full flex items-center justify-start px-4 py-3 text-white hover:bg-teal-600 rounded-lg transition-colors">
              <FaMoneyBill className="w-5 h-5 mr-3" />
              Lead Cost Calculator
            </button>
          </Link>
          <Link href="/marketing-ai-tools/ai-ad-copywriter">
            <button className="w-full flex items-center justify-start px-4 py-3 text-white hover:bg-teal-600 rounded-lg transition-colors">
              <FaNewspaper className="w-5 h-5 mr-3" />
              AI Ad Copywriter
            </button>
          </Link>
          <Link href="/tools/google-keyword-ideas-generator">
            <button className="w-full flex items-center justify-start px-4 py-3 text-white hover:bg-teal-600 rounded-lg transition-colors">
              <BiSearch className="w-5 h-5 mr-3" />
              Keyword Planner
            </button>
          </Link>
          <Link href="/tools/facebook-ads-audience-builder">
            <button className="w-full flex items-start justify-start px-4 py-3 text-white hover:bg-teal-600 rounded-lg transition-colors">
              <BsPeople className="w-5 h-5 mr-3" />
              Meta Audience Generator
            </button>
          </Link>
        </nav>
        <Link href="/marketing-ai-tools">
          <button className="w-full flex items-center justify-center px-4 py-3 mt-auto bg-transparent border-2 border-teal-500 text-white hover:bg-teal-600 rounded-lg transition-colors shadow-lg">
            <BiSearch className="w-6 h-6 mr-2" />
            Explore More Marketing Tools
          </button>
        </Link>
      </div>
      <div className="flex-1 flex flex-col min-h-0">{children}</div>
    </div>
  );
};

export default AiToolsLayout;
