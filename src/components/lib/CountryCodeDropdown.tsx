import Image from 'next/image';
import Dropdown from '@/components/lib/Dropdown';
import DownArrow from '@/images/common/down-arrow.svg';
import COUNTRIES from '@/constants/countries';
import { useMemo } from 'react';

interface ICountryCodeDropdownProps {
  countryCode: string;
  setCountryCode: React.Dispatch<React.SetStateAction<string>>;
  className?: string;
}

const CountryCodeDropdown = (props: ICountryCodeDropdownProps) => {
  const { countryCode, setCountryCode, className } = props;
  const countryShortForm = useMemo(
    () => COUNTRIES.find((item) => item.dial_code === countryCode)?.code,
    [countryCode],
  );
  return (
    <div className=" relative flex ">
      <div
        className={`flex items-center gap-2 border border-gray-400 rounded-lg p-0.5 px-3 w-[120px] relative ${className}`}
      >
        {countryShortForm && (
          <Image
            src={`https://flagcdn.com/${countryShortForm.toLowerCase()}.svg`}
            width="30"
            height="30"
            alt="Ukraine"
            className=" w-fit h-4 object-cover "
          />
        )}
        <span className=" text-sm ">{countryCode}</span>
        <DownArrow
          width="24"
          height="24"
          className="text-gray-dark absolute top-1/2 -translate-y-1/2 right-0.5 pointer-events-none select-none"
        />
      </div>
      <Dropdown
        className=" absolute inset-0 h-full !m-0 opacity-0 "
        value={countryCode ?? '+91'}
        onChange={(value) => setCountryCode(value)}
      >
        <option value="" disabled>
          Select Country Code
        </option>
        {COUNTRIES.map((item, index) => {
          return (
            <option key={index} value={item.dial_code}>
              {item.name} ({item.dial_code})
            </option>
          );
        })}
      </Dropdown>
    </div>
  );
};

export default CountryCodeDropdown;
