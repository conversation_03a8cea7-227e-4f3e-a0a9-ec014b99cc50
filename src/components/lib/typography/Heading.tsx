import React from 'react';

interface IHeadingProps extends React.HtmlHTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
  level: number;
}

const Heading = (props: IHeadingProps) => {
  const { children, level, className, ...remainingProps } = props;
  const baseClass = ' font-poppins ';
  switch (level) {
    case 1:
      return (
        <h1
          className={` ${baseClass} text-[32px] sm:text-[36px] md:text-[48px] lg:text-[50px] xl:text-[60px] font-bold leading-[150%] ${className}`}
          {...remainingProps}
        >
          {children}
        </h1>
      );
    case 2:
      return (
        <h2 className={` ${className}`} {...remainingProps}>
          {children}
        </h2>
      );
    case 3:
      return (
        <h3
          className={` font-poppins text-xl sm:text-2xl md:text-3xl font-medium  ${className}`}
          {...remainingProps}
        >
          {children}
        </h3>
      );
    case 4:
      return (
        <h4
          className={` font-poppins text-base sm:text-lg md:text-xl lg:text-2xl font-semibold  ${className}`}
          {...remainingProps}
        >
          {children}
        </h4>
      );
    case 5:
      return <h5 className={` ${className}`}>{children}</h5>;
    case 6:
      return <h6 className={` ${className}`}>{children}</h6>;
    default:
      return <h1 className={` ${className}`}>{children}</h1>;
  }
};

export default Heading;
