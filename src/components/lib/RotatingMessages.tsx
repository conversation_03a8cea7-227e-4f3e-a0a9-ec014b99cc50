import { useState, useEffect } from 'react';

interface RotatingMessagesProps {
  messages: string[];
  interval?: number;
  className?: string;
}

const RotatingMessages = ({
  messages,
  interval = 3000,
  className = '',
}: RotatingMessagesProps) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const fadeOutTimeout = setTimeout(() => {
      setIsVisible(false);
    }, interval - 500);

    const messageInterval = setInterval(() => {
      setCurrentMessageIndex((prevIndex) => (prevIndex + 1) % messages.length);
      setIsVisible(true);
    }, interval);

    return () => {
      clearInterval(messageInterval);
      clearTimeout(fadeOutTimeout);
    };
  }, [messages.length, interval, currentMessageIndex]);

  return (
    <div className={`h-6 overflow-hidden ${className}`}>
      <p
        className={`
          text-center font-medium transition-all duration-500 ease-in-out
          ${isVisible ? 'opacity-100 transform-none' : 'opacity-0 -translate-y-3'}
        `}
      >
        {messages[currentMessageIndex]}
      </p>
    </div>
  );
};

export default RotatingMessages;
