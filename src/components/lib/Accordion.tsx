import classNames from 'classnames';
import { ReactNode, useState } from 'react';
import DownArrow from '@/images/common/down-arrow.svg';

export interface IAccordionProps {
  children: ReactNode;
  className?: string;
  title: string;
  id?: string;
  isOpen?: boolean;
  onToggle?: (id: string, isOpen: boolean) => void;
}

const Accordion = (props: IAccordionProps) => {
  const { children, className = '', title, id, isOpen, onToggle } = props;

  // Use internal state only if isOpen is not provided
  const [internalIsOpen, setInternalIsOpen] = useState(false);

  // Determine if the accordion is open based on props or internal state
  const accordionIsOpen = isOpen !== undefined ? isOpen : internalIsOpen;

  // Handle click on the accordion header
  const handleToggle = () => {
    const newIsOpen = !accordionIsOpen;

    // If we're controlled externally, call the onToggle callback
    if (id && onToggle) {
      onToggle(id, newIsOpen);
    } else {
      // Otherwise, use internal state
      setInternalIsOpen(newIsOpen);
    }
  };

  return (
    <div>
      <div
        className={`flex items-center cursor-pointer outline-0 ${className}`}
        onClick={handleToggle}
      >
        <p className="text-sm flex-1">{title}</p>
        <div
          className={classNames({
            'rotate-180': accordionIsOpen,
          })}
        >
          <DownArrow width="24" height="24" />
        </div>
      </div>
      {accordionIsOpen ? children : null}
    </div>
  );
};

export default Accordion;
