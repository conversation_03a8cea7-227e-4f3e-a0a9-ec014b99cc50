import React from 'react';

interface IButtonV2Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'outline';
  icon?: React.ReactNode;
}

const ButtonV2 = (props: IButtonV2Props) => {
  const {
    children,
    className = '',
    variant = 'primary',
    icon,
    ...remainingProps
  } = props;
  return (
    <button
      className={`font-poppins flex items-center text-lg md:text-xl lg:text-2xl px-8 py-2 sm:py-3 rounded-lg transition-all duration-150 ${
        remainingProps.disabled
          ? 'opacity-50 cursor-not-allowed'
          : 'active:bg-opacity-90 active:shadow-[inset_0_0_0_2px_#286053]'
      } ${
        variant === 'primary'
          ? 'bg-[#286053] text-white active:bg-[#286053]'
          : 'shadow-[inset_0_0_0_2px_#286053] text-[#286053] bg-[#286053] bg-opacity-0 hover:bg-opacity-10 active:bg-opacity-20'
      } ${className}`}
      {...remainingProps}
    >
      {icon && (
        <div className="relative h-8 w-8 mr-4 flex items-center justify-center">
          {icon}
        </div>
      )}
      {children}
    </button>
  );
};

export default ButtonV2;
