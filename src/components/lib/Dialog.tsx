'use client';
import { createContext, useContext, useState } from 'react';
import CrossIcon from '@/images/common/cross.svg';

interface IDialogContext {
  isOpen: boolean;
  openDialog: () => void;
  closeDialog: () => void;
}

export const DialogContext = createContext<IDialogContext>(null);

export default function Dialog({ children }: { children: React.ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);

  const openDialog = () => {
    document.body.style.overflow = 'hidden';
    setIsOpen(true);
  };
  const closeDialog = () => {
    document.body.style.overflow = '';
    setIsOpen(false);
  };

  return (
    <DialogContext.Provider value={{ isOpen, openDialog, closeDialog }}>
      {children}
    </DialogContext.Provider>
  );
}

export const DialogTrigger = ({ children }: { children: React.ReactNode }) => {
  const { openDialog } = useContext(DialogContext);

  return <div onClick={openDialog}>{children}</div>;
};

export const DialogContent = ({ children }: { children: React.ReactNode }) => {
  const { isOpen, closeDialog } = useContext(DialogContext);

  if (!isOpen) return null;

  return (
    <div className=" fixed z-50 inset-0 flex items-center justify-center ">
      <div
        className=" absolute inset-0 z-0 bg-black/60 "
        onClick={closeDialog}
      />
      <div className=" relative z-10 ">{children}</div>
    </div>
  );
};

export const DialogHeader = ({ children }: { children?: React.ReactNode }) => {
  const { closeDialog } = useContext(DialogContext);
  return (
    <div className=" flex items-center pb-2 ">
      <div className=" flex-1 ">{children}</div>
      <div onClick={closeDialog}>
        <CrossIcon className=" text-[#f57141] w-5 h-5 cursor-pointer " />
      </div>
    </div>
  );
};

export const DialogClose = ({ children }: { children: React.ReactNode }) => {
  const { closeDialog } = useContext(DialogContext);

  return <div onClick={closeDialog}>{children}</div>;
};
