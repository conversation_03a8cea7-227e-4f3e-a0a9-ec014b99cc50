import Image from 'next/image';

interface INoDataFoundProps {
  illustration: {
    url: string;
    width: number;
    height: number;
  };
  title: string;
  description?: string;
  className?: string;
  imageClassName?: string;
}

const NoDataFound = (props: INoDataFoundProps) => {
  const { illustration, title, className = '', imageClassName = '' } = props;

  return (
    <div className={`${className}`}>
      <div className="flex flex-row justify-center">
        <Image
          src={illustration.url}
          width={illustration.width}
          height={illustration.height}
          alt=""
          className={imageClassName}
        />
      </div>
      <p className="text-center mt-5">{title}</p>
    </div>
  );
};

export default NoDataFound;
