import { ReactNode } from 'react';
import DownArrow from '@/images/common/down-arrow.svg';

interface IDropdownProps {
  children: ReactNode;
  value: string;
  onChange: (value: string) => void;
  className?: string;
  dropdownClassName?: string;
  disabled?: boolean;
}

const Dropdown = (props: IDropdownProps) => {
  const {
    children,
    value,
    onChange,
    className = '',
    dropdownClassName = '',
    disabled = false,
  } = props;

  return (
    <div
      className={`mt-3 border border-gray-300 rounded-lg p-0.5 w-full flex items-center ${className}`}
    >
      <div className="flex-1 relative">
        <select
          className={`outline-none appearance-none px-4 pr-8 w-full cursor-pointer ${dropdownClassName}`}
          onChange={(event) => onChange(event.target?.value)}
          value={value}
          aria-label="Dropdown selection"
          disabled={disabled}
        >
          {children}
        </select>
        <DownArrow
          width="24"
          height="24"
          className="text-gray-dark absolute top-1/2 -translate-y-1/2 right-0.5 pointer-events-none select-none"
        />
      </div>
      <div></div>
    </div>
  );
};

export default Dropdown;
