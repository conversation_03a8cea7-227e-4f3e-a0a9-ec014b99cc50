import classNames from 'classnames';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';

interface NavLink {
  label: string;
  href: string;
}

interface NavigationHeaderProps {
  navlinks?: NavLink[];
}

const DEFAULT_NAVLINKS = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'About Us',
    href: '/about-us',
  },
  {
    label: 'Login',
    href: '/login',
  },
];

const NavigationHeader = ({
  navlinks = DEFAULT_NAVLINKS,
}: NavigationHeaderProps) => {
  const router = useRouter();
  const pathname = router.pathname;

  return (
    <div className="flex items-center px-4 py-6 sm:px-36 bg-off-white">
      <div className="flex">
        <Link className="w-28 sm:w-36" href="/">
          <Image
            src="/images/common/logo-black.png"
            alt="Groweasy logo"
            width="651"
            height="115"
          />
        </Link>
      </div>
      <div className="flex-1" />
      <div className="flex items-center">
        {navlinks.map((item, index) => {
          return (
            <div className="mr-3 sm:mr-12 text-sm sm:text-base" key={index}>
              <Link
                href={item.href}
                className={classNames('hover:font-medium', {
                  'font-medium': item.href === pathname,
                })}
              >
                {item.label}
              </Link>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default NavigationHeader;
