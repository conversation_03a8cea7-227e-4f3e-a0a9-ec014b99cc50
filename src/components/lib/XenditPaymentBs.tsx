import BottomSheet from '@/components/lib/BottomSheet';
import { QueryParams } from '@/constants/index';
import CrossIcon from '@/images/common/cross.svg';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useEffect, useRef } from 'react';
import { useMutation } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import { createXenditInvoice } from 'src/actions/payment';
import { GrowEasyPartners, IGroweasyUser, IPartnerConfig } from 'src/types';
import { Currency } from 'src/types/campaigns';
import { IOrderDetails } from 'src/types/payments_invoices';
import {
  formatCurrencyAmount,
  logApiErrorAndShowToastMessage,
  openUrlInNewTab,
} from 'src/utils';

interface IXenditPaymentBsProps {
  onClose: () => void;
  user: IGroweasyUser;
  orderDetails: IOrderDetails;
  partnerConfig?: IPartnerConfig;
}

// https://developers.xendit.co/api-reference/#create-invoice
const XenditPaymentBs = (props: IXenditPaymentBsProps) => {
  const { onClose, user, orderDetails, partnerConfig } = props;

  const router = useRouter();

  const invoiceUrlRef = useRef('');

  const createXenditInvoiceMutation = useMutation(createXenditInvoice);

  const onInvoiceFetchSuccess = () => {
    if (invoiceUrlRef.current) {
      const newTabOpenSuccess = openUrlInNewTab(invoiceUrlRef.current);
      if (newTabOpenSuccess) {
        onClose();
        void router.push({
          pathname: '/payment',
          query: {
            [QueryParams.CAMPAIGN_ID]: orderDetails?.campaign_id,
            [QueryParams.ORDER_ID]: orderDetails?.razorpay_order_id,
          },
        });
      }
    }
  };

  useEffect(() => {
    void createXenditInvoiceMutation.mutateAsync(
      {
        headers: getCommonHeaders(user),
        queryParams: {},
        data: {
          razorpay_order_id: orderDetails.razorpay_order_id,
        },
      },
      {
        onError: (error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'XenditPaymentBs.createXenditInvoiceMutation',
          );
        },
        onSuccess: (response) => {
          invoiceUrlRef.current = response?.data?.invoice_url;
          onInvoiceFetchSuccess();
        },
      },
    );
  }, []);

  return (
    <BottomSheet
      className="h-auto"
      contentClassName="h-full flex flex-col flex-1"
      onClose={onClose}
      loading={createXenditInvoiceMutation.isLoading}
    >
      <div className="flex flex-col flex-1 overflow-hidden">
        <div className="flex items-center">
          <div className="flex-1" />
          <CrossIcon
            width="14"
            height="14"
            className="text-gray-dark cursor-pointer"
            onClick={onClose}
          />
        </div>
        <div className="flex-1 flex-col overflow-y-scroll no-scrollbar">
          <div className="mt-5">
            <div className="flex justify-center">
              {partnerConfig?.partner === GrowEasyPartners.AD_GLOBAL_AI ? (
                <div className="w-40">
                  <Image
                    src={partnerConfig?.logoImage}
                    alt={partnerConfig?.name}
                    width="976"
                    height="156"
                  />
                </div>
              ) : (
                <div>
                  <Image
                    src="/images/groweasy-logo-square.png"
                    width="40"
                    height="40"
                    alt=""
                  />
                </div>
              )}
            </div>
            <div className="flex flex-col items-center justify-center mt-3">
              <p className="text-xs">{partnerConfig?.name ?? 'GrowEasy'}</p>
              <p className="tex-sm font-medium mt-2">
                {formatCurrencyAmount(
                  orderDetails.amount /
                    ([Currency.IDR, Currency.VND].includes(
                      orderDetails.currency,
                    )
                      ? 1
                      : 100),
                  orderDetails.currency,
                )}
              </p>
              <p
                className="text-hyperlink mt-5 cursor-pointer"
                onClick={onInvoiceFetchSuccess}
              >
                Complete Payment
              </p>
            </div>
          </div>
        </div>
      </div>
    </BottomSheet>
  );
};

export default XenditPaymentBs;
