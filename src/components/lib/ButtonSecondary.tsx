import classNames from 'classnames';
import { ButtonHTMLAttributes, ReactNode } from 'react';

interface IButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  className?: string;
}

const Button = (props: IButtonProps) => {
  const { children, className = '', ...remainingProps } = props;

  return (
    <button
      className={classNames(
        `bg-white text-primary hover:bg-gray-200 transition-all duration-300 ring-primary text-base font-medium py-3 px-6 rounded-lg ring-1 ${className}`,
        {
          'opacity-50': !!remainingProps.disabled,
        },
      )}
      {...remainingProps}
    >
      {children}
    </button>
  );
};

export default Button;
