import { IMAGE_FILE_SIZE_LIMIT_IN_KB } from '@/constants/index';
import React, { ChangeEventHandler } from 'react';
import { showToastMessage } from 'src/modules/toast';

interface IDragAndDropProps {
  setIsDragging: React.Dispatch<React.SetStateAction<boolean>>;
  handleUpload: ChangeEventHandler<HTMLInputElement>;
  children: React.ReactNode;
  className?: string;
  acceptedTypes: Array<'image/*' | 'video/*'>;
  maxFileSizeInKB?: number;
  multipleAccept?: boolean;
}

const DragAndDrop = (props: IDragAndDropProps) => {
  const {
    setIsDragging,
    handleUpload,
    children,
    className,
    acceptedTypes,
    maxFileSizeInKB = IMAGE_FILE_SIZE_LIMIT_IN_KB,
    multipleAccept = false,
  } = props;

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFiles = Array.from(e.dataTransfer.files).slice(
      0,
      multipleAccept ? undefined : 1,
    );

    const filteredFiles = droppedFiles.filter((file) =>
      acceptedTypes.some(
        (type) =>
          type === file.type || // Exact match like "image/png"
          (type.endsWith('/*') && file.type.startsWith(type.split('/')[0])), // wildcard like "image/*"
      ),
    );

    if (filteredFiles.length > 0) {
      const fileList = new DataTransfer();
      Array.from(filteredFiles).forEach((file) => {
        const fileSizeInKb = file.size / 1024;
        if (fileSizeInKb > maxFileSizeInKB) {
          showToastMessage(
            `File "${file.name}" exceeds the maximum size of ${
              maxFileSizeInKB / 1024
            } MB.`,
            'error',
          );
        } else {
          fileList.items.add(file);
        }
      });

      const inputEvent = {
        target: { files: fileList.files },
      } as React.ChangeEvent<HTMLInputElement>;

      handleUpload(inputEvent);
    } else if (droppedFiles.length > 0) {
      showToastMessage(
        'Invalid file type! Please upload a valid file.',
        'error',
      );
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  return (
    <div
      onDrop={handleDrop}
      className={className + ' w-full '}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      {children}
    </div>
  );
};

export default DragAndDrop;
