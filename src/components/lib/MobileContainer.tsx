import { ReactNode } from 'react';

interface IMobileContainerProps {
  children?: ReactNode;
  className?: string;
  innerClassName?: string;
}

const MobileContainer = (props: IMobileContainerProps) => {
  const { children, className = '', innerClassName = '' } = props;

  return (
    <div
      className={`h-dvh min-h-svh w-full flex flex-col items-center bg-black sm:py-6 ${className}`}
      id="mobile-container"
    >
      <div
        className={`bg-off-white w-full h-full flex flex-col flex-1 overflow-y-scroll sm:rounded-2xl ${innerClassName}`}
        id="mobile-container-content"
      >
        {children}
      </div>
    </div>
  );
};

export default MobileContainer;
