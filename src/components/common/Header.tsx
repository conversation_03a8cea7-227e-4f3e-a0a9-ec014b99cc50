import React from 'react';
import GrowEasyLogo from '@/images/logos/groweasy-with-name.svg';
import Link from 'next/link';
import BodyV2 from '../lib/typography/BodyV2';

const Header = () => {
  return (
    <div className=" fixed top-0 left-0 w-full  z-50 bg-off-white ">
      <nav className=" max-w-[1600px] mx-auto flex h-[70px] md:h-[80px] justify-between items-center px-4 md:px-8 ">
        <div className=" h-6 xs:h-7 sm:h-8 md:h-10 flex overflow-hidden ">
          <GrowEasyLogo />
        </div>

        <div className=" flex ">
          <Link href={'/'}>
            <BodyV2
              variant="sm"
              className=" flex items-center justify-center w-12 md:w-16 hover:font-semibold "
            >
              Home
            </BodyV2>
          </Link>
          <Link href={'/about-us'}>
            <BodyV2
              variant="sm"
              className=" flex items-center justify-center w-24 md:w-36 hover:font-semibold"
            >
              About Us
            </BodyV2>
          </Link>
          <Link href={'/login'}>
            <BodyV2
              variant="sm"
              className=" flex items-center justify-center w-12 md:w-16 hover:font-semibold"
            >
              Login
            </BodyV2>
          </Link>
        </div>
      </nav>
    </div>
  );
};

export default Header;
