import { IGroweasyUser } from 'src/types';
import { Currency, ICampaign } from 'src/types/campaigns';
import {
  formatCurrencyAmount,
  getFormattedDateString,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import Accordion from '../lib/Accordion';
import { useQuery } from 'react-query';
import { getOrders } from 'src/actions/profile';
import { getCommonHeaders } from 'src/actions';
import { QueryParams } from '@/constants/index';
import SpinnerLoader from '../lib/SpinnerLoader';
import classNames from 'classnames';
import { useRouter } from 'next/router';

interface ICampaignInvoicesAccordionProps {
  campaignDetails?: ICampaign;
  className?: string;
  user: IGroweasyUser;
}

const CampaignInvoicesAccordion = (props: ICampaignInvoicesAccordionProps) => {
  const { campaignDetails, className = '', user } = props;

  const router = useRouter();

  const ordersResponse = useQuery(
    ['getOrders', campaignDetails?.id],
    () => {
      return getOrders({
        headers: getCommonHeaders(user),
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails?.id,
        },
      });
    },
    {
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignInvoicesAccordion.getOrders',
        );
      },
    },
  );

  const onInvoiceClick = (id: string) => {
    void router.push({
      pathname: '/invoice-details',
      query: {
        [QueryParams.INVOICE_ID]: id,
      },
    });
  };

  const orders = ordersResponse?.data?.data ?? null;

  return (
    <div className={`px-4 pt-2 pb-3 bg-white rounded-lg shadow ${className}`}>
      <Accordion title="Campaign Invoices">
        {ordersResponse.isLoading ? (
          <div className="flex justify-center">
            <SpinnerLoader size={24} borderWidth={3} />
          </div>
        ) : (
          <div className="mt-2">
            {orders?.map((item, index) => {
              const creationDate = new Date(item.created_at._seconds * 1000);
              return (
                <div
                  key={index}
                  className="border-b border-gray-light py-3 cursor-pointer"
                  onClick={() => onInvoiceClick(item.invoice_id)}
                >
                  <div className="flex items-center">
                    <p className="text-sm w-28">
                      {getFormattedDateString(creationDate)}
                    </p>
                    <p
                      className={classNames(
                        'text-xs  py-1 px-2 rounded-lg bg-green-light',
                      )}
                    >
                      {item.type?.toUpperCase()}
                    </p>
                    <div className="flex-1" />
                    <p className="text-sm font-medium">
                      {formatCurrencyAmount(
                        item.amount /
                          ([Currency.IDR, Currency.VND].includes(item.currency)
                            ? 1
                            : 100),
                        item.currency,
                      )}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </Accordion>
    </div>
  );
};

export default CampaignInvoicesAccordion;
