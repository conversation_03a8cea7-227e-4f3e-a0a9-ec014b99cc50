import { ICtwaLead, IGoogleLead, IMetaLead } from 'src/types/leads';
import AreaChartComp from './charts/AreaChartComp';
import { IGroweasyUser } from 'src/types';
import {
  Currency,
  ICampaign,
  ICampaignInsightDetails,
} from 'src/types/campaigns';
import Carousel from '../onboarding/ad_videos/Carousel';
import { useRef, useState } from 'react';
import { useQuery } from 'react-query';
import { getCampaignInsights } from 'src/actions/dashboard';
import { QueryParams } from '@/constants/index';
import { getCommonHeaders } from 'src/actions';
import {
  formatCurrencyAmount,
  getCampaignCurrencyBudgetNode,
  getMetaLeadsCountFromInsightDetails,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import SpinnerLoader from '../lib/SpinnerLoader';
import BarChartComp from './charts/BarChartComp';
import {
  NameType,
  ValueType,
} from 'recharts/types/component/DefaultTooltipContent';
import { TooltipProps } from 'recharts';

interface ILeadsChartCompProps {
  campaignLeads?: IMetaLead[] | ICtwaLead[] | IGoogleLead[];
  className?: string;
  user: IGroweasyUser;
  campaignDetails: ICampaign;
}

const CustomTooltip = ({
  active,
  payload,
  label,
}: TooltipProps<ValueType, NameType>) => {
  if (!active || !payload || !payload.length) return null;
  const data = payload[0].payload as {
    spent: number;
    currency: Currency;
    cpl: number;
    leads: number;
  };

  return (
    <div className="bg-white border rounded p-2 shadow text-xs">
      <p className="text-base">{label}</p>
      <div className=" mt-3 space-y-1.5 ">
        <p style={{ color: payload[0].color }}>{`Leads: ${data.leads}`}</p>
        <p
          style={{ color: payload[0].color }}
        >{`Amount Spent: ${formatCurrencyAmount(
          data?.spent,
          data?.currency,
        )}`}</p>
        <p style={{ color: payload[0].color }}>{`CPL: ${formatCurrencyAmount(
          data?.cpl,
          data?.currency,
        )}`}</p>
      </div>
    </div>
  );
};

const LeadsChartComp = (props: ILeadsChartCompProps) => {
  const { campaignDetails, user, className = ' ' } = props;
  const [currentChartIndex, setCurrentChartIndex] = useState(0);
  const chartRefs = useRef<Array<HTMLDivElement | null>>([]);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const today = new Date();
  const pastDate = new Date();
  pastDate.setDate(today.getDate() - 90); // last 3 months

  // "YYYY-MM-DD"
  const timeRangeParam = JSON.stringify({
    since: pastDate.toISOString().split('T')[0],
    until: today.toISOString().split('T')[0],
  });

  const dayWiseInsightsResponse = useQuery(
    ['dayWiseInsightsResponse', campaignDetails.id],
    () =>
      getCampaignInsights({
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails?.meta_id,
          [QueryParams.TIME_INCREMENT]: '1',
          [QueryParams.TIME_RANGE]: timeRangeParam,
          [QueryParams.LIMIT]: '250',
        },
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignDetails?.meta_id,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'LeadsChartComp.dayWiseInsightsResponse',
        );
      },
    },
  );

  if (dayWiseInsightsResponse.isLoading) {
    return (
      <div className="flex flex-col items-center justify-center flex-1">
        <SpinnerLoader />
      </div>
    );
  }
  const dayWiseInsights: ICampaignInsightDetails[] =
    dayWiseInsightsResponse?.data?.data ?? [];
  const budgetAndScheduling = campaignDetails?.details?.budget_and_scheduling;
  const campaignCurrencyBudgetNode =
    getCampaignCurrencyBudgetNode(budgetAndScheduling);

  const dayWiseConversionsArr = dayWiseInsights.map((item) => {
    let spendInInr = parseFloat(item.spend ?? '0');
    spendInInr =
      spendInInr * (100 / (100 - budgetAndScheduling?.platform_fee_percentage));
    const spendInCampaignCurrency =
      spendInInr / campaignCurrencyBudgetNode.exchange_rate;
    const dateStart = new Date(item.date_start);
    const day = dateStart.getDate();
    const month = dateStart.toLocaleString('en-US', { month: 'short' });
    const leads = getMetaLeadsCountFromInsightDetails(
      item,
      campaignDetails.type,
    );
    return {
      date: `${day} ${month}`,
      leads: leads,
      spent: Math.round(spendInCampaignCurrency),
      currency: budgetAndScheduling?.currency,
      cpl: Number(leads ? (spendInCampaignCurrency / leads).toFixed(2) : 0),
    };
  });

  const handleIndicatorClick = (index: number) => {
    const targetElement = chartRefs.current[index];
    const scrollContainer = scrollContainerRef.current; // Get the actual scroll container

    if (targetElement && scrollContainer) {
      // Calculate the target scrollLeft position
      // This centers the target element in the view
      const targetScrollLeft =
        targetElement.offsetLeft -
        scrollContainer.clientWidth / 2 +
        targetElement.clientWidth / 2 -
        scrollContainer.offsetLeft;

      // Scroll the container only along the X-axis
      scrollContainer.scrollTo({
        left: targetScrollLeft,
        behavior: 'smooth',
      });

      // If you want to update the currentChartIndex here as well
      // setCurrentIndex(index);
    }
  };

  return (
    <div className={className}>
      <Carousel
        currentIndex={currentChartIndex}
        setCurrentIndex={setCurrentChartIndex}
        divRef={scrollContainerRef}
      >
        <div
          ref={(el) => {
            chartRefs.current[0] = el;
          }}
        >
          <p className="text-base font-medium text-primary mt-6">Leads</p>
          <AreaChartComp
            data={dayWiseConversionsArr}
            xAxisDataKey="date"
            yAxisDataKey="leads"
            CustomToolTip={CustomTooltip}
          />
        </div>
        <div
          ref={(el) => {
            chartRefs.current[1] = el;
          }}
        >
          <p className="text-base font-medium text-primary mt-6">
            Amount Spent
          </p>
          <BarChartComp
            data={dayWiseConversionsArr}
            xAxisDataKey="date"
            yAxisDataKey="spent"
            CustomToolTip={CustomTooltip}
          />
        </div>
        <div
          ref={(el) => {
            chartRefs.current[2] = el;
          }}
        >
          <p className="text-base font-medium text-primary mt-6">
            Amount Spent per Lead
          </p>
          <AreaChartComp
            data={dayWiseConversionsArr}
            xAxisDataKey="date"
            yAxisDataKey="cpl"
            CustomToolTip={CustomTooltip}
          />
        </div>
      </Carousel>

      <div className=" flex items-center justify-center gap-1 mt-4 ">
        {Array.from({ length: 3 }).map((item, index) => (
          <div
            className={` w-8 h-1.5 rounded ${
              currentChartIndex === index ? 'bg-primary' : 'bg-gray-400'
            } transition-all duration-150 cursor-pointer `}
            onClick={() => handleIndicatorClick(index)}
            key={index}
          />
        ))}
      </div>
    </div>
  );
};

export default LeadsChartComp;
