import {
  GROWEASY_CAMPAIGN_TYPE,
  IBudgetAndScheduling,
  ICampaignInsightDetails,
} from 'src/types/campaigns';
import Tooltip from '../lib/Tooltip';
import { getParsedCampaignInsights } from 'src/utils';

interface ICampaignInsightsComp {
  className?: string;
  data: ICampaignInsightDetails;
  budgetAndScheduling: IBudgetAndScheduling;
  campaignType: GROWEASY_CAMPAIGN_TYPE;
}

const CampaignInsightsComp = (props: ICampaignInsightsComp) => {
  const { className = '', data, budgetAndScheduling, campaignType } = props;

  const details = getParsedCampaignInsights({
    budgetAndScheduling,
    insights: data,
    campaignType,
  });
  return (
    <div
      className={`bg-white rounded-lg shadow flex items-start flex-wrap ${className}`}
    >
      {details.map((item, index) => {
        return (
          <div key={index} className="shrink-0 p-4 w-1/3">
            <div className="flex items-center">
              <p className="text-sm text-gray-dark mr-1">{item.label}</p>
              {item.description ? <Tooltip title={item.description} /> : null}
            </div>
            <p className="text-lg font-medium">{item.value}</p>
          </div>
        );
      })}
    </div>
  );
};

export default CampaignInsightsComp;
