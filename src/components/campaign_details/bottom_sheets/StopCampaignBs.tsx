import BottomSheet from '@/components/lib/BottomSheet';
import Button from '@/components/lib/Button';
import CrossIcon from '@/images/common/cross.svg';

interface IConfirmStopCampaignBsProps {
  onClose: () => void;
  onStopCampaignClick: () => void;
  loading: boolean;
}

const ConfirmStopCampaignBs = (props: IConfirmStopCampaignBsProps) => {
  const { onClose, onStopCampaignClick, loading } = props;

  return (
    <BottomSheet
      className="h-auto"
      contentClassName="h-full flex flex-col flex-1"
      onClose={onClose}
      loading={loading}
    >
      <div className="flex flex-col flex-1 overflow-hidden">
        <div className="flex items-center">
          <p className="text-base text-gray-dark flex-1">Stop Campaign?</p>
          <CrossIcon
            width="14"
            height="14"
            className="text-gray-dark cursor-pointer"
            onClick={onClose}
          />
        </div>
        <div className="flex-1 flex-col overflow-y-scroll no-scrollbar">
          <p className="text-xs mt-5">
            This campaign will be stopped, and the remaining ad spend will be
            credited to your wallet. This process cannot be undone.
          </p>
        </div>
        <Button
          className="mt-5"
          onClick={onStopCampaignClick}
          disabled={loading}
        >
          <p>Confirm</p>
        </Button>
      </div>
    </BottomSheet>
  );
};

export default ConfirmStopCampaignBs;
