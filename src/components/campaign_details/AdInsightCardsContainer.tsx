import { QueryParams } from '@/constants/index';
import { useRouter } from 'next/router';
import { IGroweasyUser } from 'src/types';
import { ICampaign } from 'src/types/campaigns';
import DownArrow from '@/images/common/down-arrow.svg';
import { useQuery } from 'react-query';
import { getAdInsightCards } from 'src/actions/reports';
import { getCommonHeaders } from 'src/actions';
import { logApiErrorAndShowToastMessage } from 'src/utils';

interface IAdInsightCardsContainerProps {
  campaignDetails: ICampaign;
  user: IGroweasyUser;
}

const AdInsightCardsContainer = (props: IAdInsightCardsContainerProps) => {
  const { user, campaignDetails } = props;

  const router = useRouter();

  const adInsightCardsResponse = useQuery(
    ['getAdInsightCards', campaignDetails?.order_id],
    () =>
      getAdInsightCards({
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
        },
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignDetails?.id,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'AdInsightCardsContainer.adInsightCardsResponse',
        );
      },
    },
  );

  const onAnalyticsBreakdownClick = () => {
    void router.push({
      pathname: '/campaign-analytics',
      query: {
        [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
      },
    });
  };

  const insightCards = adInsightCardsResponse?.data?.data?.insights_cards ?? [];

  return (
    <div>
      {insightCards.length ? (
        <div className="mt-6 mb-3">
          <p className="text-sm">Ad Insights</p>
          <div className="flex overflow-x-scroll gap-x-2 mt-3 no-scrollbar">
            {insightCards.map((item, index) => {
              return (
                <div
                  className="px-4 py-2 bg-white rounded-lg w-60 shrink-0"
                  key={index}
                >
                  <p className="text-xs text-primary font-semibold">
                    {item.title}
                  </p>
                  <p className="text-xxs mt-2">{item.description}</p>
                </div>
              );
            })}
          </div>
          <div
            className="flex mt-3 cursor-pointer"
            onClick={onAnalyticsBreakdownClick}
          >
            <div className="flex-1" />
            <p className="text-xs text-primary">
              View Detailed Analytics <span className="ml-2">⮕</span>
            </p>
          </div>
        </div>
      ) : (
        <div
          className="mt-4 cursor-pointer px-4 py-2 bg-white rounded-lg shadow"
          onClick={onAnalyticsBreakdownClick}
        >
          <div>
            <div className={`flex items-center outline-0`}>
              <p className="text-sm flex-1">Analytics Breakdown</p>
              <div className="-rotate-90">
                <DownArrow width="24" height="24" />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdInsightCardsContainer;
