import { ICampaign } from 'src/types/campaigns';
import Accordion from '@/components/lib/Accordion';
import { SALES_CAMPAIGN_FOR_CLIENTS_DATASET_ID } from 'src/constants';
import { copyText, openUrlInNewTab } from 'src/utils';
import Image from 'next/image';
import { useMemo, useState } from 'react';
import Dropdown from '../lib/Dropdown';
import { getPixelPlatformsData } from 'src/utils/pixel';

interface IPixelSetupAccordionProps {
  campaignDetails?: ICampaign;
}

const PixelSetupAccordion = (props: IPixelSetupAccordionProps) => {
  const { campaignDetails } = props;
  const [selectedPlatform, setSelectedPlatform] =
    useState<keyof typeof pixelPlatforms>('shopify');

  const pixelPlatforms = useMemo(() => {
    const website = campaignDetails.details.business_details?.website;
    const pixelId = SALES_CAMPAIGN_FOR_CLIENTS_DATASET_ID;
    const customEventName =
      campaignDetails?.details?.config?.meta_sales_purchase_event_name ||
      'Purchase';

    return getPixelPlatformsData({ website, pixelId, customEventName });
  }, [campaignDetails]);

  return (
    <div className="mt-4 px-4 py-2 bg-white rounded-lg shadow">
      <Accordion title="Custom Pixel Setup">
        <Dropdown
          className=" h-9 !px-0 !rounded-md !text-md !capitalize "
          value={selectedPlatform}
          onChange={(value) =>
            setSelectedPlatform(value as keyof typeof pixelPlatforms)
          }
        >
          {Object.keys(pixelPlatforms).map((item) => (
            <option key={item} value={item} className=" text-sm ">
              {item.charAt(0).toUpperCase() + item.slice(1)}
            </option>
          ))}
        </Dropdown>

        <p className=" text-xxs !font-medium mt-4 !text-gray-dark">
          {pixelPlatforms[selectedPlatform].description}

          <br />
          <span
            onClick={() =>
              openUrlInNewTab(pixelPlatforms[selectedPlatform].guideLink)
            }
            className="text-blue-600 underline cursor-pointer"
          >
            {selectedPlatform} Integration Guide
          </span>
        </p>

        {pixelPlatforms[selectedPlatform].sections.map(
          ({ title, code }, index) => (
            <div key={index} className="bg-gray-100 p-3 mt-4 rounded">
              <div className="flex items-center mb-2">
                <p className="text-xs font-medium text-gray-dark">{title}</p>
                <div
                  className="ml-1 cursor-pointer"
                  title="Copy this response"
                  onClick={() => copyText(code)}
                >
                  <Image
                    src="/images/common/copy.svg"
                    height="16"
                    width="16"
                    alt="Copy"
                  />
                </div>
              </div>
              <pre className="text-xs bg-gray-800 text-white p-2 rounded overflow-x-scroll no-scrollbar">
                {code}
              </pre>
            </div>
          ),
        )}
      </Accordion>
    </div>
  );
};

export default PixelSetupAccordion;
