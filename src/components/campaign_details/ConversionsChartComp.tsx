import { QueryParams } from '@/constants/index';
import { useQuery } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import { getCampaignInsights } from 'src/actions/dashboard';
import { IGroweasyUser } from 'src/types';
import {
  Currency,
  ICampaign,
  ICampaignInsightDetails,
} from 'src/types/campaigns';
import {
  formatCurrencyAmount,
  getCampaignCurrencyBudgetNode,
  getMetaLeadsCountFromInsightDetails,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import SpinnerLoader from '../lib/SpinnerLoader';
import AreaChartComp from './charts/AreaChartComp';
import {
  NameType,
  Payload,
  ValueType,
} from 'recharts/types/component/DefaultTooltipContent';
import BarChartComp from './charts/BarChartComp';

interface IConversionsChartCompProps {
  campaignDetails: ICampaign;
  user?: IGroweasyUser;
}

const ConversionsChartComp = (props: IConversionsChartCompProps) => {
  const { campaignDetails, user } = props;

  const today = new Date();
  const pastDate = new Date();
  pastDate.setDate(today.getDate() - 90); // last 3 months

  // "YYYY-MM-DD"
  const timeRangeParam = JSON.stringify({
    since: pastDate.toISOString().split('T')[0],
    until: today.toISOString().split('T')[0],
  });

  const dayWiseInsightsResponse = useQuery(
    ['dayWiseInsightsResponse', campaignDetails.id],
    () =>
      getCampaignInsights({
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails?.meta_id,
          [QueryParams.TIME_INCREMENT]: '1',
          [QueryParams.TIME_RANGE]: timeRangeParam,
          [QueryParams.LIMIT]: '250',
        },
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignDetails?.meta_id,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'ConversionsChartComp.dayWiseInsightsResponse',
        );
      },
    },
  );

  if (dayWiseInsightsResponse.isLoading) {
    return (
      <div className="flex flex-col items-center justify-center flex-1">
        <SpinnerLoader />
      </div>
    );
  }
  const dayWiseInsights: ICampaignInsightDetails[] =
    dayWiseInsightsResponse?.data?.data ?? [];
  const budgetAndScheduling = campaignDetails?.details?.budget_and_scheduling;
  const campaignCurrencyBudgetNode =
    getCampaignCurrencyBudgetNode(budgetAndScheduling);
  const dayWiseConversionsArr = dayWiseInsights.map((item) => {
    let spendInInr = parseFloat(item.spend ?? '0');
    spendInInr =
      spendInInr * (100 / (100 - budgetAndScheduling?.platform_fee_percentage));
    const spendInCampaignCurrency =
      spendInInr / campaignCurrencyBudgetNode.exchange_rate;
    const dateStart = new Date(item.date_start);
    const day = dateStart.getDate();
    const month = dateStart.toLocaleString('en-US', { month: 'short' });
    const conversions = getMetaLeadsCountFromInsightDetails(
      item,
      campaignDetails.type,
    );
    return {
      date: `${day} ${month}`,
      conversions: conversions,
      spent: Math.round(spendInCampaignCurrency),
      currency: budgetAndScheduling?.currency,
      cpco: Number(
        conversions ? (spendInCampaignCurrency / conversions).toFixed(2) : 0,
      ),
    };
  });

  const tooltipFormatter = (
    value: ValueType,
    name: NameType,
    payload: Payload<ValueType, NameType>,
  ): [ValueType, NameType] => {
    let formattedValue = value;
    let formattedName = name;
    const data = payload.payload as {
      spent: number;
      currency: Currency;
    };

    if (name === 'cpco') {
      formattedName = 'CPCo';
      formattedValue = formatCurrencyAmount(Number(value), data?.currency);
    } else if (name === 'conversions') {
      formattedName = 'Conversions';
    } else if (name === 'spent') {
      formattedName = 'Amount Spent';
      formattedValue = formatCurrencyAmount(Number(value), data?.currency);
    }
    return [formattedValue, formattedName];
  };

  return (
    <div className="flex flex-col flex-1 h-full px-4 pb-4 overflow-y-hidden ">
      <div className=" flex-1 overflow-y-scroll no-scrollbar ">
        <p className="text-sm font-medium text-primary mt-6">Conversions</p>
        <AreaChartComp
          data={dayWiseConversionsArr}
          xAxisDataKey="date"
          yAxisDataKey="conversions"
          syncId="syncId1"
          tooltipFormatter={tooltipFormatter}
        />
        <p className="text-sm font-medium text-primary mt-6">Amount Spent</p>
        <BarChartComp
          data={dayWiseConversionsArr}
          xAxisDataKey="date"
          yAxisDataKey="spent"
          // CustomToolTip={<DayWiseSpendTooltip />}
          syncId="syncId1"
          tooltipFormatter={tooltipFormatter}
        />
        <p className="text-sm font-medium text-primary mt-6">
          Amount Spent per Conversion
        </p>
        <AreaChartComp
          data={dayWiseConversionsArr}
          xAxisDataKey="date"
          yAxisDataKey="cpco"
          syncId="syncId1"
          tooltipFormatter={tooltipFormatter}
        />
        <div className="mt-4" /> {/* Spacer for bottom breathing room */}
      </div>
    </div>
  );
};

export default ConversionsChartComp;
