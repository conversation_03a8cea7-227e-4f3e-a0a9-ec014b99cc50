import { IGroweasyUser } from 'src/types';
import { ICampaign } from 'src/types/campaigns';
import { isAdmin } from 'src/utils';
import Accordion from '../lib/Accordion';
import DetailedTargetingComp from './DetailedTargetingComp';

interface IDetailedTargetingAccordionProps {
  campaignDetails?: ICampaign;
  className?: string;
  user: IGroweasyUser;
}

const DetailedTargetingAccordion = (
  props: IDetailedTargetingAccordionProps,
) => {
  const { campaignDetails, className = '', user } = props;

  if (!isAdmin(user)) {
    return null;
  }

  return (
    <div className={`px-4 pt-2 pb-3 bg-white rounded-lg shadow ${className}`}>
      <Accordion title="Detailed Targeting">
        <DetailedTargetingComp
          flexibleSpecItem={
            campaignDetails?.details?.targeting?.flexible_spec?.[0]
          }
        />
      </Accordion>
    </div>
  );
};

export default DetailedTargetingAccordion;
