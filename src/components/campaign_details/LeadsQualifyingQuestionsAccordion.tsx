import { EVENT_NAMES } from 'src/constants/events';
import { logEvent } from 'src/modules/firebase';
import {
  GrowEasyCampaignStatus,
  IGoogleAdsData,
  ILeadgenForm,
} from 'src/types/campaigns';
import Accordion from '../lib/Accordion';
import DownArrow from '@/images/common/down-arrow.svg';
import { AdPlatforms } from 'src/types';
import { openUrlInNewTab } from 'src/utils';

interface ILeadsQualifyingQuestionsAccordionProps {
  leadgenForm?: ILeadgenForm;
  onEditCtaClick?: () => void;
  googleAdsData?: IGoogleAdsData;
  campaignStatus: GrowEasyCampaignStatus;
  adPlatform: AdPlatforms;
}

const LeadsQualifyingQuestionsAccordion = (
  props: ILeadsQualifyingQuestionsAccordionProps,
) => {
  const { leadgenForm, onEditCtaClick, googleAdsData, adPlatform } = props;

  const connectformLeadForm = googleAdsData?.lead_form_url?.includes(
    'connectform.co/lead-forms',
  );

  if (adPlatform === AdPlatforms.GOOGLE && googleAdsData?.lead_form_url) {
    return (
      <div
        className="mt-4 px-4 py-2 bg-white rounded-lg shadow cursor-pointer"
        onClick={() => openUrlInNewTab(googleAdsData?.lead_form_url)}
      >
        <div>
          <div className={`flex items-center outline-0`}>
            <p className="text-sm flex-1">
              {connectformLeadForm
                ? 'Landing page & form preview'
                : 'Landing page preview'}
            </p>
            <div className="-rotate-90">
              <DownArrow width="24" height="24" />
            </div>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="mt-4 px-4 py-2 bg-white rounded-lg shadow">
      <Accordion title="Lead Qualifying Questions">
        <p className="text-xs !font-medium mt-2">
          Apart from name, email, and contact information, we will also be
          asking the following questions to Ad respondents
        </p>
        {leadgenForm?.questions
          ?.filter((item) => item.type === 'CUSTOM')
          .map((item, index) => {
            return (
              <div key={index} className="mt-2 last:mb-2">
                <p className="text-xs text-gray-dark">
                  {index + 1}. {item.label}
                </p>
              </div>
            );
          })}
        {onEditCtaClick ? (
          <p
            className="text-xs text-hyperlink text-center cursor-pointer font-medium mt-5"
            onClick={() => {
              logEvent(EVENT_NAMES.edit_leads_qualifying_questions_clicked);
              onEditCtaClick();
            }}
          >
            Edit Questions
          </p>
        ) : null}
      </Accordion>
    </div>
  );
};

export default LeadsQualifyingQuestionsAccordion;
