import classNames from 'classnames';
import { ImEarth } from 'react-icons/im';
import { IoCall } from 'react-icons/io5';
import {
  GROWEASY_CAMPAIGN_TYPE,
  ICampaign,
  IGoogleAdsData,
  IGoogleMediaAssetDetails,
} from 'src/types/campaigns';

interface IGoogleAdPreviewsCompProps {
  googleAdsData: IGoogleAdsData;
  mediaAssetsDetails?: IGoogleMediaAssetDetails[];
  type: GROWEASY_CAMPAIGN_TYPE;
  campaignDetails: ICampaign;
}

const GoogleSearchAdPreviewsComp = ({
  googleAdsData,
}: {
  googleAdsData: IGoogleAdsData;
}) => {
  return (
    <>
      {googleAdsData?.ad_copies?.slice(0, 5)?.map((item, index) => (
        <div key={index} className="p-3 my-1 bg-white rounded-lg shadow">
          <div className="w-64">
            <p className="text-sm text-hyperlink">
              {[item.headline, googleAdsData?.ad_copies?.[index + 1]?.headline]
                .filter((item) => !!item)
                .join(' | ')}
            </p>
            <div className="flex items-center mt-2">
              <div className="text-xxs mr-2 px-1 rounded border border-green-700 text-green-700">
                Ad
              </div>
              <p className="line-clamp-1 text-xs text-green-700">
                {googleAdsData?.lead_form_url}
              </p>
            </div>
            <hr className="my-2" />
            <p className="text-sm mt-1 text-gray-dark">{item.description}</p>
          </div>
        </div>
      ))}
    </>
  );
};

const PMaxAdPreviewsComp = ({
  googleAdsData,
  mediaAssetsDetails,
}: {
  googleAdsData: IGoogleAdsData;
  mediaAssetsDetails: IGoogleMediaAssetDetails[];
}) => {
  return (
    <>
      {googleAdsData?.ad_copies?.slice(0, 5)?.map((item, index) => {
        const imageUrl = mediaAssetsDetails?.filter(
          (item) => item.asset.type === 'IMAGE',
        )?.[index]?.asset?.imageAsset?.fullSize?.url;

        return (
          <div key={index} className="p-3 my-1 bg-white rounded-lg shadow">
            <div className="w-64 bg-gray-light">
              {imageUrl ? (
                <img src={imageUrl} alt="" className="w-full" />
              ) : null}
            </div>
            <div className="mt-2">
              <p className="font-semibold text-sm">{item.headline}</p>
              <p className="text-sm mt-1 text-gray-dark">{item.description}</p>
              <p className="mt-2 text-sm">
                <span className="mr-1 font-bold">Ad •</span>
                <span className="text-gray-dark">
                  {googleAdsData?.text_assets?.BUSINESS_NAME?.[0]?.text}
                </span>
              </p>
            </div>
          </div>
        );
      })}
    </>
  );
};

const GoogleCallAdPreviewsComp = ({
  googleAdsData,
  campaignDetails,
}: {
  googleAdsData: IGoogleAdsData;
  campaignDetails: ICampaign;
}) => {
  const businessName =
    campaignDetails?.details?.business_details?.business_name;
  return (
    <>
      {googleAdsData?.ad_copies?.slice(0, 2)?.map((item, index) => (
        <div key={index} className="p-3 my-1 bg-white rounded-lg shadow">
          <div className="w-72">
            <p className="text-xs font-semibold">Sponsored</p>
            <div className="flex items-center mt-3">
              <div className="mr-3 p-[6px] rounded-full bg-blue-100">
                <ImEarth className="text-hyperlink" height={12} width={12} />
              </div>
              <div>
                <p className="font-light text-sm">{businessName}</p>
                <p className="text-xs text-gray font-light">
                  {campaignDetails?.details?.business_details?.website}
                </p>
              </div>
            </div>
            <p className="text-base text-hyperlink my-2">{item.headline}</p>
            <p className="text-xs text-gray-dark">{item.description}</p>
            <div className="mt-3 rounded-lg border border-gray-light px-3 py-2">
              <div className="flex items-center">
                <div className="mr-2">
                  <IoCall className="text-hyperlink" />
                </div>
                <div>
                  <p className="text-hyperlink text-sm">
                    Call{' '}
                    {
                      campaignDetails?.details?.business_details
                        ?.mobile_without_country_code
                    }
                  </p>
                  <p className="font-light text-xxs">{businessName}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

const GoogleAdPreviewsComp = (props: IGoogleAdPreviewsCompProps) => {
  const { mediaAssetsDetails, campaignDetails, googleAdsData, type } = props;

  /*const youtubeVideoId = mediaAssetsDetails?.find(
    (item) => item.asset.type === 'YOUTUBE_VIDEO',
  )?.asset?.youtubeVideoAsset?.youtubeVideoId;*/
  const videoUrl = googleAdsData?.ad_videos?.[0]?.video_url;

  return (
    <div>
      <div
        className={classNames('flex overflow-x-scroll no-scrollbar gap-x-3', {
          'items-start': type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
          'items-stretch': type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH,
        })}
      >
        {videoUrl ? (
          <div className="mt-1 shrink-0">
            {/* <iframe
              width="256"
              height="455"
              src={`https://www.youtube.com/embed/${youtubeVideoId}`}
              className="rounded-lg"
            ></iframe> */}
            <video width="280" controls>
              <source src={videoUrl} />
            </video>
          </div>
        ) : null}
        {type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH ? (
          <GoogleSearchAdPreviewsComp googleAdsData={googleAdsData} />
        ) : type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX ? (
          <PMaxAdPreviewsComp
            googleAdsData={googleAdsData}
            mediaAssetsDetails={mediaAssetsDetails}
          />
        ) : type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL ? (
          <GoogleCallAdPreviewsComp
            googleAdsData={googleAdsData}
            campaignDetails={campaignDetails}
          />
        ) : null}
      </div>
    </div>
  );
};

export default GoogleAdPreviewsComp;
