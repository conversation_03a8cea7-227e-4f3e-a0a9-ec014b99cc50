import { IGoogleCallsInsights } from 'src/types/campaigns';
import Accordion from '@/components/lib/Accordion';
import { getFormattedTimeString } from 'src/utils';

interface ICallsInsightssAccordionProps {
  callsInsightsDetails: IGoogleCallsInsights[];
}

const CallsInsightssAccordion = ({
  callsInsightsDetails,
}: ICallsInsightssAccordionProps) => {
  return (
    <div className="mt-4 px-4 py-2 bg-white rounded-lg shadow">
      <Accordion title="Call Analytics">
        <div className="overflow-x-auto mt-3 no-scrollbar">
          <table className="min-w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100 text-gray-700 text-xs">
                <th className="border border-gray-300 px-4 py-2 text-left">
                  Start Time
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  End Time
                </th>
                <th className="border border-gray-300 px-4 py-2 text-right">
                  Duration (sec)
                </th>
                <th className="border border-gray-300 px-4 py-2 text-right">
                  Status
                </th>
              </tr>
            </thead>
            <tbody>
              {callsInsightsDetails?.map((item, index) => {
                const {
                  callDurationSeconds,
                  startCallDateTime,
                  endCallDateTime,
                  callStatus,
                } = item.callView;

                return (
                  <tr key={index} className="border border-gray-300 text-xxs">
                    <td className="border border-gray-300 px-4 py-2">
                      {getFormattedTimeString(new Date(startCallDateTime))}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      {getFormattedTimeString(new Date(endCallDateTime))}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-right">
                      {callDurationSeconds}
                    </td>
                    <td className="border border-gray-300 px-4 py-2 text-right capitalize">
                      {callStatus.toLowerCase()}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </Accordion>
    </div>
  );
};

export default CallsInsightssAccordion;
