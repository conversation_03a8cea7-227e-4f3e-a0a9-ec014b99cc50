import {
  ICampaign,
  IGoogleLocationDetails,
  IGoogleMediaAssetDetails,
} from 'src/types/campaigns';
import GoogleAdPreviewsComp from './GoogleAdPreviewsComp';
import GoogleSelectLocationsComp from './GoogleSelectLocationsComp';
import { IGroweasyUser } from 'src/types';
import Button from '@/components/lib/Button';
import Image from 'next/image';
import classNames from 'classnames';
import { ChangeEventHandler, useEffect, useRef, useState } from 'react';
import { useMutation } from 'react-query';
import { uploadFileAndGetS3Url } from 'src/actions/campaign_details';
import { getCommonHeaders } from 'src/actions';
import { useRouter } from 'next/router';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import UploadIcon from '@/images/icons/upload-icon.svg';
import { showToastMessage } from 'src/modules/toast';
import FullScreenLoader from '@/components/lib/FullScreenLoader';

interface IGoogleCampaignDetailsPreLaunchProps {
  campaignDetails?: ICampaign;
  mediaAssetsDetails: IGoogleMediaAssetDetails[];
  user: IGroweasyUser;
  onLaunchCampaignClick: (data: {
    campaign_id: string;
    geo_locations: IGoogleLocationDetails[];
  }) => void;
  onGenerateAdAssetsClick: (data: {
    businessName: string;
    businessSquareLogoUrl: string;
    logoDimensions: {
      width: number;
      height: number;
    };
  }) => void;
}

const GoogleCampaignDetailsPreLaunch = (
  props: IGoogleCampaignDetailsPreLaunchProps,
) => {
  const {
    campaignDetails,
    mediaAssetsDetails,
    user,
    onLaunchCampaignClick,
    onGenerateAdAssetsClick,
  } = props;

  const [businessName, setBusinessName] = useState('');
  const [businessSquareLogoUrl, setBusinessSquareLogoUrl] = useState('');
  const [logoDimensions, setLogoDimensions] = useState({ width: 0, height: 0 });
  const [logoFile, setLogoFile] = useState<File | null>(null);

  const uploadImageInputRef = useRef<HTMLInputElement>();

  const uploadLogoImageMutation = useMutation(uploadFileAndGetS3Url);

  const googleAdsData = campaignDetails?.google_ads_data;

  const [geoLocations, setGeoLocations] = useState(
    googleAdsData?.geo_locations ?? [],
  );

  const router = useRouter();

  useEffect(() => {
    if (campaignDetails?.details?.business_details?.business_name) {
      setBusinessName(
        campaignDetails?.details?.business_details?.business_name,
      );
    }
    if (
      campaignDetails?.details?.business_details?.business_logo?.square?.url
    ) {
      setBusinessSquareLogoUrl(
        campaignDetails?.details?.business_details?.business_logo.square.url,
      );
    }
    if (campaignDetails?.google_ads_data?.geo_locations) {
      setGeoLocations(campaignDetails.google_ads_data.geo_locations);
    }
  }, [campaignDetails]);

  useEffect(() => {
    if (logoFile) {
      // upload to BE and get url
      uploadLogoImageMutation
        .mutateAsync({
          headers: getCommonHeaders(user),
          data: {
            file: logoFile,
          },
          queryParams: router.query as Record<string, string>,
        })
        .then((response) => {
          setBusinessSquareLogoUrl(response.data.s3_url);
        })
        .catch((error) => {
          logApiErrorAndShowToastMessage(
            error as Error,
            'GoogleCampaignDetailsPreLaunch.uploadLogoImageMutation',
          );
          // reset in case of error
          setLogoFile(null);
        });
    }
  }, [logoFile]);

  const onUploadImageInputChange: ChangeEventHandler<HTMLInputElement> = (
    e,
  ) => {
    const file = e.target?.files?.[0];
    if (file) {
      // Check file size (less than 1 MB)
      const isValidSize = file.size < 1 * 1024 * 1024; // 1 MB in bytes

      if (!isValidSize) {
        showToastMessage('Logo size should be less than 1 MB', 'error');
        return;
      }
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new window.Image();
        img.onload = () => {
          setLogoDimensions({ width: img.width, height: img.height });
          if (img.width !== img.height) {
            showToastMessage('Please choose square logo only', 'error');
          } else {
            setLogoFile(file);
          }
        };
        img.src = e.target.result as string;
      };
      reader.readAsDataURL(file);
    }
    // Reset the input's value to allow selecting the same file again
    e.target.value = '';
  };

  return (
    <div className="flex flex-col flex-1 overflow-y-hidden h-full p-4">
      {googleAdsData?.media_assets && googleAdsData?.text_assets ? (
        <div className="flex flex-col flex-1 overflow-y-hidden">
          <div className="flex flex-col flex-1 overflow-y-scroll no-scrollbar">
            <GoogleAdPreviewsComp
              googleAdsData={googleAdsData}
              mediaAssetsDetails={mediaAssetsDetails}
              type={campaignDetails.type}
              campaignDetails={campaignDetails}
            />
            <GoogleSelectLocationsComp
              geoLocations={geoLocations}
              onLocationsChange={(locations) => setGeoLocations(locations)}
              user={user}
            />
          </div>
          <Button
            onClick={() =>
              onLaunchCampaignClick({
                campaign_id: campaignDetails.id,
                geo_locations: geoLocations,
              })
            }
            className="mt-3"
            disabled={!geoLocations.length}
          >
            <p>Pay and Launch on Google</p>
          </Button>
        </div>
      ) : (
        <div className="flex flex-col flex-1">
          <div className="flex justify-center mt-3">
            <div className="rounded-md overflow-hidden w-80">
              <Image
                src="/images/dashboard/google-ads-illustration.webp"
                width="800"
                height="516"
                alt=""
              />
            </div>
          </div>
          <p className="mt-6">
            Launch campaign on Google and reach additional audience in just
            minutes.
          </p>
          <p className="mt-4 text-xs">
            Please provide below details to generate high-performing ad assets
          </p>
          <p className="text-sm text-gray-dark mt-6">
            Business Name (Max 25 chars)
          </p>
          <div className="mt-3 flex">
            <input
              className={classNames(
                'outline-none border border-gray-medium rounded-lg px-2 py-2 text-sm w-60',
                {
                  'border-red': businessName.length > 25,
                },
              )}
              type="text"
              onChange={(event) => setBusinessName(event.target.value)}
              value={businessName}
            />
          </div>
          <p className="text-sm text-gray-dark mt-6">Business Logo (Square)</p>
          <div
            className="mt-3"
            onClick={() => {
              uploadImageInputRef.current?.click();
            }}
          >
            {businessSquareLogoUrl ? (
              <div className="w-24 rounded-md cursor-pointer">
                <Image
                  src={businessSquareLogoUrl}
                  alt=""
                  width={96}
                  height={96}
                />
              </div>
            ) : (
              <div className="w-24 h-24 flex items-center justify-center bg-gray-medium rounded-md cursor-pointer">
                <UploadIcon className="text-black h-8 w-8" />
              </div>
            )}
          </div>
          <input
            type="file"
            ref={uploadImageInputRef}
            onChange={onUploadImageInputChange}
            className="hidden"
            accept="image/*"
          />
          <div className="flex-1" />
          <Button
            onClick={() =>
              onGenerateAdAssetsClick({
                businessName,
                businessSquareLogoUrl,
                logoDimensions,
              })
            }
            className="mt-12"
            disabled={
              !businessName ||
              businessName.length > 25 ||
              !businessSquareLogoUrl
            }
          >
            <p>Generate Google Ad Assets</p>
          </Button>
        </div>
      )}
      {uploadLogoImageMutation.isLoading ? <FullScreenLoader /> : null}
    </div>
  );
};

export default GoogleCampaignDetailsPreLaunch;
