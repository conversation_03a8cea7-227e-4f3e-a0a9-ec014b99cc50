import { IGoogleSearchLocationsInsights } from 'src/types/campaigns';
import Accordion from '@/components/lib/Accordion';

interface ILocationsInsightsAccordionProps {
  locationsInsightsDetails: IGoogleSearchLocationsInsights[];
}

const LocationsInsightsAccordion = (
  props: ILocationsInsightsAccordionProps,
) => {
  const { locationsInsightsDetails } = props;

  // Sort by clicks in descending order
  const sortedLocations = [...locationsInsightsDetails].sort(
    (a, b) => Number(b.metrics.clicks) - Number(a.metrics.clicks),
  );

  return (
    <div className="mt-4 px-4 py-2 bg-white rounded-lg shadow">
      <Accordion title="Locations Analytics">
        <div className="overflow-x-auto mt-3 no-scrollbar">
          <table className="min-w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100 text-gray-700 text-xs">
                <th className="border border-gray-300 px-4 py-2 text-left">
                  Location
                </th>
                <th className="border border-gray-300 px-4 py-2 text-right">
                  Views
                </th>
                <th className="border border-gray-300 px-4 py-2 text-right">
                  Clicks
                </th>
                <th className="border border-gray-300 px-4 py-2 text-right">
                  Conversion
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedLocations?.map((item, index) => (
                <tr key={index} className="border border-gray-300 text-xxs">
                  <td className="border border-gray-300 px-4 py-2">
                    {item.segments?.geoTargetCityCanonicalName
                      ?.split(',')
                      ?.join(', ') ?? ''}
                  </td>
                  <td className="border border-gray-300 px-4 py-2 text-right">
                    {item.metrics.impressions}
                  </td>
                  <td className="border border-gray-300 px-4 py-2 text-right">
                    {item.metrics.clicks}
                  </td>
                  <td className="border border-gray-300 px-4 py-2 text-right">
                    {item.metrics.allConversions}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Accordion>
    </div>
  );
};

export default LocationsInsightsAccordion;
