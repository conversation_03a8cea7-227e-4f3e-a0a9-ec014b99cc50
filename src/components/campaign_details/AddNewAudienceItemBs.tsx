import { useState, useCallback, ChangeEvent } from 'react';
import debounce from 'lodash.debounce';
import {
  FlexibleTargetingItemType,
  IFlexibleTargetingItem,
} from 'src/types/campaigns';
import CrossIcon from '@/images/common/cross.svg';
import { useQuery } from 'react-query';
import { getAudienceSuggestions } from 'src/actions/onboarding';
import { getCommonHeaders } from 'src/actions';
import { IGroweasyUser } from 'src/types';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import BottomSheet from '../lib/BottomSheet';

interface IAddNewAudienceItemBsProps {
  onAudienceItemAdd: (
    type: FlexibleTargetingItemType,
    audienceItem: IFlexibleTargetingItem,
  ) => void;
  onClose: () => void;
  user: IGroweasyUser;
}

const AddNewAudienceItemBs = (props: IAddNewAudienceItemBsProps) => {
  const { onAudienceItemAdd, onClose, user } = props;

  const [query, setQuery] = useState('');

  const audiencesResponse = useQuery(
    ['getAudienceSuggestions', query],
    () => {
      return getAudienceSuggestions({
        queryParams: {
          q: query,
        },
        headers: getCommonHeaders(user),
      });
    },
    {
      enabled: !!query,
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'AddNewAudienceItemBs.getAudienceSuggestions',
        );
      },
    },
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onQueryChnage = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    debounce((event: ChangeEvent<HTMLInputElement>) => {
      setQuery(event.target.value);
    }, 500),
    [],
  ) as (event: ChangeEvent<HTMLInputElement>) => void;

  return (
    <BottomSheet
      onClose={onClose}
      className="h-4/5"
      loading={audiencesResponse.isFetching}
      contentClassName="h-full flex flex-col flex-1"
    >
      <div className="flex flex-col flex-1 overflow-hidden">
        <div className="flex items-center">
          <input
            className="outline-none w-16 border border-gray-dark rounded-lg px-2 py-2 text-sm mr-4 flex-1"
            type="text"
            onChange={onQueryChnage}
            placeholder="Search targeting audience e.g. frequent travelers"
          />
          <CrossIcon
            width="14"
            height="14"
            className="text-gray-dark cursor-pointer"
            onClick={onClose}
          />
        </div>
        <div className="mt-2 flex-1 overflow-y-scroll no-scrollbar">
          {audiencesResponse?.data?.data?.map((item, index) => {
            return (
              <div
                key={index}
                className="mt-2 cursor-pointer hover:bg-gray-light py-1 px-2 rounded-lg"
                onClick={() => {
                  onAudienceItemAdd(item.type, item);
                  onClose();
                }}
              >
                <p className="text-sm">{item.name}</p>
                <div className="flex items-center">
                  <p className="text-sm text-gray-dark flex-1">
                    {item.description ?? item.path?.join(' > ')}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </BottomSheet>
  );
};

export default AddNewAudienceItemBs;
