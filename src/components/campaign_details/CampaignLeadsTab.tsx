import { GROWEASY_CAMPAIGN_TYPE, ICampaign } from 'src/types/campaigns';
import { ICtwaLead, IGoogleLead, IMetaLead } from 'src/types/leads';
import NoDataFound from '../lib/NoDataFound';
import {
  getCsvFromArray,
  getFormattedTimeString,
  getLeadsDataAfterMappingQuestions,
} from 'src/utils';
import CampaignLeadsListItem from './CampaignLeadsListItem';
import { showToastMessage } from 'src/modules/toast';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import { ILeadsCrmDetails } from 'src/types/leads_crm';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import dynamic from 'next/dynamic';

interface ICampaignLeadsTabProps {
  campaignDetails: ICampaign;
  campaignLeads?: IMetaLead[] | ICtwaLead[] | IGoogleLead[];
  leadsCrmRecordsObj: Record<string, ILeadsCrmDetails>;
  onEditLeadsCrmClick?: (leadsCrmDetails: Partial<ILeadsCrmDetails>) => void;
  partnerConfig?: IPartnerConfig;
  user: IGroweasyUser;
}

const LeadsChartComp = dynamic(() => import('./LeadsChartComp'));

const CampaignLeadsTab = (props: ICampaignLeadsTabProps) => {
  const {
    campaignDetails,
    campaignLeads = [],
    leadsCrmRecordsObj,
    onEditLeadsCrmClick,
    partnerConfig,
    user,
  } = props;

  const parsedLeads = getLeadsDataAfterMappingQuestions(
    campaignLeads,
    campaignDetails.details?.leadgen_form?.questions ?? [],
  );

  const onDownloadAsCsvClick = () => {
    logEvent(EVENT_NAMES.download_leads_as_csv_clicked);
    const arr = parsedLeads.map((item) => {
      // ordering of keys in data will decide ordering of columns
      // we want to keep CUSTOM questions in last columns
      const data = {
        time: getFormattedTimeString(new Date(item.created_time)),
      };
      // process Standard questions first (FULL_NAME, EMAIL, PHONE)
      item.field_data.forEach((fieldData) => {
        if (fieldData.type === 'CUSTOM') {
          // will be processes after standard questions
        } else {
          data[fieldData.type] = fieldData.values?.[0];
        }
      });
      item.field_data.forEach((fieldData) => {
        if (fieldData.type === 'CUSTOM') {
          data[fieldData.label] = fieldData.values?.[0];
        }
      });
      return data;
    });
    const csv = getCsvFromArray(arr);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.href = url;
    link.download = `${partnerConfig?.name ?? 'groweasy'}-leads.csv`;
    document.body.appendChild(link);
    link.click();
    showToastMessage('CSV downloaded!', 'success');
    // Cleanup
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="flex flex-col flex-1 h-full px-4 pb-4 overflow-y-hidden">
      {parsedLeads?.length ? (
        <div className="flex flex-col flex-1 h-full overflow-y-scroll no-scrollbar pb-1">
          <div className="flex flex-col mt-2">
            <p
              className="text-xs font-medium text-hyperlink text-center cursor-pointer"
              onClick={onDownloadAsCsvClick}
            >
              Download as CSV
            </p>
          </div>
          {campaignLeads.length > 1 ? (
            <LeadsChartComp
              campaignLeads={campaignLeads}
              user={user}
              campaignDetails={campaignDetails}
            />
          ) : null}
          {parsedLeads.map((data, index) => {
            return (
              <CampaignLeadsListItem
                key={index}
                data={data}
                campaignName={campaignDetails?.name}
                leadsCrmDetails={leadsCrmRecordsObj[data.id]}
                onEditLeadsCrmClick={onEditLeadsCrmClick}
                partnerConfig={partnerConfig}
              />
            );
          })}
        </div>
      ) : (
        <div className="flex justify-center">
          <NoDataFound
            illustration={{
              url: '/images/dashboard/leads-illustration.svg',
              width: 200,
              height: 200,
            }}
            title={
              campaignDetails?.type === GROWEASY_CAMPAIGN_TYPE.CTWA
                ? 'You will receive your inquiries directly in your WhatsApp Business app.'
                : 'No Leads Found!'
            }
            imageClassName="rounded-xl"
            className="mt-3"
          />
        </div>
      )}
    </div>
  );
};

export default CampaignLeadsTab;
