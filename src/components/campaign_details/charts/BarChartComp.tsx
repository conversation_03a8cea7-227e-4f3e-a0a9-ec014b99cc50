import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Tooltip,
  ResponsiveContainer,
  TooltipProps,
  CartesianGrid,
} from 'recharts';
import { ReactElement } from 'react';
import {
  NameType,
  Payload,
  ValueType,
} from 'recharts/types/component/DefaultTooltipContent';

interface IBarChartCompProps {
  data: Array<Record<string, string | number>>;
  xAxisDataKey: string;
  yAxisDataKey: string;
  height?: number;
  CustomToolTip?:
    | ReactElement
    | ((props: TooltipProps<ValueType, NameType>) => ReactElement);
  barColor?: string;
  syncId?: string;
  tooltipFormatter?: (
    value: ValueType,
    name: NameType,
    props: Payload<ValueType, NameType>,
    index: number,
  ) => [ValueType, NameType] | ValueType;
}

const DEFAULT_BAR_COLOR = '#294744';

const BarChartComp = ({
  data,
  xAxisDataKey,
  yAxisDataKey,
  height = 220,
  CustomToolTip,
  barColor = DEFAULT_BAR_COLOR,
  syncId,
  tooltipFormatter,
}: IBarChartCompProps) => {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart
        data={data}
        margin={{ top: 24, left: -24, right: 12, bottom: 12 }}
        syncId={syncId}
      >
        <XAxis
          dataKey={xAxisDataKey}
          style={{ fontSize: 10 }}
          angle={-45}
          textAnchor="end"
        />
        <YAxis dataKey={yAxisDataKey} fontSize={10} />
        <Tooltip
          content={CustomToolTip ?? undefined}
          formatter={tooltipFormatter}
        />
        <CartesianGrid strokeDasharray="0 3" />
        <Bar
          dataKey={yAxisDataKey}
          fill={barColor}
          radius={[4, 4, 0, 0]}
          maxBarSize={16}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default BarChartComp;
