import { ReactElement } from 'react';
import {
  Bar,
  ComposedChart,
  Label,
  Line,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from 'recharts';
import {
  NameType,
  ValueType,
} from 'recharts/types/component/DefaultTooltipContent';
import { Currency } from 'src/types/campaigns';
import { formatCurrencyAmount } from 'src/utils';

const COLOR_1 = '#289F9F';
const COLOR_2 = '#649bf8';
const COLOR_3 = '#cd6c67';

interface IAreaChartCompProps {
  data: Array<Record<string, string | number>>;
  xAxisDataKey: string;
  bar1DataKey: string;
  bar2DataKey: string;
  lineDataKey: string;
  leftYAxisLabel: string;
  rightYAxisLabel: string;
  height?: number;
  CustomToolTip?:
    | ReactElement
    | ((props: TooltipProps<ValueType, NameType>) => ReactElement);
}

const DayWiseSpendTooltip = ({
  active,
  payload,
  label,
}: TooltipProps<ValueType, NameType>) => {
  if (!active || !payload || !payload.length) return null;
  const data = payload[0].payload as {
    spend: number;
    currency: Currency;
    conversions: number;
  };

  return (
    <div className="bg-white border rounded p-2 shadow text-xs">
      <p className="text-lg">{label}</p>
      <div className=" mt-3 ">
        {payload.map((item, index) => (
          <p
            className="mt-1 capitalize"
            style={{ color: item.color, fontSize: '14px' }}
            key={index}
          >{`${item.name.toString()}: ${
            item.name.toString() === 'Spend' || item.name.toString() === 'CPCO'
              ? formatCurrencyAmount(
                  Number(item?.value.toString()),
                  data?.currency,
                )
              : item.value.toString()
          }`}</p>
        ))}
      </div>
    </div>
  );
};

const ComposedChartComp = ({
  data,
  xAxisDataKey,
  bar1DataKey,
  bar2DataKey,
  lineDataKey,
  leftYAxisLabel,
  rightYAxisLabel,
  height = 220,
  CustomToolTip,
}: IAreaChartCompProps) => {
  return (
    <ResponsiveContainer width="100%" height={height} className="mt-4">
      <ComposedChart
        data={data}
        margin={{
          top: 12,
          left: 12,
          right: 12,
          bottom: 12,
        }}
      >
        <XAxis
          dataKey={xAxisDataKey}
          fontSize={10}
          angle={-45}
          textAnchor="end"
        />

        <YAxis yAxisId={bar2DataKey} type="number" hide />

        <YAxis
          type="number"
          orientation="left"
          yAxisId="left"
          fontSize={10}
          tick={{ fontSize: 10, fill: COLOR_1 }}
          axisLine={{ stroke: COLOR_1 }}
          tickLine={{ stroke: COLOR_1 }}
        >
          <Label
            value={leftYAxisLabel}
            angle={-90}
            fontSize={14}
            offset={-10}
            position="left"
            style={{ textAnchor: 'middle', fontSize: 14, fill: COLOR_1 }}
          />
        </YAxis>

        <YAxis
          type="number"
          orientation="right"
          yAxisId="right"
          fontSize={10}
          tick={{ fontSize: 10, fill: COLOR_3 }}
          axisLine={{ stroke: COLOR_3 }}
          tickLine={{ stroke: COLOR_3 }}
        >
          <Label
            value={rightYAxisLabel}
            angle={90}
            fontSize={14}
            offset={-10}
            position="right"
            style={{
              textAnchor: 'middle',
              fontSize: 14,
              fill: COLOR_3,
            }}
          />
        </YAxis>

        <Tooltip content={CustomToolTip ?? DayWiseSpendTooltip} />
        <Bar
          yAxisId={'left'}
          dataKey={bar1DataKey}
          fill={COLOR_1}
          radius={[6, 6, 0, 0]}
          maxBarSize={14}
        />
        <Bar
          yAxisId={bar2DataKey}
          dataKey={bar2DataKey}
          fill={COLOR_2}
          radius={[6, 6, 0, 0]}
          maxBarSize={14}
        />
        <Line
          yAxisId="right"
          type="monotone"
          dataKey={lineDataKey}
          stroke={COLOR_3}
          strokeWidth={2}
          fillOpacity={1}
          fill="url(#chartColor)"
          dot={{
            stroke: COLOR_3,
            strokeWidth: 2,
            r: 5,
            fill: 'white',
          }}
        />
      </ComposedChart>
    </ResponsiveContainer>
  );
};

export default ComposedChartComp;
