import { ReactElement } from 'react';
import {
  Area,
  AreaChart,
  ResponsiveContainer,
  Tooltip,
  TooltipProps,
  XAxis,
  YAxis,
} from 'recharts';
import {
  NameType,
  Payload,
  ValueType,
} from 'recharts/types/component/DefaultTooltipContent';

const COLOR = '#294744';
const COLOR_START = '#289F9F';
const COLOR_END = '#E2F1F1';

interface IAreaChartCompProps {
  data: Array<Record<string, string | number>>;
  xAxisDataKey: string;
  yAxisDataKey: string;
  height?: number;
  CustomToolTip?:
    | ReactElement
    | ((props: TooltipProps<ValueType, NameType>) => ReactElement);
  syncId?: string;
  tooltipFormatter?: (
    value: ValueType,
    name: NameType,
    props: Payload<ValueType, NameType>,
    index: number,
  ) => [ValueType, NameType] | ValueType;
}

const AreaChartComp = ({
  data,
  xAxisDataK<PERSON>,
  yAxisDataKey,
  height = 220,
  CustomToolTip,
  syncId,
  tooltipFormatter,
}: IAreaChartCompProps) => {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <AreaChart
        data={data}
        margin={{
          top: 24,
          left: -24,
          right: 24,
          bottom: 12,
        }}
        syncId={syncId}
      >
        <defs>
          <linearGradient id="chartColor" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={COLOR_START} stopOpacity={0.8} />
            <stop offset="95%" stopColor={COLOR_END} stopOpacity={0.05} />
          </linearGradient>
        </defs>
        <XAxis
          dataKey={xAxisDataKey}
          style={{ fontSize: 10 }}
          angle={-45}
          textAnchor="end"
        />
        <YAxis dataKey={yAxisDataKey} fontSize={10} />
        <Tooltip
          content={CustomToolTip ?? undefined}
          formatter={tooltipFormatter}
        />
        <Area
          type="monotone"
          dataKey={yAxisDataKey}
          // yAxis={}
          stroke={COLOR}
          strokeWidth={2}
          fillOpacity={1}
          fill="url(#chartColor)"
          dot={{
            stroke: COLOR,
            strokeWidth: 5,
            r: 1,
            fill: 'white',
          }}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

export default AreaChartComp;
