export interface IPost {
  id: string;
  post_url: string;
  post_title: string;
  post_description: string;
  post_text: string;
  post_image: string;
  post_image_desc: string;
  post_image_alt: string;
  post_date: string;
  post_updated: string;
  post_pinned: string;
  post_privacy: string;
  post_meta_title: string;
  post_meta_description: string;
  post_meta_robots: string;
  post_meta_canonical: string;
  post_code_injection_header: string;
  post_code_injection_footer: string;
  post_categories: {
    category_url: string;
    category_name: string;
  }[];
  post_authors: {
    author_avatar: string;
    author_first_name: string;
    author_last_name: string;
    author_url: string;
  }[];
  post_word_count: number;
}

export interface IPostDetails {
  id: string;
  post_url: string;
  post_title: string;
  post_description: string;
  post_text: string;
  post_image: string;
  post_image_desc: string;
  post_image_alt: string;
  post_date: string;
  post_updated: string;
  post_pinned: string;
  post_privacy: string;
  post_meta_title: string;
  post_meta_description: string;
  post_meta_robots: string;
  post_meta_canonical: string;
  post_code_injection_header: string;
  post_code_injection_footer: string;
  categories: {
    category_url: string;
    category_name: string;
  }[];
  authors: {
    author_avatar: string;
    author_first_name: string;
    author_last_name: string;
    author_url: string;
  }[];
  post_word_count: number;
  related_posts: {
    post_url: string;
    post_title: string;
    post_description: string;
    post_image: string;
    post_image_desc: string;
    post_image_alt: string;
    post_date: string;
  }[];
}

export interface IBlogCategory {
  category_url: string;
  category_name: string;
  category_description: string;
  category_meta_title: string;
  category_meta_description: string;
  category_meta_robots: string;
  category_meta_canonical: string;
  category_code_injection_header: string;
  category_code_injection_footer: string;
  category_privacy: string;
  category_update_date: string;
}

export interface IBlogCategoryDetails {
  category_url: string;
  category_title: string;
  category_description: string;
  category_meta_title: string;
  category_meta_description: string;
  category_meta_robots: string;
  category_meta_canonical: string;
  category_code_injection_header: string;
  category_code_injection_footer: string;
  category_privacy: string;
  category_update_date: string;
  posts: IPost[];
}
