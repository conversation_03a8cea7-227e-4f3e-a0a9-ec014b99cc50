export interface IAdCopywriterRequest {
  session_id: string;
  message: string;
  email: string;
}

export interface IAdCopy {
  primary_text?: string;
  headline?: string;
  description?: string;
  call_to_action_type?: string;

  short_headline?: string;
  long_headline?: string;
  short_description?: string;
}

export interface IAdCopywriterData {
  ad_copies: IAdCopy[];
  parameters: {
    channel: string;
    ad_language: string;
    business_details: {
      product_or_service_description: string;
      business_category: string;
    };
  };
}

export interface IAdCopywriterResponse {
  data: {
    message: string;
    ad_copies?: IAdCopywriterData;
  };
}

export interface IAdCopywriterSession {
  sessionId: string;
  email: string;
  conversationData: {
    businessDescription?: string;
    targetAudience?: string;
    adLanguage?: string;
    channel?: string;
  };
}
