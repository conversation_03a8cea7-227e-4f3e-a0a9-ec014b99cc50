export enum ILeadsStatus {
  /*CONTACTED = 'CONTACTED',
  IRRELEVANT = 'IRRELEVANT',
  DID_NOT_CONNECT = 'DID_NOT_CONNECT',
  FOLLOW_UP = 'FOLLOW_UP',
  INTERESTED = 'INTERESTED',
  SALE_DONE = 'SALE_DONE',
  NOT_INTERESTED = 'NOT_INTERESTED',
  YET_TO_CONTACT = 'YET_TO_CONTACT',*/
  GOOD_LEAD_FOLLOW_UP = 'GOOD_LEAD_FOLLOW_UP',
  DID_NOT_CONNECT_OR_BUSY = 'DID_NOT_CONNECT_OR_BUSY',
  BAD_LEAD = 'BAD_LEAD',
  SALE_DONE = 'SALE_DONE',
}

export const LeadStatusNameMapping = {
  [ILeadsStatus.GOOD_LEAD_FOLLOW_UP]: 'Good Lead - Follow Up',
  [ILeadsStatus.DID_NOT_CONNECT_OR_BUSY]: 'Did Not Connect / Busy',
  [ILeadsStatus.BAD_LEAD]: 'Bad Lead',
  [ILeadsStatus.SALE_DONE]: 'Sale Done',
};

export interface ILeadsCrmDetails {
  campaign_id: string;
  uid: string;
  status?: ILeadsStatus;
  note?: string;
  leadgen_id: string;
}
