import { AdPlatforms, GrowEasyPartners } from '.';
import { Currency, IFirestoreTimestamp } from './campaigns';

export enum GrowEasyOrderType {
  // launch = new campaign, extend = existing campaign
  LAUNCH = 'launch',
  EXTEND = 'extend',
}

export interface ICampaignExtensionDetails {
  lifetime_budget: number; // in paise
  end_time: null | string;
  daily_budget?: number; // in paise or cents
}

export interface IOrderDetails {
  razorpay_payment_id?: string;
  razorpay_order_id: string;
  razorpay_signature?: string;
  status: 'created' | 'attempted' | 'paid';
  amount: number;
  currency: Currency;
  created_at: IFirestoreTimestamp;
  updated_at: IFirestoreTimestamp;
  campaign_id: string;
  uid: string;
  type: GrowEasyOrderType;
  campaign_extension_details?: ICampaignExtensionDetails;
  invoice_id?: string;
  stripe_payment_intent_id?: string;
  platform?: AdPlatforms;
  xendit_invoice_id?: string;
  ad_credit?: {
    amount_to_be_consumed: number; // in paise/cents/IDR
    transaction_id?: string | null;
  };
}

export interface IRazorpayCheckoutHandlerResponse {
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
}

export interface IRazorpayCheckoutEventCbResponse {
  error: {
    code: string;
    reason: string;
  };
}

// docs: https://razorpay.com/docs/payments/payment-gateway/web-integration/standard/build-integration/
export interface IRazorpayCheckoutOptions {
  key: string;
  amount?: string | number;
  currency: string;
  name: string;
  description: string;
  image: string;
  order_id: string;
  prefill: {
    name?: string;
    email?: string;
    contact?: string;
  };
  notes?: Record<string, string>;
  theme: {
    color: string;
  };
  handler?: (response: IRazorpayCheckoutHandlerResponse) => void;
  callback_url?: string;
  redirect?: boolean;
}

export interface IRazorpayCheckoutInstance {
  on: (
    event: string,
    callback: (response: IRazorpayCheckoutEventCbResponse) => void,
  ) => void;
  open: () => void;
}

export interface IBillingDetails {
  uid: string;
  country_code: string;
  billing_email: string;
  billing_mobile: string;
  business_name: string;
  business_address: string;
  business_city: string;
  state_code?: string;
  postal_code: string;
  business_tax_id?: string;
  created_at: IFirestoreTimestamp;
  updated_at: IFirestoreTimestamp;
}

export interface IInvoiceDetails {
  order_id: string;
  campaign_id: string;
  uid: string;
  id: string;
  created_at: IFirestoreTimestamp;
  issue_date: IFirestoreTimestamp;
  invoice_no: number;
  line_items: Array<{
    description: string;
    amount: string;
    quantity?: string;
    unit_price?: string;
  }>;
  calculation_details: Array<{
    key: string;
    value: string;
  }>;
  total_payable: string;
  billing_details: Partial<IBillingDetails>;
  campaign_details: {
    business_category: string;
    start_time: string;
    end_time: string;
    friendly_name?: string;
    product_or_service_description: string;
  };
  partner?: GrowEasyPartners | null;
}
