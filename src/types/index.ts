import { Currency, IFirestoreTimestamp } from './campaigns';
import { IRazorpayCheckoutOptions } from './payments_invoices';

declare global {
  interface Window {
    sentry: {
      initializeSentry: () => void;
      addBreadcrumbToSentry: (eventName: string, payload?: object) => void;
      logErrorToSentry: (error: Error, origin: string) => void;
      setUserToSentry: (user: { id?: string; email?: string }) => void;
    };
    bridge: {
      postMessage: (message: string) => void;
    };
    platform?: 'android' | 'ios' | 'web';
    onLoginSuccess: (user: IGroweasyUser) => void;
    onLogout: () => void;
    onInitViaBridge: (user: IGroweasyUser) => void;
    Razorpay: (options: IRazorpayCheckoutOptions) => void;
    // https://developers.facebook.com/docs/meta-pixel/guides/track-multiple-events/
    fbq: (
      name: 'track' | 'trackCustom',
      eventName: string,
      eventParams?: object,
    ) =>
      | void
      | ((
          name: 'trackSingle' | 'trackSingleCustom',
          pixelId: string,
          eventName: string,
          eventParams?: object,
        ) => void);
    FB: {
      login: (
        callback: (response: IFbLoginStatusResult) => void,
        {
          config_id,
          response_type,
          override_default_response_type,
          extras,
        }: {
          config_id: string;
          // must be set to 'code' for System User access token
          response_type?: 'code';
          override_default_response_type?: boolean;
          extras?: {
            setup: Record<string, string>;
          };
        },
      ) => void;
      getLoginStatus: (
        callback: (response: IFbLoginStatusResult) => void,
      ) => void;
      api: (endpoint: string, callback) => void;
      logout: (callback: (response: IFbLoginStatusResult) => void) => void;
    };
    gtag?: (
      arg1: 'event',
      arg2: 'conversion',
      arg3: {
        send_to: string;
      },
    ) => void;
  }
}
export interface IGroweasyUser {
  displayName: string;
  email: string;
  photoUrl: string;
  authToken: string;
  uid: string;
  mobile?: string;
}

export interface IPartnerConfig {
  partner: GrowEasyPartners;
  name: string;
  meta: {
    title: string;
    description: string;
    ogImage: string;
    favIcon: string;
    noIndex: boolean;
  };
  logoImage: string;
  disablePayment: boolean;
  razorpayKeyId: string;
}

export enum GrowEasyPartners {
  BANNERBOT = 'BANNERBOT',
  NIVIDA = 'NIVIDA',
  ZENDOT = 'ZENDOT',
  GENIUS_ADS = 'GENIUS_ADS',
  AD_GLOBAL_AI = 'AD_GLOBAL_AI',
}

export interface IUserProfile {
  name: string;
  uid: string;
  email: string;
  mobile?: string;
  business_name?: string;
  created_at: IFirestoreTimestamp;
  updated_at: IFirestoreTimestamp;
  is_affiliate_marketing?: boolean;
  mobile_dial_code?: string;
  whatsapp_opt_in?: boolean;
  acquisition_source?: 'web' | 'android' | 'ios';
  feature_flags?: {
    meta_sales_campaign?: boolean;
  };
  partner?: GrowEasyPartners;
  number_of_employees?: string;
  has_calling_team?: 'yes' | 'no' | 'online_sales';
  monthly_marketing_budget?: string; // free text
}

/**
 * In case of SUAT-
 * The returned Authorization code must be transmitted to your backend,
  which will perform a server-to-server call from there to our servers for an access token
  S2S: GET https://graph.facebook.com/v17.0/oauth/access_token?client_id=<APP_ID>&client_secret=<APP_SECRET>&code=<CODE>
  Token generated will be Business Integration System User access tokens , Use this (recommended) or Admin system user access token
*/
export interface IFbLoginStatusResult {
  status: 'connected' | 'not_authorized' | 'unknown';
  authResponse?: {
    accessToken: string; // for UAT
    expiresIn: number | null; // null in case of SUAT, can be 0 too (never expire in UAT)
    reauthorize_required_in: string;
    signedRequest: string;
    userID: string;
    code?: string; // for SUAT, i.e. System User Access Token
  };
}

export interface IProcessedFbLoginResponse {
  assigned_pages?: Array<{
    name: string;
    id: string;
    tasks: string[];
    page_access_token: string;
  }>;
  account_details?: {
    name: string;
    id: string;
  };
}

export enum AdPlatforms {
  GOOGLE = 'GOOGLE',
  META = 'META',
}

export interface IWabaPhoneNumberDetails {
  verified_name: string;
  code_verification_status: string;
  display_phone_number: string;
  quality_rating: string;
  platform_type: string;
  last_onboarded_time: string;
  id: string;
}

export interface IAdCreditsTransaction {
  type: 'CREDIT' | 'DEBIT';
  created_at?: IFirestoreTimestamp;
  currency: Currency;
  value: number;
  description: string;
  campaign_id: string;
}

export interface IAdCreditsBalance {
  usd: number;
  inr: number;
  idr: number;
  php: number;
  thb: number;
  vnd: number;
  myr: number;
  created_at: IFirestoreTimestamp;
  updated_at: IFirestoreTimestamp;
}

export interface ISelfAdAccountConfigDetails {
  ad_account_id: string;
  currency: Currency;
}
