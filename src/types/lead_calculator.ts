export interface ILeadCalculatorRequest {
  session_id: string;
  message: string;
  email: string;
}

export interface ILeadCalculatorCplData {
  cpl: number;
  currency: string;
  parameters: {
    industry: string;
    city_category: string;
    channel: string;
    currency: 'USD' | 'INR';
    price_range: string;
  };
  cpl_range_lower: number;
  cpl_range_upper: number;
}

export interface ILeadCalculatorResponse {
  data: {
    message: string;
    cpl_data?: ILeadCalculatorCplData;
  };
}

export interface ILeadCalculatorSession {
  sessionId: string;
  email: string;
  conversationData: {
    industry?: string;
    city?: string;
    productPrice?: string;
    channel?: string;
  };
}
