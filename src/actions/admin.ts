import { ICampaign } from 'src/types/campaigns';
import { API_ENDPOINTS, fetchPostRequest, fetchRequest } from '.';
import { IUserProfile } from 'src/types';

export const getAdminCampaigns = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: {
    campaigns: ICampaign[];
    last_cursor_id: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.ADMIN_CAMPAIGNS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: {
      campaigns: ICampaign[];
      last_cursor_id: string;
    };
  }>;
  return response;
};

export const getAdminUserProfileDetails = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IUserProfile | null;
}> => {
  const url = new URL(API_ENDPOINTS.ADMIN_USER_PROFILE);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IUserProfile;
  }>;
  return response;
};

export const updateAdminUserCampaign = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: Partial<ICampaign>;
}): Promise<{
  data: ICampaign;
}> => {
  const url = new URL(API_ENDPOINTS.ADMIN_USER_CAMPAIGN);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: ICampaign;
  }>;
  return response;
};

export const generateAdminAiMedia = async ({
  headers,
  data,
  queryParams = {},
}: {
  headers: Record<string, string>;
  data: {
    campaign_id: string;
    media_type: 'image';
  };
  queryParams: Record<string, string>;
}): Promise<{
  data: {
    hash: string;
    width: number;
    height: number;
    s3_url: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.ADMIN_AI_MEDIA);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      hash: string;
      width: number;
      height: number;
      s3_url: string;
    };
  }>;
  return response;
};
