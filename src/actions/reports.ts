import { IAdInsightCardsData } from 'src/types/reports';
import { API_ENDPOINTS, fetchRequest } from '.';

export const getAdInsightCards = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IAdInsightCardsData;
}> => {
  const url = new URL(API_ENDPOINTS.AD_INSIGHT_CARDS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IAdInsightCardsData;
  }>;
  return response;
};
