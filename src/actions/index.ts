import fetchRetry from 'fetch-retry';
import { IGroweasyUser } from 'src/types';
import FetchError from './FetchError';

const BASE_URL = 'https://groweasy.ai/api';
//const BASE_URL = 'http://localhost:4000/api';

export const API_ENDPOINTS = {
  CAMPAIGNS: BASE_URL + '/db/campaigns',
  CAMPAIGNS_V2: BASE_URL + '/db/v2/campaigns',
  EDIT_CAMPAIGN_POST_LAUNCH: BASE_URL + '/db/campaigns/edit-post-launch',
  BUSINESS_CATEGORIES: BASE_URL + '/db/business-categories',
  META_SEARCH: BASE_URL + '/meta/search',
  OPENAI_AD_BANNERS: BASE_URL + '/openai/ad-banners',
  CAMPAIGN_DETAILS: BASE_URL + '/db/campaigns/:campaign_id',
  META_AD_IMAGES: BASE_URL + '/meta/adimages',
  META_AD_IMAGES_V2: BASE_URL + '/meta/v2/adimages',
  POPULATE_DETAILED_TARGETING: BASE_URL + '/openai/populate-detailed-targeting',
  GENERATE_LEADGEN_FORM: BASE_URL + '/openai/generate-leadgen-form',
  POPULATE_AD_COPIES: BASE_URL + '/openai/populate-ad-copies',
  META_AD_PREVIEW: BASE_URL + '/meta/ad-preview',
  META_LEADS: BASE_URL + '/meta/leads',
  UPDATE_CAMPAIGN_STATUS: BASE_URL + '/db/campaigns/update-status',
  META_INSIGHTS: BASE_URL + '/meta/insights',
  ALL_FORM_LEADS: BASE_URL + '/db/all-leads',
  SEND_WELCOME_EMAIL: BASE_URL + '/db/send-welcome-email',
  POPULATE_SPECIAL_AD_CATEGORIES:
    BASE_URL + '/openai/populate-special-ad-categories',
  META_TARGETING_SEARCH: BASE_URL + '/meta/targetingsearch',
  ADMIN_CAMPAIGNS: BASE_URL + '/admin/campaigns',
  ADMIN_AI_MEDIA: BASE_URL + '/admin/ai-media',
  ORDER_DETAILS: BASE_URL + '/db/orders/:order_id',
  VERIFY_PAYMENT: BASE_URL + '/db/verify-payment',
  ORDERS: BASE_URL + '/db/orders',
  LEADS_CRM: BASE_URL + '/crm/leads-crm',
  USERS_PROFILE: BASE_URL + '/users/profile',
  USERS_CONTACT_US: BASE_URL + '/users/contact-us',
  BILLING_DETAILS: BASE_URL + '/db/billing-details',
  INVOICES: BASE_URL + '/db/invoices',
  INVOICE_DETAILS: BASE_URL + '/db/invoices/:invoice_id',
  ADMIN_INVOICES: BASE_URL + '/admin/invoices',
  EXTEND_CAMPAIGN: BASE_URL + '/db/campaigns/extend',
  USERS_PROFILE_V2: BASE_URL + '/users/v2/profile',
  CTWA_LEADS: BASE_URL + '/ctwa/leads',
  ALL_CTWA_LEADS: BASE_URL + '/ctwa/all-leads',
  STRIPE_PAYMENT_INTENT: BASE_URL + '/payment/stripe/payment-intent',
  SEO_BLOGS_CATEGORIES: BASE_URL + '/seo/blogs-categories',
  META_FB_LOGIN: BASE_URL + '/meta/fb-login',
  META_FB_PAGE_ACCESS: BASE_URL + '/meta/fb-page-access',
  META_FB_PAGE_CREATE_POST: BASE_URL + '/meta/fb-page-create-post',
  META_AD_VIDEOS: BASE_URL + '/meta/advideos',
  META_AD_VIDEOS_DETAILS: BASE_URL + '/meta/advideos/details',
  VIDEO_SCRIPTS: BASE_URL + '/video-scripts',
  CREATE_VIDEO_ORDER: BASE_URL + '/video-orders',
  USERS_DELETE_ACCOUNT: BASE_URL + '/users/delete-account',
  AI_VIDEO_AD_TEMPLATE: BASE_URL + '/openai/video-data',
  GENERATE_AI_VIDEO_AD: BASE_URL + '/openai/generate-video',
  META_AD_VIDEOS_V2: BASE_URL + '/meta/advideos-v2',
  META_DELIVERY_ESTIMATE: BASE_URL + '/meta/delivery-estimate',
  META_AD_IMAGES_V3: BASE_URL + '/meta/v3/adimages',
  CREATE_MASTER_CLASS_ORDER: BASE_URL + '/master-class-order',
  BANNER_BASED_VIDEO_TEMPLATE: BASE_URL + '/video/banner-video-template',
  GENERATE_BANNER_BASED_VIDEO: BASE_URL + '/video/banner-video',
  S3_ASSET_URL: BASE_URL + '/google/s3-asset-url',
  GOOGLE_AD_ASSETS: BASE_URL + '/google/ad-assets',
  GOOGLE_MEDIA_ASSETS: BASE_URL + '/google/media-assets',
  GOOGLE_LOCATION_SEARCH: BASE_URL + '/google/location-search',
  GOOGLE_LANCH_CAMPAIGN: BASE_URL + '/google/launch',
  GOOGLE_LEAD_FORM_DATA: BASE_URL + '/google/lead-form-data',
  GOOGLE_INSIGHTS: BASE_URL + '/google/insights',
  GOOGLE_INSIGHTS_V2: BASE_URL + '/google/v2/insights',
  GOOGLE_LEADS: BASE_URL + '/google/leads',
  FB_AUDIENCE_BUILDER: BASE_URL + '/tools/facebook-audience-builder',
  GOOGLE_LEAD_FORM_CONTENT: BASE_URL + '/google/populate-lead-form-content',
  GOOGLE_SEARCH_KEYWORDS_SUGGESTIONS:
    BASE_URL + '/google/populate-search-keywords-suggestions',
  GOOGLE_KEYWORD_IDEAS_FOR_SEO: BASE_URL + '/tools/google-keyword-ideas',
  GOOGLE_KEYWORD_IDEAS: BASE_URL + '/google/keyword-ideas',
  FB_ASSIGNED_PAGES: BASE_URL + '/db/fb-assigned-pages',
  META_CREATIVE_INSIGHTS: BASE_URL + '/meta/creative-insights',
  META_WABA_ONBOARDING: BASE_URL + '/meta/waba-onboarding',
  GOGLE_AD_VIDEOS: BASE_URL + '/google/advideos',
  GOOGLE_AD_VIDEOS_V2: BASE_URL + '/google/advideos-v2',
  GOOGLE_CUSTOM_CONVERSION_ACTION:
    BASE_URL + '/google/custom-conversion-action',
  ADMIN_USER_PROFILE: BASE_URL + '/admin/user-profile',
  ADMIN_USER_CAMPAIGN: BASE_URL + '/admin/user-campaign',
  VIDEO_SEARCH: BASE_URL + '/video/search/videos',
  REMOTION_VIDEO_DATA: BASE_URL + '/video/remotion-video-data',
  REMOTION_VIDEO: BASE_URL + '/video/remotion-video',
  AD_LANGUAGE_SUGGESTIONS: BASE_URL + '/openai/ad-language-suggestions',
  USPS_AND_BANNER_ELEMENTS: BASE_URL + '/openai/usps-and-banner-elements',
  IDEAL_CUSTOMERS: BASE_URL + '/openai/ideal-customers',
  GENERATE_AI_BANNER: BASE_URL + '/openai/generate-ai-ad-banner',
  AD_CREDIT_BALANCE: BASE_URL + '/db/ad-credit/balance',
  AD_CREDIT_TRANSACTIONS: BASE_URL + '/db/ad-credit/transactions',
  AD_INSIGHT_CARDS: BASE_URL + '/reports/ad-insight-cards',
  XENDIT_INVOICE: BASE_URL + '/payment/xendit/invoice',
  GOOGLE_TEXT_ASSETS: BASE_URL + '/google/text-assets',
  LEAD_CALCULATOR: BASE_URL + '/agents/lead-calculator',
  AD_COPYWRITER: BASE_URL + '/agents/ad-copies-generator',
};

const fetch = fetchRetry(global.fetch, {
  retries: 2, // Number of retries
  retryDelay: (attempt, error, response) => {
    console.log(error?.message, response?.status);
    console.log('fetchRetry attempt:', attempt);
    return Math.pow(2, attempt) * 1000; // Exponential backoff
  },
  retryOn: [502, 503, 504], // Retry on these status codes
});

export const getCommonHeaders = (user?: IGroweasyUser) => {
  return {
    Authorization: `Bearer ${user?.authToken}`,
    'Content-Type': 'application/json',
    'GrowEasy-Trace-Id': user?.uid ?? '',
  };
};

export const fetchRequest = async ({
  url,
  headers,
}: {
  url: string;
  headers: Record<string, string>;
}) => {
  const response = await fetch(url, {
    headers,
  });
  let data: object = {};
  try {
    data = (await response.json()) as Promise<object>;
  } catch (error) {
    // ignore
  }
  if (response.ok) {
    return data;
  } else {
    throw new FetchError(response.status, data as Record<string, unknown>);
  }
};

export const fetchPostRequest = async ({
  url,
  headers,
  body,
}: {
  url: string;
  headers: Record<string, string>;
  body: object;
}) => {
  const response = await fetch(url, {
    headers,
    body: JSON.stringify(body),
    method: 'POST',
  });
  let data: object = {};
  try {
    data = (await response.json()) as Promise<object>;
  } catch (error) {
    // ignore
  }
  if (response.ok) {
    return data;
  } else {
    throw new FetchError(response.status, data as Record<string, unknown>);
  }
};

export const fetchFormPostRequest = async ({
  url,
  headers,
  body,
}: {
  url: string;
  headers: Record<string, string>;
  body: FormData;
}) => {
  // form data will make it multipart/form-data
  delete headers['Content-Type'];
  const response = await fetch(url, {
    headers,
    body,
    method: 'POST',
  });
  let data: object = {};
  try {
    data = (await response.json()) as Promise<object>;
  } catch (error) {
    // ignore
  }
  if (response.ok) {
    return data;
  } else {
    throw new FetchError(response.status, data as Record<string, unknown>);
  }
};
