import { QueryParams } from 'src/constants';
import { IBannerImage, IBannerTemplate } from 'src/types/banner_templates';
import {
  AdLanguage,
  GROWEASY_CAMPAIGN_TYPE,
  GrowEasyCampaignStatus,
  IAdVideo,
  IAdVideoDetails,
  IAiVideoAdTemplate,
  IBannerBasedVideoTemplate,
  IBannerElement,
  IBusinessDetails,
  ICampaign,
  ICampaignConfig,
  IFlexibleTargetingItem,
  IGoogleAdsData,
  IGoogleKeywordIdeas,
  IGoogleLocationDetails,
  ILanguageSuggestionsResponse,
  ILocationDetails,
  IProductUspsAndBannerElementsResponse,
  ITargeting,
  IUploadAdBannerResponse,
  IVideoTemplate,
} from 'src/types/campaigns';
import {
  IOrderDetails,
  IRazorpayCheckoutHandlerResponse,
} from 'src/types/payments_invoices';
import {
  API_ENDPOINTS,
  fetchFormPostRequest,
  fetchPostRequest,
  fetchRequest,
} from '.';
import {
  IFbLoginStatusResult,
  IProcessedFbLoginResponse,
  IWabaPhoneNumberDetails,
} from 'src/types';
import {
  IPexelsVideoData,
  IRemotionVideoData,
  IRemotionVideoDataResponse,
} from 'src/types/remotion';

export const getCampaignDetails = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: ICampaign;
}> => {
  const url = new URL(
    API_ENDPOINTS.CAMPAIGN_DETAILS.replace(
      ':campaign_id',
      queryParams[QueryParams.CAMPAIGN_ID],
    ),
  );
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: ICampaign;
  }>;
  return response;
};

// if id is available, it will be updated
// else a new campaign will be created
export const createOrUpdateCampaign = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: ICampaign;
}): Promise<{
  data: ICampaign;
}> => {
  const url = new URL(API_ENDPOINTS.CAMPAIGNS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: ICampaign;
  }>;
  return response;
};

export const getBusinessCategories = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: string[];
}> => {
  const url = new URL(API_ENDPOINTS.BUSINESS_CATEGORIES);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: string[];
  }>;
  return response;
};

export const getLocationSuggestions = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: ILocationDetails[];
}> => {
  const updatedQueryParams: Record<string, string> = {
    type: 'adgeolocation',
    limit: `20`,
    place_fallback: 'true',
    ...queryParams,
  };
  const url = new URL(API_ENDPOINTS.META_SEARCH);
  Object.keys(updatedQueryParams).forEach((key) => {
    url.searchParams.set(key, updatedQueryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: ILocationDetails[];
  }>;
  return response;
};

export const getBannerTemplates = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: {
    business_details: IBusinessDetails;
    targeting: ITargeting;
    google_geo_locations?: IGoogleLocationDetails[];
    ai_assisted_product_usps?: string[];
    ad_language?: AdLanguage;
  };
}): Promise<{
  data: {
    templates: IBannerTemplate[];
    images: IBannerImage[];
  };
}> => {
  const url = new URL(API_ENDPOINTS.OPENAI_AD_BANNERS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      templates: IBannerTemplate[];
      images: IBannerImage[];
    };
  }>;
  return response;
};

export const uploadAdImage = async ({
  headers,
  queryParams = {},
  file,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  file: File;
}): Promise<IUploadAdBannerResponse> => {
  const url = new URL(API_ENDPOINTS.META_AD_IMAGES);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const formData = new FormData();
  if (file) {
    formData.append('file', file, file.name);
  }
  const response = (await fetchFormPostRequest({
    url: url.toString(),
    headers,
    body: formData,
  })) as Promise<IUploadAdBannerResponse>;
  return response;
};

export const getAdImages = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: Array<{
    permalink_url: string;
    width: number;
    height: number;
    url: string;
    hash: string;
  }>;
}> => {
  queryParams.fields =
    'id,hash,permalink_url,status,width,height,label,url,url_128';
  const url = new URL(API_ENDPOINTS.META_AD_IMAGES);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: Array<{
      permalink_url: string;
      width: number;
      height: number;
      url: string;
      hash: string;
    }>;
  }>;
  return response;
};

export const populateDetailedTargeting = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: ICampaign;
}> => {
  const url = new URL(API_ENDPOINTS.POPULATE_DETAILED_TARGETING);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {},
  })) as Promise<{
    data: ICampaign;
  }>;
  return response;
};

export const generateLeadgenForm = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: ICampaign;
}> => {
  const url = new URL(API_ENDPOINTS.GENERATE_LEADGEN_FORM);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {},
  })) as Promise<{
    data: ICampaign;
  }>;
  return response;
};

export const populateAdCopies = async (
  {
    headers,
    queryParams = {},
  }: {
    headers: Record<string, string>;
    queryParams: Record<string, string>;
  },
  campaignType: GROWEASY_CAMPAIGN_TYPE,
): Promise<{
  data: ICampaign;
}> => {
  const url = new URL(
    [
      GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH,
      GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
    ].includes(campaignType)
      ? API_ENDPOINTS.GOOGLE_TEXT_ASSETS
      : API_ENDPOINTS.POPULATE_AD_COPIES,
  );
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {},
  })) as Promise<{
    data: ICampaign;
  }>;
  return response;
};

export const getAdPreview = async ({
  headers,
  queryParams,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: Array<{
    body: string;
  }>;
}> => {
  const url = new URL(API_ENDPOINTS.META_AD_PREVIEW);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: Array<{
      body: string;
    }>;
  }>;
  return response;
};

export const updateCampaignStatus = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: {
    id: string;
    status: GrowEasyCampaignStatus;
  };
}): Promise<{
  data: ICampaign;
}> => {
  const url = new URL(API_ENDPOINTS.UPDATE_CAMPAIGN_STATUS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: ICampaign;
  }>;
  return response;
};

export const populateSpecialAdCategories = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: ICampaign;
}> => {
  const url = new URL(API_ENDPOINTS.POPULATE_SPECIAL_AD_CATEGORIES);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {},
  })) as Promise<{
    data: ICampaign;
  }>;
  return response;
};

export const getAudienceSuggestions = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IFlexibleTargetingItem[];
}> => {
  const url = new URL(API_ENDPOINTS.META_TARGETING_SEARCH);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IFlexibleTargetingItem[];
  }>;
  return response;
};

export const verifyPayment = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data:
    | IRazorpayCheckoutHandlerResponse
    | {
        stripe_payment_intent_id: string;
      };
}): Promise<{
  data: IOrderDetails;
}> => {
  const url = new URL(API_ENDPOINTS.VERIFY_PAYMENT);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: IOrderDetails;
  }>;
  return response;
};

// deprecated in favour of uploadAdImageV3
export const uploadAdImageV2 = async ({
  headers,
  queryParams = {},
  template,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  template: IBannerTemplate;
}): Promise<IUploadAdBannerResponse> => {
  const url = new URL(API_ENDPOINTS.META_AD_IMAGES_V2);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {
      template,
    },
  })) as Promise<IUploadAdBannerResponse>;
  return response;
};

export const processFbLoginDetails = async ({
  headers,
  body,
  queryParams = {},
}: {
  headers: Record<string, string>;
  body: {
    result: IFbLoginStatusResult;
  };
  queryParams: Record<string, string>;
}): Promise<{
  data: IProcessedFbLoginResponse;
}> => {
  const url = new URL(API_ENDPOINTS.META_FB_LOGIN);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body,
  })) as Promise<{
    data: IProcessedFbLoginResponse;
  }>;
  return response;
};

export const grantFbPageAccess = async ({
  headers,
  body,
  queryParams = {},
}: {
  headers: Record<string, string>;
  body: {
    fb_page_id: string;
  };
  queryParams: Record<string, string>;
}): Promise<void> => {
  const url = new URL(API_ENDPOINTS.META_FB_PAGE_ACCESS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  await fetchPostRequest({
    url: url.toString(),
    headers,
    body,
  });
};

export const createPostOnFbPage = async ({
  headers,
  body,
  queryParams = {},
}: {
  headers: Record<string, string>;
  body: {
    fb_page_id: string;
    url: string;
    message: string;
  };
  queryParams: Record<string, string>;
}): Promise<{
  data: Partial<ICampaignConfig>;
}> => {
  const url = new URL(API_ENDPOINTS.META_FB_PAGE_CREATE_POST);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body,
  })) as Promise<{ data: Partial<ICampaignConfig> }>;
  return response;
};

export const uploadAdVideo = async ({
  headers,
  queryParams = {},
  blob,
  fileName,
  campaignType,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  blob: Blob;
  fileName: string;
  campaignType: GROWEASY_CAMPAIGN_TYPE;
}): Promise<{ data: IAdVideo }> => {
  const url = new URL(
    campaignType === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX
      ? API_ENDPOINTS.GOGLE_AD_VIDEOS
      : API_ENDPOINTS.META_AD_VIDEOS,
  );
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const formData = new FormData();
  if (blob) {
    formData.append('file', blob, fileName);
  }
  const response = (await fetchFormPostRequest({
    url: url.toString(),
    headers,
    body: formData,
  })) as Promise<{ data: IAdVideo }>;
  return response;
};

export const getAdVideosDetails = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: {
    [id: string]: IAdVideoDetails;
  };
}> => {
  const url = new URL(API_ENDPOINTS.META_AD_VIDEOS_DETAILS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: {
      [id: string]: IAdVideoDetails;
    };
  }>;
  return response;
};

// deprecated, use `getBannerBasedVideoAdTemplate`
export const getAiVideoAdTemplate = async ({
  headers,
  data,
  queryParams = {},
}: {
  headers: Record<string, string>;
  data: {
    business_details: IBusinessDetails;
    targeting: ITargeting;
  };
  queryParams: Record<string, string>;
}): Promise<{ data: IAiVideoAdTemplate }> => {
  const url = new URL(API_ENDPOINTS.AI_VIDEO_AD_TEMPLATE);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{ data: IAiVideoAdTemplate }>;
  return response;
};

// deprecated, use `generateBannerBasedVideoAd`
export const generateAiVideoAd = async ({
  headers,
  data,
  queryParams = {},
}: {
  headers: Record<string, string>;
  data: { template: IVideoTemplate };
  queryParams: Record<string, string>;
}): Promise<{
  data: {
    url: string;
    caption: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.GENERATE_AI_VIDEO_AD);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      url: string;
      caption: string;
    };
  }>;
  return response;
};

export const uploadAiGeneratedAdVideo = async ({
  headers,
  queryParams = {},
  data,
  campaignType,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: {
    file_url: string;
  };
  campaignType: GROWEASY_CAMPAIGN_TYPE;
}): Promise<{ data: IAdVideo }> => {
  const url = new URL(
    campaignType === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX
      ? API_ENDPOINTS.GOOGLE_AD_VIDEOS_V2
      : API_ENDPOINTS.META_AD_VIDEOS_V2,
  );
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{ data: IAdVideo }>;
  return response;
};

export const getDeliveryEstimate = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<object> => {
  const url = new URL(API_ENDPOINTS.META_DELIVERY_ESTIMATE);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<object>;
  return response;
};

export const uploadAdImageV3 = async ({
  headers,
  queryParams = {},
  template,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  template: IBannerTemplate;
}): Promise<IUploadAdBannerResponse[]> => {
  const url = new URL(API_ENDPOINTS.META_AD_IMAGES_V3);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {
      template,
    },
  })) as Promise<IUploadAdBannerResponse[]>;
  return response;
};

export const getBannerBasedVideoAdTemplate = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{ data: IBannerBasedVideoTemplate }> => {
  const url = new URL(API_ENDPOINTS.BANNER_BASED_VIDEO_TEMPLATE);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{ data: IBannerBasedVideoTemplate }>;
  return response;
};

export const generateBannerBasedVideoAd = async ({
  headers,
  data,
  queryParams = {},
}: {
  headers: Record<string, string>;
  data: { template: IBannerBasedVideoTemplate };
  queryParams: Record<string, string>;
}): Promise<{
  data: {
    url: string;
    caption: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.GENERATE_BANNER_BASED_VIDEO);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      url: string;
      caption: string;
    };
  }>;
  return response;
};

export const populateGoogleLeadFormContent = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IGoogleAdsData['lead_form_content'];
}> => {
  const url = new URL(API_ENDPOINTS.GOOGLE_LEAD_FORM_CONTENT);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {},
  })) as Promise<{
    data: IGoogleAdsData['lead_form_content'];
  }>;
  return response;
};

export const populateGoogleSearchKeywordsSuggestions = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IGoogleAdsData['search_keywords_suggestions'];
}> => {
  const url = new URL(API_ENDPOINTS.GOOGLE_SEARCH_KEYWORDS_SUGGESTIONS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {},
  })) as Promise<{
    data: IGoogleAdsData['search_keywords_suggestions'];
  }>;
  return response;
};

export const searchVideos = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IPexelsVideoData[];
}> => {
  const url = new URL(API_ENDPOINTS.VIDEO_SEARCH);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IPexelsVideoData[];
  }>;

  return response;
};

export const getRemotionVideoData = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<IRemotionVideoDataResponse> => {
  const url = new URL(API_ENDPOINTS.REMOTION_VIDEO_DATA);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<IRemotionVideoDataResponse>;
  return response;
};

export const getGoogleKeywordIdeas = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: {
    url?: string;
    seed_keywords: string[];
    geo_target_constants?: string[];
  };
}): Promise<{
  data: {
    keyword_ideas: IGoogleKeywordIdeas[];
  };
}> => {
  const url = new URL(API_ENDPOINTS.GOOGLE_KEYWORD_IDEAS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      keyword_ideas: IGoogleKeywordIdeas[];
    };
  }>;
  return response;
};

export const getFbAssignedPages = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  adminFlow?: boolean;
}): Promise<{
  data: IProcessedFbLoginResponse | null;
}> => {
  const url = new URL(API_ENDPOINTS.FB_ASSIGNED_PAGES);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IProcessedFbLoginResponse;
  }>;
  return response;
};

export const processWabaOnboarding = async ({
  headers,
  body,
  queryParams = {},
}: {
  headers: Record<string, string>;
  body: {
    code: string;
  };
  queryParams: Record<string, string>;
}): Promise<{
  data: {
    phone_numbers: IWabaPhoneNumberDetails[];
  };
}> => {
  const url = new URL(API_ENDPOINTS.META_WABA_ONBOARDING);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body,
  })) as Promise<{
    data: {
      phone_numbers: IWabaPhoneNumberDetails[];
    };
  }>;
  return response;
};

export const createCustomConversionAction = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<void> => {
  const url = new URL(API_ENDPOINTS.GOOGLE_CUSTOM_CONVERSION_ACTION);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {},
  });
};

export const generateRemotionVideo = async ({
  headers,
  data,
  queryParams = {},
}: {
  headers: Record<string, string>;
  data: {
    campaign_id: string;
    template_data: IRemotionVideoData;
  };
  queryParams: Record<string, string>;
}): Promise<{
  data: { s3_url: string };
}> => {
  const url = new URL(API_ENDPOINTS.REMOTION_VIDEO);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: { s3_url: string };
  }>;
  return response;
};

export const generateAiAdBanner = async ({
  headers,
  data,
  queryParams = {},
}: {
  headers: Record<string, string>;
  data: {
    campaign_id: string;
    size: 'square' | 'portrait';
    banner_details: IBannerElement;
    vendor?: 'ideogram' | 'openai';
  };
  queryParams: Record<string, string>;
}): Promise<{
  data: {
    hash: string;
    width: number;
    height: number;
    s3_url: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.GENERATE_AI_BANNER);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      hash: string;
      width: number;
      height: number;
      s3_url: string;
    };
  }>;
  return response;
};

export const fetchAdLanguageSuggestions = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<ILanguageSuggestionsResponse> => {
  const url = new URL(API_ENDPOINTS.AD_LANGUAGE_SUGGESTIONS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = await fetchRequest({
    url: url.toString(),
    headers,
  });

  return response as ILanguageSuggestionsResponse;
};

export const fetchProductUspsAndBannerElements = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<IProductUspsAndBannerElementsResponse> => {
  const url = new URL(API_ENDPOINTS.USPS_AND_BANNER_ELEMENTS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = await fetchRequest({
    url: url.toString(),
    headers,
  });

  return response as IProductUspsAndBannerElementsResponse;
};

export const getIdealCustomers = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: {
    ideal_customers: string;
    age_min: number;
    age_max: number;
    genders: number[];
    consumer_type: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.IDEAL_CUSTOMERS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });

  const response = await fetchRequest({
    url: url.toString(),
    headers,
  });

  return response as {
    data: {
      ideal_customers: string;
      age_min: number;
      age_max: number;
      genders: number[];
      consumer_type: string;
    };
  };
};
