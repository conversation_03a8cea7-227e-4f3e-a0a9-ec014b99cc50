import { API_ENDPOINTS, fetchPostRequest } from '.';

export const createStripePaymentIntent = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: {
    razorpay_order_id: string;
  };
}): Promise<{
  data: {
    client_secret: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.STRIPE_PAYMENT_INTENT);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      client_secret: string;
    };
  }>;
  return response;
};

export const createXenditInvoice = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: {
    razorpay_order_id: string;
  };
}): Promise<{
  data: {
    invoice_url: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.XENDIT_INVOICE);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      invoice_url: string;
    };
  }>;
  return response;
};
