import { QueryParams } from 'src/constants';
import {
  IAdCreditsBalance,
  IAdCreditsTransaction,
  IUserProfile,
} from 'src/types';
import {
  IBillingDetails,
  IInvoiceDetails,
  IOrderDetails,
} from 'src/types/payments_invoices';
import { API_ENDPOINTS, fetchPostRequest, fetchRequest } from '.';

export const getOrders = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IOrderDetails[];
}> => {
  const url = new URL(API_ENDPOINTS.ORDERS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IOrderDetails[];
  }>;
  return response;
};

export const getBillingDetails = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IBillingDetails;
}> => {
  const url = new URL(API_ENDPOINTS.BILLING_DETAILS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IBillingDetails;
  }>;
  return response;
};

export const createOrUpdateBillingDetails = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: Partial<IBillingDetails>;
}): Promise<{
  data: IBillingDetails;
}> => {
  const url = new URL(API_ENDPOINTS.BILLING_DETAILS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: IBillingDetails;
  }>;
  return response;
};

export const getInvoices = async ({
  headers,
  queryParams = {},
  adminFlow,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  adminFlow?: boolean;
}): Promise<{
  data: IInvoiceDetails[];
}> => {
  const url = new URL(
    adminFlow ? API_ENDPOINTS.ADMIN_INVOICES : API_ENDPOINTS.INVOICES,
  );
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IInvoiceDetails[];
  }>;
  return response;
};

export const getInvoiceDetails = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IInvoiceDetails;
}> => {
  const url = new URL(
    API_ENDPOINTS.INVOICE_DETAILS.replace(
      ':invoice_id',
      queryParams[QueryParams.INVOICE_ID],
    ),
  );
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IInvoiceDetails;
  }>;
  return response;
};

export const getUserProfile = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IUserProfile;
}> => {
  const url = new URL(API_ENDPOINTS.USERS_PROFILE_V2);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IUserProfile;
  }>;
  return response;
};

export const createOrUpdateUserProfile = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: Partial<IUserProfile>;
}): Promise<{
  data: IUserProfile;
}> => {
  const url = new URL(API_ENDPOINTS.USERS_PROFILE_V2);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: IUserProfile;
  }>;
  return response;
};

export const deleteUserAccount = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<void> => {
  const url = new URL(API_ENDPOINTS.USERS_DELETE_ACCOUNT);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: {},
  })) as Promise<{
    data: IUserProfile;
  }>;
  return;
};

export const getAdCreditBalance = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IAdCreditsBalance;
}> => {
  const url = new URL(API_ENDPOINTS.AD_CREDIT_BALANCE);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IAdCreditsBalance;
  }>;
  return response;
};

export const getAdCreditTransactions = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IAdCreditsTransaction[];
}> => {
  const url = new URL(API_ENDPOINTS.AD_CREDIT_TRANSACTIONS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IAdCreditsTransaction[];
  }>;
  return response;
};

export const submitContactUs = async ({
  headers,
  data,
}: {
  headers: Record<string, string>;
  data: {
    subject: string;
    message: string;
  };
}): Promise<void> => {
  const url = new URL(API_ENDPOINTS.USERS_CONTACT_US);
  await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  });
};
