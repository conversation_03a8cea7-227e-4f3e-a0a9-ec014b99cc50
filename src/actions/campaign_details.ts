import {
  ICampaignExtensionDetails,
  IOrderDetails,
} from 'src/types/payments_invoices';
import {
  API_ENDPOINTS,
  fetchFormPostRequest,
  fetchPostRequest,
  fetchRequest,
} from '.';
import {
  ICampaign,
  ICampaignInsightDetails,
  IGoogleAdsData,
  IGoogleCallsInsights,
  IGoogleCampaignInsights,
  IGoogleLocationDetails,
  IGoogleMediaAssetDetails,
  IGoogleSearchKeywordsInsights,
  IGoogleSearchLocationsInsights,
} from 'src/types/campaigns';
import { IGoogleLead } from 'src/types/leads';

export const extendCampaign = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: {
    campaign_id: string;
    campaign_extension_details: ICampaignExtensionDetails;
  };
}): Promise<{
  data: IOrderDetails;
}> => {
  const url = new URL(API_ENDPOINTS.EXTEND_CAMPAIGN);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: IOrderDetails;
  }>;
  return response;
};

export const updateCampaignPostLaunch = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: Partial<ICampaign>;
}): Promise<{
  data: ICampaign;
}> => {
  const url = new URL(API_ENDPOINTS.EDIT_CAMPAIGN_POST_LAUNCH);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: ICampaign;
  }>;
  return response;
};

export const uploadFileAndGetS3Url = async ({
  headers = {},
  queryParams = {},
  data,
}: {
  headers?: Record<string, string>;
  queryParams?: Record<string, string>;
  data: {
    file: File;
  };
}): Promise<{
  data: {
    s3_url: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.S3_ASSET_URL);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const formData = new FormData();
  formData.append('file', data.file);
  const response = (await fetchFormPostRequest({
    url: url.toString(),
    headers,
    body: formData,
  })) as Promise<{
    data: {
      s3_url: string;
    };
  }>;
  return response;
};

export const generateGoogleAdAssets = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: Partial<ICampaign>;
}): Promise<{
  data: IGoogleAdsData;
}> => {
  const url = new URL(API_ENDPOINTS.GOOGLE_AD_ASSETS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: IGoogleAdsData;
  }>;
  return response;
};

export const getGoogleMediaAssetsDetails = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IGoogleMediaAssetDetails[];
}> => {
  const url = new URL(API_ENDPOINTS.GOOGLE_MEDIA_ASSETS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IGoogleMediaAssetDetails[];
  }>;
  return response;
};

export const getGoogleLocationSuggestions = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IGoogleLocationDetails[];
}> => {
  const url = new URL(API_ENDPOINTS.GOOGLE_LOCATION_SEARCH);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IGoogleLocationDetails[];
  }>;
  return response;
};

export const launchGoogleCampaign = async ({
  headers,
  queryParams = {},
  data,
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
  data: {
    campaign_id: string;
    geo_locations?: IGoogleLocationDetails[];
  };
}): Promise<{
  data: {
    google_ads_data: IGoogleAdsData;
    order_id: string;
  };
}> => {
  const url = new URL(API_ENDPOINTS.GOOGLE_LANCH_CAMPAIGN);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchPostRequest({
    url: url.toString(),
    headers,
    body: data,
  })) as Promise<{
    data: {
      google_ads_data: IGoogleAdsData;
      order_id: string;
    };
  }>;
  return response;
};

export const getGoogleInsights = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: {
    campaign: IGoogleCampaignInsights[];
    keywords: IGoogleSearchKeywordsInsights[];
    locations: IGoogleSearchLocationsInsights[];
    calls: IGoogleCallsInsights[];
  };
}> => {
  const url = new URL(API_ENDPOINTS.GOOGLE_INSIGHTS_V2);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: {
      campaign: IGoogleCampaignInsights[];
      keywords: IGoogleSearchKeywordsInsights[];
      locations: IGoogleSearchLocationsInsights[];
      calls: IGoogleCallsInsights[];
    };
  }>;
  return response;
};

export const getGoogleLeads = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: IGoogleLead[];
}> => {
  const url = new URL(API_ENDPOINTS.GOOGLE_LEADS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: IGoogleLead[];
  }>;
  return response;
};

export const getMetaCreativesInsights = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<{
  data: ICampaignInsightDetails[];
}> => {
  const url = new URL(API_ENDPOINTS.META_CREATIVE_INSIGHTS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<{
    data: ICampaignInsightDetails[];
  }>;
  return response;
};
