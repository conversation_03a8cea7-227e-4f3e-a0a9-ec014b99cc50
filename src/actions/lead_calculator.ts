import { API_ENDPOINTS, fetchPostRequest } from './index';
import {
  ILeadCalculatorRequest,
  ILeadCalculatorResponse,
} from '../types/lead_calculator';

export const sendLeadCalculatorMessage = async ({
  sessionId,
  message,
  email,
}: {
  sessionId: string;
  message: string;
  email: string;
}): Promise<ILeadCalculatorResponse> => {
  const requestData: ILeadCalculatorRequest = {
    session_id: sessionId,
    message,
    email,
  };

  try {
    const response = await fetchPostRequest({
      url: API_ENDPOINTS.LEAD_CALCULATOR,
      headers: {
        'Content-Type': 'application/json',
      },
      body: requestData,
    });
    const data = response as ILeadCalculatorResponse;
    return data;
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};

export const generateSessionId = (): string => {
  return `${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
};
