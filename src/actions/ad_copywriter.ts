import { API_ENDPOINTS, fetchPostRequest } from './index';
import {
  IAdCopywriterRequest,
  IAdCopywriterResponse,
} from '../types/ad_copywriter';

export const sendAdCopywriterMessage = async ({
  sessionId,
  message,
  email,
}: {
  sessionId: string;
  message: string;
  email: string;
}): Promise<IAdCopywriterResponse> => {
  const requestData: IAdCopywriterRequest = {
    session_id: sessionId,
    message,
    email,
  };

  try {
    const response = await fetchPostRequest({
      url: API_ENDPOINTS.AD_COPYWRITER,
      headers: {
        'Content-Type': 'application/json',
      },
      body: requestData,
    });
    const data = response as IAdCopywriterResponse;
    return data;
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};

export const generateSessionId = (): string => {
  return `${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
};
