{"name": "groweasy-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000 & local-ssl-proxy --key ./ssl/dev.groweasy.ai-key.pem --cert ./ssl/dev.groweasy.ai.pem --source 443 --target 3000", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install"}, "dependencies": {"@sentry/nextjs": "^7.90.0", "@stripe/react-stripe-js": "^2.7.1", "@stripe/stripe-js": "^3.5.0", "classnames": "^2.3.2", "eslint-config-next": "13.5.3", "fetch-retry": "^6.0.0", "firebase": "^10.6.0", "lodash.debounce": "^4.0.8", "next": "^13.5.6", "react": "^18.2.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-markdown": "^10.1.0", "react-query": "^3.39.3", "recharts": "^2.12.2", "sharp": "^0.34.1"}, "devDependencies": {"@next/eslint-plugin-next": "^14.0.4", "@svgr/webpack": "^8.1.0", "@types/react": "18.2.38", "@typescript-eslint/eslint-plugin": "^6.13.2", "autoprefixer": "^10.4.21", "eslint": "8.50.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard-with-typescript": "^42.0.0", "eslint-plugin-n": "^16.3.1", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-promise": "^6.1.1", "husky": "^8.0.3", "lint-staged": "14.0.1", "local-ssl-proxy": "^2.0.5", "postcss": "^8.4.31", "prettier": "^3.1.0", "tailwindcss": "^3.4.17", "typescript": "5.3.2"}, "lint-staged": {"*.{tsx,jsx,js,ts}": "eslint --cache --fix", "*.{tsx,jsx,ts,js,css,md}": "prettier --write"}}