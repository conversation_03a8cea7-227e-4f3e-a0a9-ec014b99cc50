import ContactUsComp from '@/components/home_pages/groweasy_old/ContactUsComp';
import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import NavigationHeader from '@/components/lib/NavigationHeader';
import Head from 'next/head';
import { getMetaTags } from 'src/utils';

const privacyPolicy = [
  {
    title: '1. Information We Collect',
    content: [
      {
        subtitle: '1.1 Personal Information',
        text: 'We may collect personal information, including but not limited to names, email addresses, and contact details, when you use our services or interact with our website.',
      },
      {
        subtitle: '1.2 Non-Personal Information',
        text: 'We may also collect non-personal information such as device information, IP addresses, and usage data to improve our services and enhance your experience.',
      },
    ],
  },
  {
    title: '2. How We Use Your Information',
    content: [
      {
        subtitle: '2.1 Providing and Improving Our Services',
        text: 'We use collected information to provide, maintain, and improve our digital marketing services, including lead generation campaigns and user experience.',
      },
      {
        subtitle: '2.2 Communication',
        text: 'We may use your contact information to send you important updates, newsletters, and marketing communications. You can opt-out of these communications at any time.',
      },
    ],
  },
  {
    title: '3. Information Sharing',
    content: [
      {
        subtitle: '3.1 Third-Party Service Providers',
        text: 'We may share information with third-party service providers to assist us in delivering our services and enhancing our operations.',
      },
      {
        subtitle: '3.2 Legal Compliance',
        text: 'We may disclose your information to comply with legal obligations, enforce our policies, or respond to legal requests.',
      },
    ],
  },
  {
    title: '4. Data Security',
    content: [
      {
        subtitle: '',
        text: 'We take reasonable measures to protect your information from unauthorized access, disclosure, alteration, or destruction.',
      },
    ],
  },
  {
    title: '5. Your Choices and Rights',
    content: [
      {
        subtitle: '',
        text: 'You have the right to access, correct, or delete your personal information. You can also opt-out of certain communications. Contact us at [<EMAIL>] for assistance.',
      },
    ],
  },
  {
    title: '6. Changes to This Privacy Policy',
    content: [
      {
        subtitle: '',
        text: 'We may update this Privacy Policy periodically. The date of the latest update will be reflected at the top of the policy.',
      },
    ],
  },
  {
    title: '7. Contact Us',
    content: [
      {
        subtitle: '',
        text: 'If you have any questions, concerns, or requests regarding this Privacy Policy, please contact us at [<EMAIL>].',
      },
    ],
  },
];

const PrivacyPolicy = () => {
  return (
    <div>
      <Head>{getMetaTags('/privacy-policy')}</Head>
      <NavigationHeader />
      <div className="px-4 py-4 sm:px-36">
        <h1 className="mt-6 text-2xl font-medium">
          Privacy Policy for AUTOTME SOFTWARE PRIVATE LIMITED, a company
          registered in India, operates GrowEasy, an AI-enabled lead generation
          platform
        </h1>
        <p className="text-sm font-medium mt-3">Last Updated: 14 Dec, 2023</p>
        <p className="mt-5 text-base">
          Thank you for choosing GrowEasy (we, our, or us).
        </p>
        <p className="text-base">
          This Privacy Policy outlines how we collect, use, disclose, and
          protect your information when you use our services and website.
        </p>
        {privacyPolicy.map((item, index) => {
          return (
            <div key={index}>
              <h2 className="mt-8 text-xl">{item.title}</h2>
              {item.content.map((contentItem, innerIndex) => {
                return (
                  <div key={innerIndex} className="mt-4">
                    {contentItem.subtitle ? (
                      <h3 className="text-base">{contentItem.subtitle}</h3>
                    ) : null}
                    <p className="text-sm mt-2">{contentItem.text}</p>
                  </div>
                );
              })}
            </div>
          );
        })}
        <p className="mt-8 text-base">
          By using our services and website, you agree to the terms outlined in
          this Privacy Policy.
        </p>
        <ContactUsComp />
      </div>
      <FooterComp />
    </div>
  );
};

export default PrivacyPolicy;
