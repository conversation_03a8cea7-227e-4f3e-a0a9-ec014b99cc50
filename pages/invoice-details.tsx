import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { GrowEasyPartners, IGroweasyUser } from 'src/types';
import BackIcon from '@/images/common/back-arrow.svg';
import MobileContainer from '@/components/lib/MobileContainer';
import { useQuery } from 'react-query';
import { getInvoiceDetails } from 'src/actions/profile';
import { getCommonHeaders } from 'src/actions';
import {
  getFormattedDateString,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import Button from '@/components/lib/Button';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import Head from 'next/head';

interface IInvoiceDetailsProps {
  user?: IGroweasyUser;
}

const GROWEASY_DETAILS = [
  'AUTOTME SOFTWARE PRIVATE LIMITED',
  'GSTIN: 09ABBCA5230D1Z5',
  'Noida Special Economic Zone, Sector 3, Gautam Buddha Nagar',
  'Noida, 201301, Uttar Pradesh, India',
  'Phone: +************',
  'Email: <EMAIL>',
];

const AD_GLOBAL_AI_DETAILS = [
  'PT. BLUE EYED GLOBAL',
  'Company ID: NPWP16 : 0628 **************',
  'AXA Tower 36th Floor Unit 5-6, Kuningan City, Jl. Prof Dr. Satrio Kav.18, Kuningan',
  'Jakarta Selatan 12940, Indonesia',
  'Phone: +62 811-8880-5174',
  'Email: <EMAIL>',
];

const InvoiceDetails = (props: IInvoiceDetailsProps) => {
  const { user } = props;

  const router = useRouter();

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const invoiceDetailsResponse = useQuery(
    'getInvoiceDetails',
    () => {
      return getInvoiceDetails({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'InvoiceDetails.getInvoiceDetails',
        );
      },
    },
  );

  const onBackPressed = () => {
    router.back();
  };

  const invoiceDetails = invoiceDetailsResponse?.data?.data;

  const onInvoiceDownloadClick = () => {
    logEvent(EVENT_NAMES.download_invoice_clicked);
    window?.print();
  };

  const campaignDetails = invoiceDetails?.campaign_details;
  const billingDetails = invoiceDetails?.billing_details;
  const businessDetails = [
    billingDetails?.business_name ?? '',
    billingDetails?.business_tax_id
      ? `Tax ID: ${billingDetails?.business_tax_id}`
      : '',
    billingDetails?.business_address ?? '',
    [
      billingDetails?.business_city,
      billingDetails?.postal_code,
      billingDetails?.state_code,
      billingDetails?.country_code,
    ].join(', '),
    `Email/Mobile: ${
      billingDetails?.billing_email || billingDetails?.billing_mobile || 'NA'
    }`,
  ];

  return user ? (
    <MobileContainer>
      <Head>
        <title>GrowEasy-Invoice-{invoiceDetails?.id}</title>
      </Head>
      <div className="flex flex-col flex-1 w-full bg-white h-full">
        <div className="flex items-center mt-4">
          <div className="p-4 cursor-pointer" onClick={onBackPressed}>
            <BackIcon />
          </div>
          <p className="text-black text-lg">Invoice Details</p>
          <div className="flex-1" />
        </div>
        {invoiceDetails ? (
          <div className="mt-3 mb-3 flex flex-col px-4 flex-1 overflow-y-scroll h-full no-scrollbar">
            <div>
              <div className="flex items-center">
                <p className="text-base text-primary font-semibold overflow-hidden text-ellipsis mr-3">
                  {campaignDetails?.business_category}
                </p>
                <div className="flex-1" />
                <p>{invoiceDetails?.total_payable}</p>
              </div>
              <p className="text-xs text-gray-dark font-medium">
                {getFormattedDateString(new Date(campaignDetails?.start_time))}
                <span className="mx-2">-</span>
                {getFormattedDateString(new Date(campaignDetails?.end_time))}
              </p>
            </div>
            <p className="mt-5 text-xs">
              Invoice No: {invoiceDetails.invoice_no}
            </p>
            <p className="text-xs">
              Invoice Date:{' '}
              {getFormattedDateString(
                new Date(invoiceDetails.issue_date?._seconds * 1000),
              )}
            </p>
            <p className="mt-3 mb-1 text-xs text-gray-dark font-semibold">
              Billed By:
            </p>
            {(invoiceDetails.partner === GrowEasyPartners.AD_GLOBAL_AI
              ? AD_GLOBAL_AI_DETAILS
              : GROWEASY_DETAILS
            ).map((item, index) => {
              return (
                <p key={index} className="text-xs">
                  {item}
                </p>
              );
            })}
            <p className="mt-3 mb-1 text-xs text-gray-dark font-semibold">
              Billed To:
            </p>
            {businessDetails.map((item, index) => {
              return (
                <p key={index} className="text-xs">
                  {item}
                </p>
              );
            })}
            <div className="mt-5 w-full">
              <table className="w-full text-left">
                <thead>
                  <tr className="text-sm">
                    <th className="font-semibold text-gray-dark border border-gray-light py-1 px-2">
                      Item
                    </th>
                    <th className="border border-gray-light" />
                    <th className="font-semibold text-gray-dark border border-gray-light py-1 px-2">
                      Amount
                    </th>
                  </tr>
                </thead>
                <tbody className="text-sm">
                  {invoiceDetails?.line_items?.map((item, index) => {
                    return (
                      <tr key={index}>
                        <td className="border border-gray-light py-1 px-2">
                          {item.description}
                        </td>
                        <td className="border border-gray-light" />
                        <td className="border border-gray-light py-1 px-2">
                          {item.amount}
                        </td>
                      </tr>
                    );
                  })}
                  <tr className="mt-8 block" />
                  {invoiceDetails?.calculation_details?.map((item, index) => {
                    return (
                      <tr key={index}>
                        <td className="border border-gray-light" />
                        <td className="border border-gray-light py-1 px-2">
                          {item.key}
                        </td>
                        <td className="border border-gray-light py-1 px-2">
                          {item.value}
                        </td>
                      </tr>
                    );
                  })}
                  <tr>
                    <td className="border border-gray-light" />
                    <td className="border border-gray-light py-1 px-2 font-semibold">
                      Total Payable
                    </td>
                    <td className="border border-gray-light py-1 px-2 font-semibold">
                      {invoiceDetails.total_payable}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <SpinnerLoader />
          </div>
        )}
        <div className="px-4 w-full flex my-3">
          <Button
            onClick={onInvoiceDownloadClick}
            className="w-full hide-on-print"
          >
            <p>Download Invoice</p>
          </Button>
        </div>
      </div>
    </MobileContainer>
  ) : null;
};

export default InvoiceDetails;
