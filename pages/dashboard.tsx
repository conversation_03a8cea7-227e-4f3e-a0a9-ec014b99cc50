import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import MobileContainer from '@/components/lib/MobileContainer';
import { DASHBOARD_TABS, GLOBALS, QueryParams } from 'src/constants';
import Image from 'next/image';
import classNames from 'classnames';
import { useInfiniteQuery, useQuery } from 'react-query';
import { getAllFormLeads, getV2Campaigns } from 'src/actions/dashboard';
import { getCommonHeaders } from 'src/actions';
import FetchError from 'src/actions/FetchError';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import CampaignsList from '@/components/dashboard/campaigns/CampaignsList';
import LeadsList from '@/components/dashboard/leads/LeadsList';
import Link from 'next/link';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import { IMetaLead } from 'src/types/leads';
import UserProfileImage from '@/components/lib/UserProfileImage';
import { ILeadsCrmDetails } from 'src/types/leads_crm';
import EditLeadsCrmDetailsBs from '@/components/campaign_details/bottom_sheets/EditLeadsCrmDetailsBs';
import { getUserProfile } from 'src/actions/profile';
import Head from 'next/head';
import { ICampaign } from 'src/types/campaigns';
import ChooseCampaignTypeBs from '@/components/dashboard/ChooseCampaignTypeBs';

interface IDashboardProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

const Dashboard = (props: IDashboardProps) => {
  const { user, partnerConfig } = props;

  const [selectedTabId, setSelectedTabId] = useState(DASHBOARD_TABS[0].id);

  // for pagination in all-leads
  const [startAfterForAllLeads, setStartAfterForAllLeads] = useState(
    `${Math.floor(Date.now() / 1000)}`,
  );
  const [allLeads, setAllLeads] = useState<
    Array<IMetaLead /*| ICtwaLead*/ & { crm_details?: ILeadsCrmDetails }>
  >([]);
  const [noMoreFormLeadsAvailable, setNoMoreFormLeadsAvailable] =
    useState(false);
  /*const [noMoreCtwaLeadsAvailable, setNoMoreCtwaLeadsAvailable] =
    useState(false);*/
  const [selectedLeadsCrmDetails, setSelectedLeadsCrmDetails] =
    useState<Partial<ILeadsCrmDetails> | null>(null);

  const [campaignTypeSelectionBsVisible, setCampaignTypeSelectionBsVisible] =
    useState(false);

  const router = useRouter();

  const campaignsResponse = useInfiniteQuery(
    'getV2Campaigns',
    (params) => {
      return getV2Campaigns({
        headers: getCommonHeaders(user),
        queryParams: {
          ...(router.query as Record<string, string>),
          [QueryParams.LAST_CURSOR_ID]: (params.pageParam as string) ?? '',
          [QueryParams.LIMIT]: `20`,
        },
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      getNextPageParam: (lastPage) => {
        return lastPage.data?.last_cursor_id || undefined;
      },
      onError: (error: FetchError | Error) => {
        logApiErrorAndShowToastMessage(error, 'dashboard.getV2Campaigns');
      },
    },
  );

  const allFormLeadsResponse = useQuery(
    ['getAllFormLeads', startAfterForAllLeads],
    () => {
      return getAllFormLeads({
        headers: getCommonHeaders(user),
        queryParams: {
          ...router.query,
          [QueryParams.START_AFTER]: startAfterForAllLeads,
        } as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      onError: (error: FetchError | Error) => {
        logApiErrorAndShowToastMessage(error, 'dashboard.getAllFormLeads');
      },
      onSuccess: (response) => {
        const leads = response.data;
        if (leads?.length) {
          setAllLeads((existingAllLeads) => {
            return [...existingAllLeads, ...leads].sort(
              (a, b) => (b.created_time as number) - (a.created_time as number),
            );
          });
        } else {
          setNoMoreFormLeadsAvailable(true);
        }
      },
    },
  );

  /*const allCtwaLeadsResponse = useQuery(
    ['getAllCtwaLeads', startAfterForAllLeads],
    () => {
      return getAllCtwaLeads({
        headers: getCommonHeaders(user),
        queryParams: {
          ...router.query,
          [QueryParams.START_AFTER]: startAfterForAllLeads,
        } as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      onError: (error: FetchError | Error) => {
        logApiErrorAndShowToastMessage(error, 'dashboard.getAllCtwaLeads');
      },
      onSuccess: (response) => {
        const leads = response.data;
        if (leads?.length) {
          setAllLeads((existingAllLeads) => {
            return [...existingAllLeads, ...leads].sort(
              (a, b) => (b.created_time as number) - (a.created_time as number),
            );
          });
        } else {
          setNoMoreCtwaLeadsAvailable(true);
        }
      },
    },
  );*/

  useQuery(
    'getUserProfile',
    () => {
      return getUserProfile({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'dashboard.getUserProfile');
      },
      onSuccess: (response) => {
        if (response?.data) {
          GLOBALS.userProfile = response.data;
        }
        // new users when dropped from /edit-profile or when press back in flow (signup -> edit-profile -> back)
        if (!response?.data?.mobile_dial_code) {
          void router.push('/edit-profile?source=login');
        }
      },
    },
  );

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const renderTab = (tabId: string) => {
    const tab = DASHBOARD_TABS.find((item) => item.id === tabId);
    if (!tab) {
      return null;
    }
    const selected = tabId === selectedTabId;
    return (
      <div
        className="cursor-pointer flex-1"
        onClick={() => setSelectedTabId(tabId)}
      >
        <div
          className={classNames('h-1', {
            'bg-primary w-full': selected,
          })}
        />
        <div className="py-4 flex flex-col items-center">
          <div>
            <Image
              src={selected ? tab.selectedIconUrl : tab.iconUrl}
              width={tab.iconWidth}
              height={tab.iconHeight}
              alt=""
            />
          </div>
          <p
            className={classNames('mt-2 text-xs', {
              'text-primary font-semibold': selected,
              'text-gray-dark': !selected,
            })}
          >
            {tab.label}
          </p>
        </div>
      </div>
    );
  };

  const onCreateCampaignClick = () => {
    setCampaignTypeSelectionBsVisible(true);
  };

  // details will have campaign_id from child
  const onEditLeadsCrmClick = (details: Partial<ILeadsCrmDetails>) => {
    details.uid = user?.uid;
    setSelectedLeadsCrmDetails(details);
  };

  const onLeadsCrmDetailsUpdate = (
    updatedLeadsCrmDetails: ILeadsCrmDetails,
  ) => {
    // update crm_details in leads
    const allLeadsCopy = [...allLeads];
    for (let i = 0; i < allLeadsCopy.length; i++) {
      if (allLeadsCopy[i].id === updatedLeadsCrmDetails.leadgen_id) {
        allLeadsCopy[i].crm_details = updatedLeadsCrmDetails;
        break;
      }
    }
    setAllLeads(allLeadsCopy);
  };

  const onNextLeadsPress = () => {
    const lastLeadInList = allLeads[allLeads.length - 1];
    if (lastLeadInList) {
      setStartAfterForAllLeads(`${lastLeadInList.created_time}`);
    }
  };

  const campaigns: ICampaign[] = [];
  campaignsResponse.data?.pages?.forEach((item) => {
    campaigns.push(...item.data.campaigns);
  });

  return user ? (
    <MobileContainer>
      <Head>
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      <div className="flex flex-col flex-1 h-full w-full pt-4">
        <div className="flex items-center px-5">
          <Link
            className="text-black text-xl font-semibold tracking-tight"
            href="/"
          >
            {partnerConfig?.name ?? 'GrowEasy'}
          </Link>
          <div className="flex-1" />
          <div className="p-3">
            <Link href="/profile">
              <UserProfileImage user={user} width={24} height={24} />
            </Link>
          </div>
        </div>
        <div className="flex-1 overflow-y-scroll flex flex-col no-scrollbar px-5">
          {selectedTabId === DASHBOARD_TABS[0].id ? (
            <div className="flex flex-col flex-1 mt-6">
              {campaignsResponse.isLoading ? (
                <div className="flex justify-center">
                  <SpinnerLoader />
                </div>
              ) : (
                <>
                  <CampaignsList
                    data={campaigns}
                    onCreateCampaignClick={onCreateCampaignClick}
                  />
                  {campaignsResponse.hasNextPage ? (
                    <div className="my-3">
                      {campaignsResponse.isFetching ? (
                        <div className="flex justify-center">
                          <SpinnerLoader borderWidth={2} size={20} />
                        </div>
                      ) : (
                        <p
                          className="text-sm text-center text-hyperlink cursor-pointer"
                          onClick={() => {
                            void campaignsResponse.fetchNextPage();
                          }}
                        >
                          Load More
                        </p>
                      )}
                    </div>
                  ) : null}
                </>
              )}
            </div>
          ) : null}
          {selectedTabId === DASHBOARD_TABS[1].id ? (
            <div className="flex flex-col flex-1 items-center mt-10">
              <LeadsList
                data={allLeads}
                onNextPress={onNextLeadsPress}
                noMoreLeadsAvailable={
                  noMoreFormLeadsAvailable //&& noMoreCtwaLeadsAvailable
                }
                loading={
                  allFormLeadsResponse.isFetching
                  // || allCtwaLeadsResponse.isFetching
                }
                campaigns={campaigns}
                onEditLeadsCrmClick={onEditLeadsCrmClick}
                partnerConfig={partnerConfig}
              />
            </div>
          ) : null}
        </div>
        <div className="w-full flex items-center bg-white mt-3">
          {renderTab(DASHBOARD_TABS[0].id)}
          <div
            className="flex-1 flex flex-col items-center cursor-pointer"
            onClick={onCreateCampaignClick}
          >
            <div className="w-12 h-12 rounded-full flex items-center justify-center bg-primary -mt-6">
              <Image
                src="/images/dashboard/add.png"
                width="20"
                height="20"
                alt=""
              />
            </div>
            <p className="text-xs text-gray-dark mt-2">New Campaign</p>
          </div>
          {renderTab(DASHBOARD_TABS[1].id)}
        </div>
        {selectedLeadsCrmDetails ? (
          <EditLeadsCrmDetailsBs
            leadsCrmDetails={selectedLeadsCrmDetails}
            onClose={() => setSelectedLeadsCrmDetails(null)}
            user={user}
            onUpdateDone={onLeadsCrmDetailsUpdate}
          />
        ) : null}
        {campaignTypeSelectionBsVisible ? (
          <ChooseCampaignTypeBs
            onClose={() => setCampaignTypeSelectionBsVisible(false)}
            partnerConfig={partnerConfig}
            user={user}
          />
        ) : null}
      </div>
    </MobileContainer>
  ) : null;
};

export default Dashboard;
