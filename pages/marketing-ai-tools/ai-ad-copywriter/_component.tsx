'use client';

import { FaEdit } from 'react-icons/fa';
import { useMutation } from 'react-query';
import {
  sendAdCopywriterMessage,
  generateSessionId,
} from '../../../src/actions/ad_copywriter';
import {
  IAdCopywriterSession,
  IAdCopy,
} from '../../../src/types/ad_copywriter';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import AiToolChatInterface, {
  BaseMessage,
} from '../../../src/components/ai-tools/AiToolChatInterface';
import { useAiToolChat } from '../../../src/components/ai-tools/useAiToolChat';
import { AdCopyDisplay } from '../../../src/components/ai-tools/AdCopyDisplay';
import Markdown from 'react-markdown';

interface AdCopywriterMessage extends BaseMessage {
  adCopies?: IAdCopy[];
}

export default function AiAdCopywriter() {
  const {
    chatStarted,
    setChatStarted,
    messages,
    setMessages,
    currentInput,
    setCurrentInput,
    showEmailModal,
    setShowEmailModal,
    session,
    setSession,
  } = useAiToolChat<AdCopywriterMessage, IAdCopywriterSession>();

  const sendMessageMutation = useMutation(sendAdCopywriterMessage, {
    onError: (error: Error) => {
      logApiErrorAndShowToastMessage(
        error,
        'AiAdCopywriter.sendMessageMutation',
      );
    },
  });

  const handleEmailSubmit = (email: string) => {
    const sessionId = generateSessionId();
    const newSession: IAdCopywriterSession = {
      sessionId,
      email,
      conversationData: {},
    };
    setSession(newSession);
    setShowEmailModal(false);
    setChatStarted(true);

    sendMessageMutation.mutate(
      {
        sessionId,
        message:
          "Hi! Ready to create compelling ad copy? Tell me about your business and what you're advertising.",
        email,
      },
      {
        onSuccess: (response) => {
          const botMessage: AdCopywriterMessage = {
            id: 'bot-1',
            role: 'assistant',
            content: response.data.message,
            apiResponse: true,
          };
          setMessages([botMessage]);
        },
      },
    );
  };

  const handleSendMessage = (content: string) => {
    if (!content.trim()) return;

    if (!chatStarted && !session) {
      setShowEmailModal(true);
      return;
    }

    const userMessage: AdCopywriterMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: content,
    };

    setMessages((prev) => [...prev, userMessage]);
    setCurrentInput('');

    const loadingMessage: AdCopywriterMessage = {
      id: `loading-${Date.now()}`,
      role: 'assistant',
      content: 'Thinking...',
      loading: true,
    };
    setMessages((prev) => [...prev, loadingMessage]);

    if (session) {
      sendMessageMutation.mutate(
        {
          sessionId: session.sessionId,
          message: content,
          email: session.email,
        },
        {
          onSuccess: (response) => {
            setMessages((prev) => prev.filter((msg) => !msg.loading));

            const botMessage: AdCopywriterMessage = {
              id: Date.now().toString(),
              role: 'assistant',
              content: response.data.message,
              apiResponse: true,
            };
            setMessages((prev) => [...prev, botMessage]);

            if (
              response?.data?.ad_copies &&
              response?.data?.ad_copies?.ad_copies?.length > 0
            ) {
              const adCopiesMessage: AdCopywriterMessage = {
                id: (Date.now() + 1).toString(),
                role: 'assistant',
                content: 'Generated Ad Copies',
                adCopies: response.data.ad_copies.ad_copies,
              };
              setMessages((prev) => [...prev, adCopiesMessage]);
            }
          },
        },
      );
    }
  };

  const renderMessageContent = (message: AdCopywriterMessage) => {
    if (message.loading) {
      return (
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
          <span>{message.content}</span>
        </div>
      );
    }

    if (message.adCopies && message.adCopies.length > 0) {
      return <AdCopyDisplay adCopies={message.adCopies} />;
    }

    return (
      <div className="whitespace-pre-line leading-tight md:leading-tight">
        <Markdown>{message.content}</Markdown>
      </div>
    );
  };

  return (
    <AiToolChatInterface
      toolName="AI Ad Copywriter"
      toolIcon={<FaEdit className="w-4 h-4 sm:w-5 sm:h-5 text-white" />}
      toolDescription="Generate compelling ad copy with AI"
      welcomeTitle="Welcome! Ready to create compelling ad copy?"
      welcomeDescription="which platform are you running ads on – Facebook, Instagram, or Google?"
      messages={messages}
      setMessages={setMessages}
      currentInput={currentInput}
      setCurrentInput={setCurrentInput}
      chatStarted={chatStarted}
      setChatStarted={setChatStarted}
      session={session}
      setSession={setSession}
      showEmailModal={showEmailModal}
      setShowEmailModal={setShowEmailModal}
      sendMessageMutation={sendMessageMutation}
      onEmailSubmit={handleEmailSubmit}
      onSendMessage={handleSendMessage}
      renderMessageContent={renderMessageContent}
    />
  );
}
