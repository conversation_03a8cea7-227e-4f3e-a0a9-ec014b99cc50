'use client';

import { FaCalculator } from 'react-icons/fa';
import { useMutation } from 'react-query';
import {
  sendLeadCalculatorMessage,
  generateSessionId,
} from '../../../src/actions/lead_calculator';
import { ILeadCalculatorSession } from '../../../src/types/lead_calculator';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import AiToolChatInterface, {
  BaseMessage,
} from '../../../src/components/ai-tools/AiToolChatInterface';
import { useAiToolChat } from '../../../src/components/ai-tools/useAiToolChat';
import { LeadCostResultDisplay } from '../../../src/components/ai-tools/LeadCostResultDisplay';
import Markdown from 'react-markdown';

interface LeadCalculatorMessage extends BaseMessage {
  result?: {
    costRange: string;
    industry: string;
    city: string;
    channel: string;
    productPrice: string;
  };
}

export default function LeadCostCalculator() {
  const {
    chatStarted,
    setChatStarted,
    messages,
    setMessages,
    currentInput,
    setCurrentInput,
    showEmailModal,
    setShowEmailModal,
    session,
    setSession,
  } = useAiToolChat<LeadCalculatorMessage, ILeadCalculatorSession>();

  const sendMessageMutation = useMutation(sendLeadCalculatorMessage, {
    onError: (error: Error) => {
      logApiErrorAndShowToastMessage(
        error,
        'LeadCostCalculator.sendMessageMutation',
      );
    },
  });

  const handleEmailSubmit = (email: string) => {
    const sessionId = generateSessionId();
    const newSession: ILeadCalculatorSession = {
      sessionId,
      email,
      conversationData: {},
    };
    setSession(newSession);
    setShowEmailModal(false);
    setChatStarted(true);

    sendMessageMutation.mutate(
      {
        sessionId,
        message:
          "Hi! Ready to estimate your lead cost? What's your business industry/product?",
        email,
      },
      {
        onSuccess: (response) => {
          const botMessage: LeadCalculatorMessage = {
            id: 'bot-1',
            role: 'assistant',
            content: response.data.message,
            apiResponse: true,
          };
          setMessages([botMessage]);
        },
      },
    );
  };

  const handleSendMessage = (content: string) => {
    if (!content.trim()) return;

    if (!chatStarted && !session) {
      setShowEmailModal(true);
      return;
    }

    const userMessage: LeadCalculatorMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: content,
    };

    setMessages((prev) => [...prev, userMessage]);
    setCurrentInput('');

    const loadingMessage: LeadCalculatorMessage = {
      id: `loading-${Date.now()}`,
      role: 'assistant',
      content: 'Thinking...',
      loading: true,
    };
    setMessages((prev) => [...prev, loadingMessage]);

    if (session) {
      sendMessageMutation.mutate(
        {
          sessionId: session.sessionId,
          message: content,
          email: session.email,
        },
        {
          onSuccess: (response) => {
            setMessages((prev) => prev.filter((msg) => !msg.loading));
            const botMessage: LeadCalculatorMessage = {
              id: Date.now().toString(),
              role: 'assistant',
              content: response.data.message,
              apiResponse: true,
            };
            setMessages((prev) => [...prev, botMessage]);

            if (
              response?.data?.cpl_data &&
              Object.keys(response?.data?.cpl_data).length > 0
            ) {
              const currencySymbol =
                response?.data?.cpl_data?.currency === 'USD' ? '$' : '₹';
              const resultMessage: LeadCalculatorMessage = {
                id: (Date.now() + 1).toString(),
                role: 'assistant',
                content: 'Cost Per Lead Analysis',
                result: {
                  costRange: `${currencySymbol}${response?.data?.cpl_data?.cpl_range_lower} - ${currencySymbol}${response?.data?.cpl_data?.cpl_range_upper}`,
                  industry: response?.data?.cpl_data?.parameters.industry,
                  city: response?.data?.cpl_data?.parameters.city_category,
                  channel: response?.data?.cpl_data?.parameters.channel,
                  productPrice:
                    response?.data?.cpl_data?.parameters.price_range,
                },
              };
              setMessages((prev) => [...prev, resultMessage]);
            }
          },
        },
      );
    }
  };

  const renderMessageContent = (message: LeadCalculatorMessage) => {
    if (message.loading) {
      return (
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
          <span>{message.content}</span>
        </div>
      );
    }

    if (message.result) {
      return <LeadCostResultDisplay result={message.result} />;
    }

    return (
      <div className="whitespace-pre-line leading-tight md:leading-tight">
        <Markdown>{message.content}</Markdown>
      </div>
    );
  };

  return (
    <AiToolChatInterface
      toolName="Lead Cost Calculator"
      toolIcon={<FaCalculator className="w-4 h-4 sm:w-5 sm:h-5 text-white" />}
      toolDescription="Calculate your cost per lead instantly"
      welcomeTitle="Welcome! Ready to estimate your lead cost?"
      welcomeDescription="What's your business industry/product?"
      messages={messages}
      setMessages={setMessages}
      currentInput={currentInput}
      setCurrentInput={setCurrentInput}
      chatStarted={chatStarted}
      setChatStarted={setChatStarted}
      session={session}
      setSession={setSession}
      showEmailModal={showEmailModal}
      setShowEmailModal={setShowEmailModal}
      sendMessageMutation={sendMessageMutation}
      onEmailSubmit={handleEmailSubmit}
      onSendMessage={handleSendMessage}
      renderMessageContent={renderMessageContent}
    />
  );
}
