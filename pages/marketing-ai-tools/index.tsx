import { useState } from 'react';
import Head from 'next/head';
import {
  FaChevronRight,
  FaChevronDown,
  FaStar,
  FaCheckCircle,
  FaChartBar,
  FaBullseye,
  FaFileAlt,
  FaImage,
  FaEdit,
  FaSearch,
  FaDesktop,
  FaVideo,
  FaUsers,
} from 'react-icons/fa';
import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import NavigationHeader from '@/components/lib/NavigationHeader';
import Link from 'next/link';

// Define types
interface Agent {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  features: string[];
  color: string;
  link?: string;
  available: boolean;
}

interface FAQ {
  question: string;
  answer: string;
}

interface Testimonial {
  text: string;
  author: string;
  role: string;
  rating: number;
  image: string;
}

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
}

// Custom Button component
const Button = ({
  children,
  className = '',
  size = 'md',
  ...props
}: ButtonProps) => {
  const sizeClasses = {
    sm: 'py-1 px-3 text-sm',
    md: 'py-2 px-4 text-base',
    lg: 'py-3 px-6 text-lg',
  };

  return (
    <button
      className={`rounded-full font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${sizeClasses[size]} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};

// Custom Card components
const Card = ({ children, className = '', ...props }: CardProps) => {
  return (
    <div
      className={`rounded-lg border bg-white shadow-sm ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};

const CardContent = ({ children, className = '', ...props }: CardProps) => {
  return (
    <div className={`p-6 ${className}`} {...props}>
      {children}
    </div>
  );
};

const MarketingAiAgents = () => {
  const [openFaq, setOpenFaq] = useState<number | null>(0);

  const agents: Agent[] = [
    // Available tools with links (sorted first)
    {
      icon: FaImage,
      title: 'AI Ad Creative Generator',
      features: [
        'Design banners for Google Ads, Facebook, Instagram, and more',
        'Instantly create on-brand banners with AI',
        'Use ready-made templates or customize your own',
        'Download banners in multiple formats (JPG, PNG, GIF)',
      ],
      color: 'bg-teal-700',
      link: 'https://designeasy.ai',
      available: true,
    },
    {
      icon: FaSearch,
      title: 'AI Ad Keyword Planner',
      features: [
        'Find high-volume, low-competition keywords for SEO and PPC',
        'Analyze keyword trends, CPC, and monthly search volume',
        'Discover keyword gaps in competitor strategies',
        'Power up Google Ads and SEO campaigns with data-driven keywords',
      ],
      color: 'bg-teal-700',
      link: '/tools/google-keyword-ideas-generator',
      available: true,
    },
    {
      icon: FaVideo,
      title: 'AI Ad Video Script Generator',
      features: [
        'Instantly create ad scripts for YouTube, Facebook, and Instagram',
        'Customize script tone, length, and calls-to-action',
        'Keep brand voice consistent across all video ads',
        'Save time with ready-made, high-converting video scripts',
      ],
      color: 'bg-teal-700',
      link: '/order-social-media-videos-ads',
      available: true,
    },
    {
      icon: FaUsers,
      title: 'Meta Ad Audience Creator',
      features: [
        'Discover high-ROI audiences with AI',
        'Build custom and lookalike audiences for better targeting',
        'Get actionable audience suggestions to boost ad performance',
        'Export audience lists for direct use in Meta Ads',
      ],
      color: 'bg-teal-700',
      link: '/tools/facebook-ads-audience-builder',
      available: true,
    },
    {
      icon: FaBullseye,
      title: 'AI Lead Cost Calculator',
      features: [
        'Calculate your exact cost per lead quickly and easily.',
        'Compare lead costs across different marketing channels.',
        'Optimize campaigns to lower your cost per lead.',
        'Make data-driven decisions to boost lead generation ROI.',
      ],
      color: 'bg-teal-700',
      available: true,
      link: '/marketing-ai-tools/lead-cost-calculator',
    },
    {
      icon: FaFileAlt,
      title: 'AI Ads Library',
      features: [
        'Browse top-performing ad creatives for inspiration',
        'Filter ads by industry, platform (Google, Facebook, Instagram), or format',
        'Find winning ad templates for your campaigns',
        'Track the latest ad trends with AI-powered analysis',
      ],
      color: 'bg-teal-700',
      available: false,
    },
    {
      icon: FaChartBar,
      title: 'Ad Account Insights',
      features: [
        'Track ad performance across Google, Facebook, Instagram, and more',
        'Instantly see top-performing ad campaigns and best ads',
        'Use AI insights to reduce wasted ad spend and improve results',
        'Optimize ad budget to increase ROI and conversions',
      ],
      color: 'bg-teal-700',
      available: false,
    },
    {
      icon: FaEdit,
      title: 'AI Ad Copywriter',
      features: [
        'Generate high-converting ad copy in seconds',
        'Customize tone and message for your target audience',
        'Boost ad engagement with strong calls-to-action (CTAs)',
        'Save time with ready-to-use ad copy variations',
      ],
      color: 'bg-teal-700',
      available: true,
      link: '/marketing-ai-tools/ai-ad-copywriter',
    },
    {
      icon: FaDesktop,
      title: 'AI Ad Landing Page Builder',
      features: [
        'Drag-and-drop builder with AI-powered design suggestions',
        'Create mobile-friendly, conversion-focused landing pages',
        'Use proven templates or build from scratch',
        'Integrate with analytics, forms, and CRM tools',
      ],
      color: 'bg-teal-700',
      available: false,
    },
  ];

  const faqs: FAQ[] = [
    {
      question: 'What is AI digital marketing?',
      answer:
        'AI digital marketing uses artificial intelligence in digital marketing to automate and optimize tasks—like writing ads, designing ads, and analyzing performance—for better results, faster turnaround, and less manual effort.',
    },
    {
      question:
        'How do I get started with AI for digital marketing on GrowEasy?',
      answer:
        'Simply choose from our suite of AI agents, select the one that matches your needs, and start using it instantly with no signup required. Each agent is designed to be intuitive and user-friendly.',
    },
    {
      question: 'What tasks can your AI agents handle?',
      answer:
        'Our AI agents can handle ad creation, copywriting, keyword research, audience targeting, banner design, performance analysis, landing page building, and much more across all major digital marketing platforms.',
    },
    {
      question: 'Are these AI agents really free?',
      answer:
        'Yes! All our AI agents are completely free to use. There are no hidden costs, subscription fees, or credit card requirements to get started.',
    },
    {
      question: 'Do I need technical skills to use AI in digital marketing?',
      answer:
        'Not at all! Our AI agents are designed to be user-friendly and require no technical expertise. Simply input your requirements and let the AI do the heavy lifting.',
    },
    {
      question: 'How does GrowEasy ensure data security?',
      answer:
        'We implement enterprise-grade security measures including data encryption, secure servers, and strict privacy policies to ensure your marketing data and business information remain completely secure.',
    },
  ];

  const testimonials: Testimonial[] = [
    {
      text: 'Launching a lead generation campaign has never been easier. With the AI agents, I set up my ads in minutes, and the platform helped me achieve a higher return on ad spend than ever before.',
      author: 'Dipankar',
      role: 'CEO, Zendot',
      rating: 5,
      image: 'images/testimonials-people/dipankar.jpg',
    },
    {
      text: 'Using AI agents, we saw an immediate boost in campaign efficiency across all our platforms. The automation handled everything seamlessly and delivered better results with less manual effort.',
      author: 'Yash Chaudhary',
      role: 'Founder, Flabs',
      rating: 5,
      image: 'images/testimonials-people/yash.jpg',
    },
    {
      text: 'As an agency, we needed a tool that could scale with us, and the AI agents delivered. They automated repetitive tasks and provided actionable insights, helping our clients achieve outstanding results effortlessly.',
      author: 'Akshat',
      role: 'Founder, Pagaar.ai',
      rating: 5,
      image: 'images/testimonials-people/akshat.jpg',
    },
    {
      text: 'AI agents transformed the way we approach digital marketing. They took the guesswork out of optimization and let us focus on scaling. Within days, our leads skyrocketed!',
      author: 'Dhruvin',
      role: 'Sr Manager, Aprinklr',
      rating: 5,
      image: 'images/testimonials-people/dhruvin.jpg',
    },
  ];

  const TestimonialCard = ({ testimonial }: { testimonial: Testimonial }) => (
    <div className="flex-1 bg-gradient-to-tr from-primary to-primary2 shadow hover:shadow-lg hover:-translate-y-1 cursor-pointer transition-all duration-300 rounded-2xl p-6 text-white relative overflow-hidden">
      <div className="flex items-center justify-between mb-4">
        <div className="text-orange-400">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="40"
            height="40"
            viewBox="0 0 50 34"
            fill="currentColor"
          >
            <path d="M4.439 31.066C1.55 28.426 0 25.466 0 20.667 0 12.222 6.89 4.653 16.909.911l2.504 3.325C10.06 8.589 8.233 14.237 7.503 17.798c1.507-.67 3.478-.904 5.41-.75 5.058.403 9.046 3.976 9.046 8.418 0 2.24-1.034 4.388-2.875 5.972-1.84 1.583-4.336 2.473-6.94 2.473a12.4 12.4 0 0 1-4.184-.756 11 11 0 0 1-3.521-2.089m28.041 0c-2.888-2.64-4.439-5.6-4.439-10.399 0-8.445 6.89-16.014 16.909-19.756l2.504 3.325c-9.352 4.353-11.18 10.001-11.91 13.562 1.507-.67 3.478-.904 5.41-.75 5.059.403 9.046 3.976 9.046 8.418 0 2.24-1.034 4.388-2.875 5.972-1.84 1.583-4.336 2.473-6.94 2.473a12.4 12.4 0 0 1-4.184-.756 11 11 0 0 1-3.521-2.089" />
          </svg>
        </div>
        <div className="flex space-x-1">
          {Array.from({ length: testimonial.rating }).map((_, i) => (
            <FaStar
              key={i}
              className="h-5 w-5"
              style={{ color: '#C0CA02', fill: '#C0CA02' }}
            />
          ))}
        </div>
      </div>

      <img
        src="/images/groweasy-logo-square.svg"
        alt="GrowEasy watermark"
        className="absolute bottom-0 right-16 opacity-5 z-0 scale-150"
      />

      <div className="mb-14 z-10 relative">
        <p className="text-base md:leading-snug">{testimonial.text}</p>
      </div>

      <div className="flex items-center gap-4">
        <img
          src={`/${testimonial.image}`}
          alt={testimonial.author}
          className="w-16 h-16 rounded-full object-cover"
        />
        <div className="flex flex-col">
          <h2 className="text-white font-semibold text-lg md:text-xl">
            {testimonial.author}
          </h2>
          <p className="text-sm text-teal-200">{testimonial.role}</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="overflow-x-hidden">
      {/* SEO Meta Tags */}
      <Head>
        <title>
          AI Marketing Tools for Google Ads, Meta & Digital Campaigns |
          GrowEasy.ai
        </title>
        <meta
          name="title"
          content="AI Marketing Tools for Google Ads, Meta & Digital Campaigns | GrowEasy.ai"
        />
        <meta
          name="description"
          content="Boost your digital marketing with AI: Generate ad creatives, analyze lead cost, explore ads library, optimize campaigns, and more. Try GrowEasy.ai's free AI marketing agents for Google Ads, Facebook, Instagram, and Meta. No signup needed."
        />
      </Head>

      {/* Fixed Navigation Header */}
      <div className="fixed top-0 left-0 right-0 z-50">
        <NavigationHeader
          navlinks={[
            {
              label: 'Home',
              href: '/',
            },
            {
              label: 'About Us',
              href: '/about-us',
            },
          ]}
        />
      </div>

      {/* Main Content with top padding */}
      <div className="pt-10">
        <section className="relative pt-12 pb-6 sm:pb-10 md:pb-16 bg-gradient-to-br from-gray-50 to-white overflow-hidden">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-8">
                <div className="inline-flex items-center space-x-2 bg-orange-100 text-orange-700 px-4 py-2 rounded-full text-sm font-medium">
                  <FaStar className="h-4 w-4" />
                  <span>Automate Your Growth with AI in Digital Marketing</span>
                </div>

                <div className="space-y-6">
                  <h1 className="text-5xl lg:text-6xl font-bold text-teal-700 leading-tight">
                    Free AI Tools For
                    <span className="text-teal-700 block">
                      Digital Marketing
                    </span>
                  </h1>

                  <p className="text-xl text-gray-700 leading-relaxed">
                    Discover how{' '}
                    <strong>
                      artificial intelligence and digital marketing
                    </strong>{' '}
                    come together at GrowEasy. Our suite of free AI agents for
                    digital marketing empowers you to:
                  </p>

                  <div className="space-y-4">
                    {[
                      'Design high converting banners in seconds.',
                      'Generate ad copy in seconds.',
                      'Find keywords that convert.',
                      'Write viral video scripts without a writer.',
                    ].map((item, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <FaCheckCircle className="h-5 w-5 text-orange-500 flex-shrink-0" />
                        <span className="text-gray-700">{item}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Image Section */}
              <div className="relative h-[400px] lg:h-[700px]">
                <div className="absolute w-full lg:w-[613px] h-[350px] lg:h-[562px] top-0 lg:top-[80px] left-0">
                  <img
                    src="images/marketing-ai-tools/new-marketing-banner.png"
                    alt="Digital Marketing AI"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* AI Tools Grid */}
        <section id="agents" className="pt-0 sm:pt-4 pb-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-600 mb-4">
                Free AI Marketing{' '}
                <span className="text-teal-700">Tools for 2025</span>
              </h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Choose the right tool for your next marketing breakthrough.
                Click on any <br className="hidden sm:block" />
                agent to learn more or try it instantly -
                <strong> no signup required</strong>.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 px-4 sm:px-0">
              {agents.map((agent, index) => (
                <Card
                  key={index}
                  className="group hover:shadow-xl transition-all duration-300 border-gray-200 hover:border-teal-200 h-full flex flex-col"
                >
                  <CardContent className="p-6 flex flex-col h-full">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className={`${agent.color} p-3 rounded-lg`}>
                        <agent.icon className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900">
                        {agent.title}
                      </h3>
                    </div>

                    <div className="space-y-4 mb-6 flex-grow">
                      {agent.features.map((feature, featureIndex) => (
                        <div
                          key={featureIndex}
                          className="flex items-start space-x-3"
                        >
                          <FaCheckCircle className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700 text-sm leading-tight">
                            {feature}
                          </span>
                        </div>
                      ))}
                    </div>
                    <div className="flex items-center justify-center">
                      {agent.available ? (
                        <Link
                          href={agent.link || '#'}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex"
                        >
                          <Button className="bg-orange-500 hover:bg-orange-700 text-white text-lg w-[281px] h-[50px] rounded-full font-medium inline-flex items-center justify-center">
                            Try Now
                            <FaChevronRight className="ml-2 h-4 w-4 align-middle" />
                          </Button>
                        </Link>
                      ) : (
                        <Button className="bg-gray-400 text-white text-lg w-[281px] h-[50px] rounded-full font-medium inline-flex items-center justify-center cursor-not-allowed">
                          Coming soon
                          <FaChevronRight className="ml-2 h-4 w-4 align-middle" />
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
        {/* How It Works */}
        <section id="how-it-works" className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-600 mb-4">
                How It <span className="text-teal-700">Works</span>
              </h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Get started with our AI agents in just three simple steps
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  step: '1',
                  title: 'Start a Chat',
                  description:
                    'Begin by chatting with our AI assistant. Simply select the tool you want to use and start the conversation.',
                },
                {
                  step: '2',
                  title: 'Share Your Details',
                  description:
                    'Tell the AI your goals, audience, keywords, and preferences. The chat interface will guide you with prompts to customize your request.',
                },
                {
                  step: '3',
                  title: 'Get & Customize Your Output',
                  description:
                    'Instantly receive AI-generated results. You can review, edit, and further personalize the output directly in the chat before using it.',
                },
              ].map((item, index) => (
                <div
                  key={index}
                  className="text-center group p-6 border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-white"
                >
                  <div className="bg-orange-500 text-white w-16 h-16 rounded-2xl flex items-center justify-center text-2xl font-bold mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    {item.step}
                  </div>
                  <h3 className="text-2xm font-bold text-gray-700 mb-4">
                    {item.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>
        {/* new testimonials  */}
        <section id="testimonials" className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-5xl font-bold text-gray-900 mb-4">
                Testimonials
              </h2>
            </div>

            <div className="grid grid-cols-1 px-4 max-w-7xl mx-auto sm:grid-cols-2 gap-6">
              {testimonials.map((testimonial, index) => (
                <TestimonialCard key={index} testimonial={testimonial} />
              ))}
            </div>
          </div>
        </section>
        {/* FAQ Section */}
        <section id="faq" className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-700 mb-4">
                Frequently{' '}
                <span className="text-teal-700">Asked Questions</span>
              </h2>
              <p className="text-xl text-gray-700">
                Everything you need to know about our AI marketing agents
              </p>
            </div>

            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <div
                  key={index}
                  className="rounded-[22px] bg-teal-700/10 shadow-md px-[26.67px] py-[26.67px] transition-all duration-300 overflow-hidden w-full max-w-[1280px] mx-auto"
                >
                  <button
                    className="w-full text-left flex items-center justify-between group"
                    onClick={() => setOpenFaq(openFaq === index ? null : index)}
                  >
                    <span className="text-lg font-semibold text-gray-900 group-hover:text-teal-700 transition-colors duration-200">
                      {faq.question}
                    </span>
                    <FaChevronDown
                      className={`h-5 w-5 text-teal-700 transition-transform duration-300 ${
                        openFaq === index ? 'rotate-180' : ''
                      }`}
                    />
                  </button>

                  {openFaq === index && (
                    <div className="mt-4">
                      <p className="text-gray-800 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </section>
        {/* Final CTA Section with Correct Background Color */}
        <section className="py-10 overflow-hidden bg-[#1C4D48] text-white relative mt-10 mb-20 group max-w-[1400px] mx-auto px-4 md:px-8 md:pt-24 rounded-3xl">
          {/* Optional background blur effects (remove if not needed) */}
          <div className="absolute top-60 -left-40 blur-[100px] h-28 w-60 bg-teal-400/60"></div>
          <div className="absolute top-60 -right-40 blur-[100px] h-28 w-60 bg-teal-400/60"></div>

          {/* Animated Rocket Icon (SVG directly embedded) */}
          <div className="top-6 -right-0 md:-top-100 -translate-x-full translate-y-full transition-all duration-1000 group-hover:translate-x-0 group-hover:translate-y-0 absolute w-[180px] h-[190px] md:w-[320px] md:h-[320px] flex items-center justify-center z-0">
            <svg
              width="378"
              height="301"
              viewBox="0 0 378 301"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M68.6315 151.483L105.303 167.062C109.691 158.25 114.236 149.752 118.938 141.569C123.639 133.387 128.811 125.204 134.453 117.021L108.124 111.828L68.6315 151.483ZM135.393 190.666L188.99 244.012C202.154 238.976 216.259 231.266 231.304 220.88C246.349 210.494 260.453 198.692 273.617 185.473C295.558 163.443 312.718 138.973 325.099 112.064C337.48 85.1554 342.886 60.371 341.319 37.7109C318.752 36.1373 293.991 41.5663 267.035 53.9978C240.08 66.4294 215.632 83.6605 193.692 105.691C180.527 118.909 168.774 133.072 158.43 148.179C148.087 163.285 140.408 177.448 135.393 190.666ZM219.08 159.981C211.871 152.742 208.266 143.851 208.266 133.308C208.266 122.765 211.871 113.874 219.08 106.635C226.289 99.3966 235.222 95.7773 245.878 95.7773C256.535 95.7773 265.468 99.3966 272.677 106.635C279.886 113.874 283.491 122.765 283.491 133.308C283.491 143.851 279.886 152.742 272.677 159.981C265.468 167.219 256.535 170.839 245.878 170.839C235.222 170.839 226.289 167.219 219.08 159.981ZM228.013 311.52L267.505 271.865L262.334 245.428C254.184 251.093 246.035 256.207 237.886 260.771C229.737 265.334 221.274 269.819 212.498 274.225L228.013 311.52ZM375.17 3.24875C381.125 41.3302 377.442 78.3889 364.121 114.425C350.8 150.46 327.841 184.844 295.244 217.575L304.647 264.311C305.901 270.606 305.588 276.743 303.707 282.723C301.826 288.702 298.692 293.895 294.304 298.301L215.319 377.612L175.826 284.611L95.4301 203.885L2.81029 164.23L81.3255 84.9193C85.7136 80.5132 90.9636 77.366 97.0756 75.4777C103.188 73.5893 109.378 73.2746 115.647 74.5335L162.192 83.9752C194.789 51.244 228.953 28.1119 264.684 14.5788C300.416 1.04569 337.244 -2.73099 375.17 3.24875ZM36.191 263.839C47.1612 252.824 60.5605 247.238 76.3889 247.08C92.2174 246.923 105.617 252.352 116.587 263.367C127.557 274.383 132.964 287.837 132.807 303.73C132.65 319.624 127.087 333.078 116.117 344.094C108.281 351.962 95.195 358.728 76.8591 364.393C58.5232 370.058 33.2133 375.094 0.929688 379.5C5.31776 347.084 10.3327 321.67 15.9745 303.258C21.6163 284.847 28.3552 271.707 36.191 263.839ZM62.9896 290.276C59.8553 293.423 56.7209 299.167 53.5866 307.507C50.4523 315.847 48.2582 324.266 47.0045 332.764C55.4672 331.505 63.8516 329.38 72.1576 326.391C80.4636 323.401 86.1838 320.332 89.3181 317.185C93.0793 313.408 95.1166 308.845 95.4301 303.494C95.7435 298.144 94.0196 293.581 90.2584 289.804C86.4972 286.027 81.9524 284.218 76.624 284.375C71.2956 284.532 66.7508 286.499 62.9896 290.276Z"
                fill="white"
                fillOpacity="0.08"
              />
            </svg>
          </div>

          {/* Content Container */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center font-poppins py-8 md:py-4 relative z-10">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 mx-auto leading-tight">
              Ready to transform your marketing with digital
              <br />
              marketing and AI?
            </h2>

            <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mt-6">
              <Button
                size="lg"
                className="bg-[#F57141] hover:bg-[#E56738] text-white text-sm md:text-lg font-semibold py-3 md:py-4 px-6 tracking-tight rounded-full inline-flex items-center gap-2 active:scale-95 transition-all duration-300 whitespace-nowrap"
              >
                Get Started with Free AI Tools
                <FaChevronRight className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </section>
        <FooterComp />
      </div>
    </div>
  );
};

export default MarketingAiAgents;
