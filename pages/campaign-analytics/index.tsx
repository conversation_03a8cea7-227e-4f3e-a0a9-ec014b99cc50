import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import MobileContainer from '@/components/lib/MobileContainer';
import { QueryParams } from 'src/constants';
import { useQuery } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import { getCampaignDetails } from 'src/actions/onboarding';
import BackIcon from '@/images/common/back-arrow.svg';
import CampaignStatusComp from '@/components/dashboard/campaigns/CampaignStatusComp';
import { getCampaignInsights } from 'src/actions/dashboard';
import { ICampaignInsightDetails } from 'src/types/campaigns';
import MetaAnalyticsByRegionAccordion from '@/components/campaign_analytics/MetaAnalyticsByRegionAccordion';
import MetaAnalyticsByBodyAssetAccordion from '@/components/campaign_analytics/MetaAnalyticsByBodyAssetAccordion';
import MetaAnalyticsByMediaAssetAccordion from '@/components/campaign_analytics/MetaAnalyticsByMediaAssetAccordion';
import MetaAnalyticsByAgeAndGenderAccordion from '@/components/campaign_analytics/MetaAnalyticsByAgeAndGenderAccordion';
import { getMetaCreativesInsights } from 'src/actions/campaign_details';
import MetaAnalyticsByPlatformAccordion from '@/components/campaign_analytics/MetaAnalyticsByPlatformAccordion';
import MetaAnalyticsByHourRangeAccordion from '@/components/campaign_analytics/MetaAnalyticsByHourRangeAccordion';

interface ICampaignAnalyticsProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

const CampaignAnalytics = (props: ICampaignAnalyticsProps) => {
  const { user } = props;

  const router = useRouter();

  const campaignId = router.query[QueryParams.CAMPAIGN_ID];

  const [openAccordions, setOpenAccordions] = useState<Record<string, boolean>>(
    {},
  );
  const handleAccordionToggle = (id: string, isOpen: boolean) => {
    setOpenAccordions((prev) => ({
      ...prev,
      [id]: isOpen,
    }));
  };

  const campaignDetailsResponse = useQuery(
    ['getCampaignDetails', campaignId],
    () => {
      return getCampaignDetails({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled: !!campaignId,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignAnalytics.getCampaignDetails',
        );
      },
    },
  );

  const campaignDetails = campaignDetailsResponse?.data?.data ?? null;

  const apiQueryParams = {
    ...router.query,
    [QueryParams.CAMPAIGN_ID]: campaignDetails?.meta_id,
    [QueryParams.SORT]: 'clicks_descending',
    [QueryParams.LIMIT]: '50',
  } as Record<string, string>;

  const regionWiseInsightsResponse = useQuery(
    ['getCampaignInsights-region', campaignId],
    () =>
      getCampaignInsights({
        queryParams: {
          ...apiQueryParams,
          [QueryParams.BREAKDOWNS]: 'region',
        },
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignDetails?.meta_id && openAccordions['region'] === true,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignAnalytics.getCampaignInsights.region',
        );
      },
    },
  );

  const ageAndGenderWiseInsightsResponse = useQuery(
    ['getCampaignInsights-age-gender', campaignId],
    () =>
      getCampaignInsights({
        queryParams: {
          ...apiQueryParams,
          [QueryParams.BREAKDOWNS]: 'age,gender',
        },
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled:
        !!campaignDetails?.meta_id && openAccordions['age-gender'] === true,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignAnalytics.getCampaignInsights.age-gender',
        );
      },
    },
  );

  const bodyAssetWiseInsightsResponse = useQuery(
    ['getCampaignInsights-body_asset', campaignId],
    () =>
      getCampaignInsights({
        queryParams: {
          ...apiQueryParams,
          [QueryParams.BREAKDOWNS]: 'body_asset',
        },
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled:
        !!campaignDetails?.meta_id && openAccordions['body-asset'] === true,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignAnalytics.getCampaignInsights.body-asset',
        );
      },
    },
  );

  const mediaAssetWiseInsightsResponse = useQuery(
    ['getMetaCreativesInsights', campaignId],
    () =>
      getMetaCreativesInsights({
        queryParams: {
          [QueryParams.META_ID]: campaignDetails.meta_id,
          [QueryParams.META_AD_IDS]: campaignDetails.meta_ad_ids?.join(','),
        },
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled:
        !!campaignDetails?.meta_id && openAccordions['media-asset'] === true,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignAnalytics.getMetaCreativesInsights',
        );
      },
    },
  );

  const platformWiseInsightsResponse = useQuery(
    ['getCampaignInsights-platform', campaignId],
    () =>
      getCampaignInsights({
        queryParams: {
          ...apiQueryParams,
          [QueryParams.BREAKDOWNS]: 'publisher_platform',
        },
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled:
        !!campaignDetails?.meta_id && openAccordions['platform'] === true,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignAnalytics.getCampaignInsights.platform',
        );
      },
    },
  );

  const hourRangeWiseInsightsResponse = useQuery(
    ['getCampaignInsights-hour-range', campaignId],
    () =>
      getCampaignInsights({
        queryParams: {
          ...apiQueryParams,
          [QueryParams.BREAKDOWNS]:
            'hourly_stats_aggregated_by_audience_time_zone',
        },
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled:
        !!campaignDetails?.meta_id && openAccordions['hour-range'] === true,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignAnalytics.getCampaignInsights.hour-range',
        );
      },
    },
  );

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const onBackPressed = () => {
    router.back();
  };

  const campaignName =
    campaignDetails?.friendly_name ??
    campaignDetails?.details?.business_details?.business_category ??
    '';
  const regionWiseInsights: ICampaignInsightDetails[] =
    regionWiseInsightsResponse?.data?.data ?? [];
  const ageAndGenderWiseInsights: ICampaignInsightDetails[] =
    ageAndGenderWiseInsightsResponse?.data?.data ?? [];
  const bodyAssetWiseInsights: ICampaignInsightDetails[] =
    bodyAssetWiseInsightsResponse?.data?.data ?? [];
  const mediaAssetWiseInsights: ICampaignInsightDetails[] =
    mediaAssetWiseInsightsResponse?.data?.data ?? [];
  const platformWiseInsights: ICampaignInsightDetails[] =
    platformWiseInsightsResponse?.data?.data ?? [];
  const hourRangeWiseInsights: ICampaignInsightDetails[] =
    hourRangeWiseInsightsResponse?.data?.data ?? [];

  return user ? (
    <MobileContainer>
      <div className="flex flex-col flex-1 h-full w-full">
        <div className="flex items-center mt-4">
          <div className="p-4 cursor-pointer" onClick={onBackPressed}>
            <BackIcon />
          </div>
          <p className="text-black text-lg cursor-pointer line-clamp-2">
            {campaignName}{' '}
          </p>
          <div className="flex-1" />
          <div className="px-4">
            {campaignDetails ? (
              <CampaignStatusComp campaignDetails={campaignDetails} />
            ) : null}
          </div>
        </div>
        <div className="px-4 my-5 flex flex-col flex-1 overflow-y-scroll h-full no-scrollbar">
          <MetaAnalyticsByRegionAccordion
            insights={regionWiseInsights}
            campaignType={campaignDetails?.type}
            accordionProps={{
              id: 'region',
              isOpen: openAccordions['region'],
              onToggle: handleAccordionToggle,
            }}
            loading={regionWiseInsightsResponse.isLoading}
          />
          <MetaAnalyticsByAgeAndGenderAccordion
            insights={ageAndGenderWiseInsights}
            className="mt-4"
            campaignType={campaignDetails?.type}
            accordionProps={{
              id: 'age-gender',
              isOpen: openAccordions['age-gender'],
              onToggle: handleAccordionToggle,
            }}
            loading={ageAndGenderWiseInsightsResponse.isLoading}
          />
          <MetaAnalyticsByBodyAssetAccordion
            insights={bodyAssetWiseInsights}
            className="mt-4"
            campaignType={campaignDetails?.type}
            accordionProps={{
              id: 'body-asset',
              isOpen: openAccordions['body-asset'],
              onToggle: handleAccordionToggle,
            }}
            loading={bodyAssetWiseInsightsResponse.isLoading}
          />
          <MetaAnalyticsByMediaAssetAccordion
            insights={mediaAssetWiseInsights}
            className="mt-4"
            campaignType={campaignDetails?.type}
            loading={mediaAssetWiseInsightsResponse.isLoading}
            accordionProps={{
              id: 'media-asset',
              isOpen: openAccordions['media-asset'],
              onToggle: handleAccordionToggle,
            }}
          />
          <MetaAnalyticsByPlatformAccordion
            insights={platformWiseInsights}
            className="mt-4"
            campaignType={campaignDetails?.type}
            accordionProps={{
              id: 'platform',
              isOpen: openAccordions['platform'],
              onToggle: handleAccordionToggle,
            }}
            loading={platformWiseInsightsResponse.isLoading}
          />
          <MetaAnalyticsByHourRangeAccordion
            insights={hourRangeWiseInsights}
            className="mt-4"
            campaignType={campaignDetails?.type}
            accordionProps={{
              id: 'hour-range',
              isOpen: openAccordions['hour-range'],
              onToggle: handleAccordionToggle,
            }}
            loading={hourRangeWiseInsightsResponse.isLoading}
          />
        </div>
      </div>
    </MobileContainer>
  ) : null;
};

export default CampaignAnalytics;
