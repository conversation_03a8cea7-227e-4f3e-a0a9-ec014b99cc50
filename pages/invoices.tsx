import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser } from 'src/types';
import BackIcon from '@/images/common/back-arrow.svg';
import MobileContainer from '@/components/lib/MobileContainer';
import { useQuery } from 'react-query';
import { getInvoices } from 'src/actions/profile';
import { getCommonHeaders } from 'src/actions';
import {
  getFormattedDateString,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import NoDataFound from '@/components/lib/NoDataFound';
import { QueryParams } from 'src/constants';

interface IInvoicesProps {
  user?: IGroweasyUser;
  adminFlow?: boolean;
}

const Invoices = (props: IInvoicesProps) => {
  const { user, adminFlow } = props;

  const router = useRouter();

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const invoicesResponse = useQuery(
    'getInvoices',
    () => {
      return getInvoices({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
        adminFlow,
      });
    },
    {
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'Invoices.getInvoices');
      },
    },
  );

  const onBackPressed = () => {
    router.back();
  };

  const onInvoiceClick = (id: string) => {
    void router.push({
      pathname: '/invoice-details',
      query: {
        [QueryParams.INVOICE_ID]: id,
      },
    });
  };

  const invoices = invoicesResponse?.data?.data ?? null;

  return user ? (
    <MobileContainer>
      <div className="flex flex-col flex-1 w-full h-full">
        <div className="flex items-center mt-4">
          <div className="p-4 cursor-pointer" onClick={onBackPressed}>
            <BackIcon />
          </div>
          <p className="text-black text-lg">Invoices</p>
          <div className="flex-1" />
        </div>
        {invoices ? (
          <div className="mt-3 mb-3 flex flex-col px-5 flex-1 overflow-y-scroll no-scrollbar">
            {invoices.map((item, index) => {
              const campaignDetails = item.campaign_details;
              return (
                <div
                  className="bg-white rounded-xl py-4 px-3 mt-3 shadow-sm cursor-pointer"
                  onClick={() => onInvoiceClick(item.id)}
                  key={index}
                >
                  <div className="flex items-center">
                    <p className="text-sm text-primary font-semibold overflow-hidden text-ellipsis mr-3">
                      {campaignDetails?.friendly_name ||
                        campaignDetails?.business_category}
                    </p>
                    <div className="flex-1" />
                    <p>{item.total_payable}</p>
                  </div>
                  {campaignDetails?.product_or_service_description ? (
                    <p className="text-xxs mt-1 mb-2 line-clamp-2">
                      {campaignDetails.product_or_service_description}
                    </p>
                  ) : null}
                  <p className="text-xs text-gray-dark font-medium">
                    {getFormattedDateString(
                      new Date(campaignDetails?.start_time),
                    )}
                    <span className="mx-2">-</span>
                    {getFormattedDateString(
                      new Date(campaignDetails?.end_time),
                    )}
                  </p>
                </div>
              );
            })}
            {invoices.length === 0 ? (
              <div className="flex justify-center">
                <NoDataFound
                  illustration={{
                    url: '/images/dashboard/payment-illustration.svg',
                    width: 200,
                    height: 200,
                  }}
                  title="No Invoices Found!"
                />
              </div>
            ) : null}
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <SpinnerLoader />
          </div>
        )}
      </div>
    </MobileContainer>
  ) : null;
};

export default Invoices;
