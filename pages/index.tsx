import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { GrowEasyPartners, IGroweasyUser, IPartnerConfig } from 'src/types';
import GrowEasyHomePage from '@/components/home_pages/groweasy/GrowEasyHomePage';
import AdGlobalAiHomePage from '@/components/home_pages/adglobalai/AdGlobalAiHomePage';

interface IHomePageProps {
  user: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

const HomePage = (props: IHomePageProps) => {
  const { user, partnerConfig } = props;

  const router = useRouter();

  useEffect(() => {
    if (user) {
      // not redirecting to /dashboard when user is logged in
      //void router.push('/dashboard');
    } else if (partnerConfig) {
      if (partnerConfig.partner === GrowEasyPartners.AD_GLOBAL_AI) {
        // let its Home Page render
      } else {
        void router.push('/login');
      }
    }
  }, [user, partnerConfig, router]);

  if (partnerConfig) {
    if (partnerConfig.partner === GrowEasyPartners.AD_GLOBAL_AI) {
      return <AdGlobalAiHomePage />;
    } else {
      // will be redirected to /login
      return null;
    }
  } else {
    return <GrowEasyHomePage />;
  }
};

export default HomePage;
