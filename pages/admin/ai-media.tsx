import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import MobileContainer from '@/components/lib/MobileContainer';
import { getCommonHeaders } from 'src/actions';
import { getCampaignDetails } from 'src/actions/onboarding';
import {
  generateAdminAiMedia,
  updateAdminUserCampaign,
} from 'src/actions/admin';
import {
  getFormattedDateString,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import { QueryParams } from 'src/constants';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { showToastMessage } from 'src/modules/toast';
import BackIcon from '@/images/common/back-arrow.svg';
import { FaChevronLeft, FaRedo } from 'react-icons/fa';
import { ICampaign } from 'src/types/campaigns';
import CampaignStatusComp from '@/components/dashboard/campaigns/CampaignStatusComp';

interface IAiMediaProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

interface IGeneratedBanner {
  id: string;
  url: string;
  prompt: string;
  created_at: string;
  width?: number;
  height?: number;
}

const AiMedia = (props: IAiMediaProps) => {
  const { user } = props;
  const router = useRouter();

  const [campaignId, setCampaignId] = useState('');
  const [campaignDetails, setCampaignDetails] = useState<ICampaign | null>(
    null,
  );
  const [generatedBanner, setGeneratedBanner] =
    useState<IGeneratedBanner | null>(null);
  const [isLoadingCampaign, setIsLoadingCampaign] = useState(false);
  const [isGeneratingBanner, setIsGeneratingBanner] = useState(false);

  const [isUpdatingCampaign, setIsUpdatingCampaign] = useState(false);
  const [step, setStep] = useState<
    'input' | 'campaign' | 'banner' | 'confirm' | 'upload'
  >('input');

  // Handle campaign_id query parameter
  useEffect(() => {
    const queryParamCampaignId = router.query[
      QueryParams.CAMPAIGN_ID
    ] as string;
    if (queryParamCampaignId && queryParamCampaignId !== campaignId) {
      setCampaignId(queryParamCampaignId);
    }
  }, [router.query, campaignId]);

  const fetchCampaignDetails = async () => {
    if (!campaignId.trim()) return;

    setIsLoadingCampaign(true);
    try {
      const response = await getCampaignDetails({
        headers: getCommonHeaders(user),
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignId,
        },
      });

      const campaign = response.data;

      setCampaignDetails(campaign);
      setStep('campaign');
      showToastMessage('Campaign details fetched successfully!', 'success');
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error as Error,
        'AiMedia.fetchCampaignDetails',
      );
    } finally {
      setIsLoadingCampaign(false);
    }
  };

  const generateBanner = async () => {
    if (!campaignDetails || !campaignDetails.id) return;

    setIsGeneratingBanner(true);
    try {
      const response = await generateAdminAiMedia({
        headers: getCommonHeaders(user),
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
        },
        data: {
          campaign_id: campaignDetails.id,
          media_type: 'image',
        },
      });

      const bannerData: IGeneratedBanner = {
        id: response.data.hash,
        url: response.data.s3_url,
        prompt: `Generated banner for ${
          campaignDetails.friendly_name || campaignDetails.name
        }`,
        created_at: new Date().toISOString(),
        width: response.data.width,
        height: response.data.height,
      };

      setGeneratedBanner(bannerData);
      setStep('confirm');
      showToastMessage('AI banner generated successfully!', 'success');
    } catch (error) {
      logApiErrorAndShowToastMessage(error as Error, 'AiMedia.generateBanner');
    } finally {
      setIsGeneratingBanner(false);
    }
  };

  const updateCampaignWithBanner = async () => {
    if (!generatedBanner || !campaignDetails || !campaignDetails.id) return;

    setIsUpdatingCampaign(true);
    try {
      const bannerData = {
        hash: generatedBanner.id,
        height: generatedBanner.height || 400,
        s3_url: generatedBanner.url,
        width: generatedBanner.width || 400,
      };

      await updateAdminUserCampaign({
        headers: getCommonHeaders(user),
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
        },
        data: {
          details: {
            ad_banners: [
              {
                image: bannerData,
              },
            ],
          },
        },
      });

      setStep('upload');
      showToastMessage('Campaign updated with banner successfully!', 'success');
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error as Error,
        'AiMedia.updateCampaignWithBanner',
      );
    } finally {
      setIsUpdatingCampaign(false);
    }
  };

  const resetFlow = () => {
    setCampaignId('');
    setCampaignDetails(null);
    setGeneratedBanner(null);
    setStep('input');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      void fetchCampaignDetails();
    }
  };

  return (
    <MobileContainer className="relative overflow-x-hidden">
      <div className="bg-white max-w-5xl mx-auto rounded-2xl overflow-y-auto overflow-x-hidden relative h-full w-full">
        <div className="mt-4 w-full rounded-t-xl flex items-center border-b absolute top-0 bg-white">
          <div
            className="cursor-pointer p-4"
            onClick={() => void router.back()}
          >
            <BackIcon />
          </div>
          <p className="text-lg font-medium tracking-tight">
            AI Media Generator
          </p>
        </div>

        <div className="px-4 py-8 md:p-8 mb-6 mt-14 overflow-y-auto">
          {step === 'input' && (
            <div className="space-y-8">
              <div className="text-center">
                <p className=" font-semibold text-gray-900 mb-2">
                  Enter Campaign Details
                </p>
                <p className="text-gray-600 text-sm">
                  Start by entering your campaign ID to fetch campaign
                  information
                </p>
              </div>

              <div className="max-w-md mx-auto">
                <label
                  htmlFor="campaignId"
                  className="block text-sm font-semibold text-gray-700 mb-3"
                >
                  Campaign ID
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="campaignId"
                    value={campaignId}
                    onChange={(e) => setCampaignId(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Enter campaign ID"
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl transition-colors text-sm"
                  />
                </div>
              </div>

              <div className="text-center fixed bottom-12 left-0 right-0">
                <button
                  onClick={() => void fetchCampaignDetails()}
                  disabled={!campaignId.trim() || isLoadingCampaign}
                  className="inline-flex items-center px-8 py-4 bg-primary2 text-white font-semibold rounded-xl disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 shadow-lg"
                >
                  {isLoadingCampaign ? (
                    <>
                      <SpinnerLoader
                        className="mr-3"
                        size={20}
                        borderWidth={4}
                      />
                      Fetching Campaign...
                    </>
                  ) : (
                    <>
                      <svg
                        className="w-5 h-5 mr-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        />
                      </svg>
                      Fetch Campaign Details
                    </>
                  )}
                </button>
              </div>
            </div>
          )}

          {step === 'campaign' && campaignDetails && (
            <div className="flex flex-col gap-4">
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-2 mb-20 md:mb-20 md:p-6 border border-gray-200">
                <div className="flex gap-1 flex-col">
                  <div className="flex items-center">
                    <p className="text-sm text-primary font-semibold overflow-hidden text-ellipsis mr-3">
                      {campaignDetails.name}
                    </p>
                    <div className="flex-1" />
                    <CampaignStatusComp campaignDetails={campaignDetails} />
                  </div>
                  <p className="text-xxs mt-1 line-clamp-2">
                    {
                      campaignDetails.details?.business_details
                        ?.product_or_service_description
                    }
                  </p>
                  <p className="text-xs text-gray-dark font-medium mt-3">
                    {getFormattedDateString(
                      new Date(
                        campaignDetails.details?.budget_and_scheduling
                          ?.start_time ??
                          campaignDetails.created_at._seconds * 1000,
                      ),
                    )}
                  </p>
                </div>
              </div>

              <div className="flex flex-col mx-10 sm:flex-row gap-2 md:gap-4 justify-center fixed bottom-12 left-0 right-0">
                <button
                  onClick={resetFlow}
                  className="inline-flex items-center px-6 py-2 border border-gray-300 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200 bg-white"
                >
                  <FaChevronLeft className="mr-3" />
                  Back
                </button>
                <button
                  onClick={() => void generateBanner()}
                  disabled={isGeneratingBanner}
                  className="inline-flex items-center px-6 py-2 bg-primary2 text-white font-semibold rounded-xl disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 shadow-lg"
                >
                  {isGeneratingBanner ? (
                    <>
                      <SpinnerLoader
                        className="mr-3"
                        size={20}
                        borderWidth={4}
                      />
                      Generating Banner...
                    </>
                  ) : (
                    <>
                      <svg
                        className="w-5 h-5 mr-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13 10V3L4 14h7v7l9-11h-7z"
                        />
                      </svg>
                      Generate AI Banner
                    </>
                  )}
                </button>
              </div>
            </div>
          )}

          {step === 'confirm' && generatedBanner && (
            <div className="flex flex-col gap-4">
              <div className="text-center">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  Confirm Banner
                </h2>
                <p className="text-gray-600 text-sm md:px-12">
                  Review your AI-generated banner and confirm to save it to the
                  campaign
                </p>
              </div>

              <div className="bg-gradient-to-r from-gray-50 to-purple-50 rounded-2xl p-2 mb-20 md:mb-20  md:p-8 border border-gray-200">
                <div className="max-w-2xl mx-auto space-y-6">
                  <div className="bg-white rounded-xl overflow-hidden shadow-lg border border-gray-200">
                    <img
                      src={generatedBanner.url}
                      alt="Generated Banner"
                      className="w-full h-auto"
                      style={{ maxHeight: '450px', objectFit: 'cover' }}
                    />
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-2 mx-4 justify-center fixed bottom-12 left-0 right-0">
                <button
                  onClick={() => void updateCampaignWithBanner()}
                  disabled={isUpdatingCampaign}
                  className="inline-flex items-center px-8 py-3 bg-primary text-white font-semibold rounded-xl disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 shadow-lg"
                >
                  {isUpdatingCampaign ? (
                    <>
                      <SpinnerLoader
                        className="mr-3"
                        size={20}
                        borderWidth={4}
                      />
                      Saving to Campaign...
                    </>
                  ) : (
                    <>
                      <svg
                        className="w-5 h-5 mr-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      Confirm Banner
                    </>
                  )}
                </button>
                <button
                  onClick={resetFlow}
                  className="inline-flex items-center px-6 py-3 border bg-white border-gray-300 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  Start Over
                </button>
              </div>
            </div>
          )}

          {step === 'upload' && (
            <div className="text-center space-y-8">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-4">
                <svg
                  className="w-10 h-10 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>

              <div className="max-w-md mx-auto">
                <h2 className="text-3xl font-bold text-green-900 mb-4">
                  Banner Saved Successfully!
                </h2>
                <p className="text-lg text-green-700">
                  The AI-generated banner has been saved to your campaign and is
                  ready to use.
                </p>
              </div>

              <div className="bg-green-50 border-2 border-green-200 rounded-2xl p-6 max-w-lg mx-auto">
                <div className="flex items-center justify-center space-x-2 text-green-800">
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span className="font-semibold">Ready for Campaign Use</span>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={resetFlow}
                  className="inline-flex items-center px-6 py-3 bg-primary2 text-white font-semibold rounded-xl transition-all duration-200"
                >
                  <FaRedo className="mr-3" />
                  Start Over
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </MobileContainer>
  );
};

export default AiMedia;
