import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import MobileContainer from '@/components/lib/MobileContainer';
import Image from 'next/image';
import { useInfiniteQuery } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import FetchError from 'src/actions/FetchError';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import Link from 'next/link';
import {
  getLocalStorage,
  logApiErrorAndShowToastMessage,
  setLocalStorage,
} from 'src/utils';
import { getAdminCampaigns } from 'src/actions/admin';
import CampaignListItem from '@/components/dashboard/campaigns/CampaignListItem';
import classNames from 'classnames';
import { QueryParams } from 'src/constants';
import { GrowEasyCampaignStatus, ICampaign } from 'src/types/campaigns';
import AssignCampaignBs from '@/components/admin/AssignCampaignBs';
import SearchCampaignsBs from '@/components/admin/SearchCampaignsBs';
import SearchIcon from '@/images/common/search.svg';
import { HiXCircle } from 'react-icons/hi';

interface IAdminDashboardProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

const SELECTED_TAB_ID_LS_KEY = 'admin_dashboard_selected_tab_id';

export const CAMPAIGN_TABS = [
  {
    id: 'draft',
    label: 'Draft',
  },
  {
    id: 'paused',
    label: 'Paused',
  },
  {
    id: 'active',
    label: 'Active',
  },
];

const AdminDashboard = (props: IAdminDashboardProps) => {
  const { user, partnerConfig } = props;

  const [selectedTabId, setSelectedTabId] = useState(CAMPAIGN_TABS[0].id);
  const [assignCampaignSheetData, setAssignCampaignSheetData] =
    useState<ICampaign | null>(null);
  const [showSearchSheet, setShowSearchSheet] = useState(false);

  const router = useRouter();

  const queryParams = router.query as Record<string, string>;

  const draftCampaignsResponse = useInfiniteQuery(
    ['getAdminCampaigns', CAMPAIGN_TABS[0].id, queryParams.email],
    (params) => {
      return getAdminCampaigns({
        headers: getCommonHeaders(user),
        queryParams: {
          ...queryParams,
          [QueryParams.STATUS]: GrowEasyCampaignStatus.DRAFT,
          [QueryParams.LAST_CURSOR_ID]: (params.pageParam as string) ?? '',
          [QueryParams.LIMIT]: `20`,
        },
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      getNextPageParam: (lastPage) => {
        return lastPage.data?.last_cursor_id || undefined;
      },
      onError: (error: FetchError | Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'admin.dashboard.draftCampaignsResponse',
        );
      },
    },
  );

  const pausedCampaignsResponse = useInfiniteQuery(
    ['getAdminCampaigns', CAMPAIGN_TABS[1].id, queryParams.email],
    (params) => {
      return getAdminCampaigns({
        headers: getCommonHeaders(user),
        queryParams: {
          ...queryParams,
          [QueryParams.STATUS]: GrowEasyCampaignStatus.PAUSED,
          [QueryParams.LAST_CURSOR_ID]: (params.pageParam as string) ?? '',
          [QueryParams.LIMIT]: `20`,
        },
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      getNextPageParam: (lastPage) => {
        return lastPage.data?.last_cursor_id || undefined;
      },
      onError: (error: FetchError | Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'admin.dashboard.pausedCampaignsResponse',
        );
      },
    },
  );

  const activeCampaignsResponse = useInfiniteQuery(
    ['getAdminCampaigns', CAMPAIGN_TABS[2].id, queryParams.email],
    (params) => {
      return getAdminCampaigns({
        headers: getCommonHeaders(user),
        queryParams: {
          ...queryParams,
          [QueryParams.STATUS]: `${GrowEasyCampaignStatus.ACTIVE},${GrowEasyCampaignStatus.ARCHIVED}`,
          [QueryParams.LAST_CURSOR_ID]: (params.pageParam as string) ?? '',
          [QueryParams.LIMIT]: `20`,
        },
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      getNextPageParam: (lastPage) => {
        return lastPage.data?.last_cursor_id || undefined;
      },
      onError: (error: FetchError | Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'admin.dashboard.activeCampaignsResponse',
        );
      },
    },
  );

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  useEffect(() => {
    const selectedTabIdFromLs = getLocalStorage(SELECTED_TAB_ID_LS_KEY);
    if (selectedTabIdFromLs) {
      setSelectedTabId(selectedTabIdFromLs);
    }
  }, []);

  const onAssignCampaignRefresh = () => {
    if (selectedTabId === CAMPAIGN_TABS[0].id) {
      void draftCampaignsResponse.refetch();
    } else if (selectedTabId === CAMPAIGN_TABS[1].id) {
      void pausedCampaignsResponse.refetch();
    }
    if (selectedTabId === CAMPAIGN_TABS[2].id) {
      void activeCampaignsResponse.refetch();
    }
  };

  const targetCampaignResponse =
    selectedTabId === CAMPAIGN_TABS[0].id
      ? draftCampaignsResponse
      : selectedTabId === CAMPAIGN_TABS[1].id
        ? pausedCampaignsResponse
        : activeCampaignsResponse;
  const campaigns: ICampaign[] = [];
  targetCampaignResponse.data?.pages?.forEach((item) => {
    campaigns.push(...item.data.campaigns);
  });

  return user ? (
    <MobileContainer>
      <div className="flex flex-col flex-1 h-full w-full pt-4">
        <div className="flex items-center px-5">
          <p className="text-black text-xl font-poppins font-semibold tracking-tight">
            {partnerConfig?.name ?? 'GrowEasy'} Admin
          </p>
          <div className="flex-1" />
          <div className="flex items-center gap-3">
            <div
              className="p-2 cursor-pointer hover:bg-gray-100 rounded-full transition-colors"
              onClick={() => setShowSearchSheet(true)}
            >
              <SearchIcon width="20" height="20" className="text-gray-600" />
            </div>
            <div className="p-3">
              <Link href="/profile">
                <Image
                  src={user.photoUrl}
                  width="24"
                  height="24"
                  alt=""
                  className="rounded-full"
                />
              </Link>
            </div>
          </div>
        </div>
        {queryParams.email && (
          <div className="px-5 mt-3">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-blue-800">
                    Showing campaigns for:{' '}
                    <span className="font-semibold">{queryParams.email}</span>
                  </p>
                </div>
                <button
                  type="button"
                  onClick={() => {
                    void router.push('/admin/dashboard');
                  }}
                  className="text-rose-600 hover:text-rose-800 text-xs font-medium inline-flex items-center gap-1"
                >
                  <HiXCircle size={16} />
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
        <div className="flex items-center px-4 mt-3 border-b border-gray-light">
          {CAMPAIGN_TABS.map((item, index) => {
            const selected = item.id === selectedTabId;
            return (
              <div
                key={index}
                className={classNames('flex-1 cursor-pointer pb-4', {
                  'border-b-2 border-primary': selected,
                })}
                onClick={() => {
                  setSelectedTabId(item.id);
                  setLocalStorage(SELECTED_TAB_ID_LS_KEY, item.id);
                }}
              >
                <p
                  className={classNames('text-sm text-primary text-center', {
                    'font-bold': selected,
                  })}
                >
                  {item.id === CAMPAIGN_TABS[0].id
                    ? item.label
                    : `${item.label}`}
                </p>
              </div>
            );
          })}
        </div>
        <div className="flex-1 overflow-y-scroll flex flex-col no-scrollbar px-5">
          <div className="flex flex-col flex-1 mt-3 mb-4">
            {targetCampaignResponse.isLoading ? (
              <div className="flex justify-center">
                <SpinnerLoader />
              </div>
            ) : (
              <div>
                {campaigns?.map((item) => {
                  return (
                    <div
                      key={item.id}
                      className="rounded-xl py-4 px-3 mt-3 shadow-sm bg-white"
                    >
                      <CampaignListItem data={item} adminView />
                      <hr className="my-3" />
                      <div className="flex items-center">
                        <p
                          className="text-hyperlink text-xxs font-medium cursor-pointer"
                          onClick={() => setAssignCampaignSheetData(item)}
                        >
                          Assign
                        </p>
                      </div>
                    </div>
                  );
                })}
                {targetCampaignResponse.hasNextPage ? (
                  <div className="my-3">
                    {targetCampaignResponse.isFetching ? (
                      <div className="flex justify-center">
                        <SpinnerLoader borderWidth={2} size={20} />
                      </div>
                    ) : (
                      <p
                        className="text-sm text-center text-hyperlink cursor-pointer"
                        onClick={() => {
                          void targetCampaignResponse.fetchNextPage();
                        }}
                      >
                        Load More
                      </p>
                    )}
                  </div>
                ) : null}
              </div>
            )}
          </div>
        </div>
      </div>
      {assignCampaignSheetData ? (
        <AssignCampaignBs
          campaign={assignCampaignSheetData}
          onClose={() => setAssignCampaignSheetData(null)}
          onRefresh={onAssignCampaignRefresh}
          user={user}
        />
      ) : null}
      {showSearchSheet ? (
        <SearchCampaignsBs onClose={() => setShowSearchSheet(false)} />
      ) : null}
    </MobileContainer>
  ) : null;
};

export default AdminDashboard;
