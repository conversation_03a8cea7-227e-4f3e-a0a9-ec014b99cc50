import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import Head from 'next/head';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FormEventHandler, useRef, useState } from 'react';
import { getCommonHeaders } from 'src/actions';
import { submitGoogleKeywordIdeasData } from 'src/actions/seo';
import { showToastMessage } from 'src/modules/toast';
import {
  getMetaTags,
  isValidURL,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import DownArrow from '@/images/common/down-arrow.svg';
import SearchCategoryModal from '@/components/onboarding/business_details/SearchCategoryModal';
import BUSINESS_CATEGORIES from 'src/constants/business_categories';

const GoogleKeywordIdeasGenerator = () => {
  const [submissionInProgress, setSubmissionInProgress] = useState(false);

  const formRef = useRef<HTMLFormElement>(null);

  const [category, setCategory] = useState('');
  const [showCategoryModal, setShowCategoryModal] = useState(false);

  const router = useRouter();

  const onFormSubmit: FormEventHandler<HTMLFormElement> = (event) => {
    event.preventDefault();

    const formData = new FormData(event.currentTarget); // Get the form data
    const formValues: { [key: string]: string } = {};

    // Loop through formData and construct a key-value pair object
    formData.forEach((value, key) => {
      if (typeof value === 'string') {
        formValues[key] = value.toString();
      }
    });
    formValues.business_category = category;
    const formIncomplete = Object.keys(formValues).some(
      (key) => key !== 'website' && !formValues[key],
    );
    if (formIncomplete) {
      showToastMessage('Please fill all the details', 'error');
      return;
    }

    if (formValues.website && !isValidURL(formValues.website)) {
      showToastMessage('Invalid website', 'error');
      return;
    }

    setSubmissionInProgress(true);

    submitGoogleKeywordIdeasData({
      headers: getCommonHeaders(),
      queryParams: router.query as Record<string, string>,
      data: {
        form_values: formValues,
      },
    })
      .then(() => {
        showToastMessage(
          'Thank you! We have sent you high-volume keyword ideas.',
          'success',
        );
        // Reset the form after submission
        if (formRef.current) {
          formRef.current.reset();
        }
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'GoogleKeywordIdeasGenerator.onFormSubmit',
        );
      })
      .finally(() => {
        setSubmissionInProgress(false);
      });
  };

  const bgImageUrl =
    'https://images.unsplash.com/photo-1625296276703-3fbc924f07b5';

  return (
    <div>
      <Head>{getMetaTags('/tools/google-keyword-ideas-generator')}</Head>
      <div className="grid md:grid-cols-2">
        <div
          className="bg-cover min-h-[580px] lg:min-h-screen"
          style={{ backgroundImage: `url(${bgImageUrl})` }}
        >
          <div className="flex flex-col h-full w-full bg-black/50 px-8 pt-8 pb-12">
            <Link href="/" target="_blank">
              <div className="flex items-center cursor-pointer">
                <div className="w-16 mr-3">
                  <Image
                    src="/images/groweasy-logo-square.png"
                    alt={`groweasy-logo`}
                    width={80}
                    height={80}
                  />
                </div>
                <p className="text-xl font-medium text-white">GrowEasy</p>
              </div>
            </Link>
            <div className="flex-1" />
            <h1 className="text-3xl font-semibold text-white mt-8">
              Discover High-Volume Google Keywords for Your Business
            </h1>
            <p className="text-white font-medium mt-5">
              Let GrowEasy’s AI-powered Google Keyword Ideas tool generate
              high-performing keyword suggestions for your ads and SEO strategy.
            </p>
          </div>
        </div>

        <div className="px-6 pt-4">
          <h2 className="mt-6 text-2xl font-medium">
            Boost Your SEO and PPC Campaigns
          </h2>
          <p className="mt-3 !text-gray-dark">
            Finding the right keywords is crucial for ranking higher in search
            results and optimizing your ad spend. With GrowEasy, discover
            high-volume and relevant keywords tailored to your business.
          </p>
          <form onSubmit={onFormSubmit} className="mt-8" ref={formRef}>
            <div className="gap-x-5 gap-y-6 grid lg:grid-cols-1">
              <div className="w-full">
                <p className="text-sm text-black">
                  Select Your Business Category
                </p>
                <div
                  className={`mt-3 border border-gray-medium rounded-lg p-2 w-full flex items-center `}
                  onClick={() => setShowCategoryModal(true)}
                >
                  <div className="flex-1">{category || 'Select'}</div>
                  <div>
                    <DownArrow
                      width="24"
                      height="24"
                      className="text-gray-dark"
                    />
                  </div>
                </div>
                {showCategoryModal && (
                  <SearchCategoryModal
                    categories={BUSINESS_CATEGORIES}
                    onChange={(value: string) => setCategory(value)}
                    hideModal={() => setShowCategoryModal(false)}
                  />
                )}
              </div>
              <div className="w-full">
                <p className="text-sm capitalize">
                  Describe Your Product or Service
                </p>
                <textarea
                  className="w-full border border-gray-medium mt-3 rounded-lg outline-none p-2 text-gray-dark"
                  rows={4}
                  name="product_or_service_description"
                  placeholder="Describe your offering here."
                ></textarea>
              </div>

              <div className="w-full">
                <p className="text-sm capitalize">Enter your website</p>
                <input
                  type="text"
                  name="website"
                  className="mt-2 border border-gray-medium p-2 rounded w-full outline-none focus:border-primary hover:border-gray-dark"
                  placeholder="Your website will be used filter out irrelevant keywords."
                />
              </div>

              <div className="w-full">
                <p className="text-sm capitalize">Choose Targeting Country</p>
                <div className="mt-3 flex items-center">
                  <input
                    type="radio"
                    name="country_code"
                    value="IN"
                    checked
                    className="mr-2"
                  />
                  <p className="text-xs mr-5">India</p>
                  <input
                    type="radio"
                    name="country_code"
                    value="US"
                    className="mr-2"
                  />
                  <p className="text-xs">United States</p>
                </div>
              </div>

              <div className="w-full">
                <p className="text-sm capitalize">Enter your email</p>
                <input
                  type="email"
                  name="email"
                  className="mt-2 border border-gray-medium p-2 rounded w-full outline-none focus:border-primary hover:border-gray-dark"
                  placeholder="We'll send you high volume google keyword ideas on this email"
                />
              </div>
            </div>
            <div className="mt-8 w-full">
              <input
                type="submit"
                className="w-full bg-primary text-white py-3 px-6 rounded-md cursor-pointer hover:shadow-md text-lg"
                value="Send me Keyword Ideas"
              />
            </div>
          </form>
        </div>
      </div>
      <div className="p-6 mt-6">
        <div className="w-full border rounded-lg border-primary p-2">
          <Image
            alt="facebook-audience-buider-by-groweasy"
            src="/images/tools/keyword-ideas-groweasy-email.png"
            width="2102"
            height="1380"
          />
        </div>
      </div>
      <div className="p-6 sm:p-8">
        <h2 className="text-2xl font-medium text-primary">How It Works</h2>
        <div className="gap-2 mt-3 flex flex-col">
          <p className="text-sm">
            <span className="font-medium mr-1">
              1. Enter Your Product or Service Details:
            </span>
            Describe your business, products, or services to generate relevant
            keyword ideas.
          </p>
          <p className="text-sm">
            <span className="font-medium mr-1">2. Provide Your Email:</span>
            Receive a detailed keyword list directly in your inbox.
          </p>
          <p className="text-sm">
            <span className="font-medium mr-1">
              3. Get High-Intent Keywords:
            </span>
            Our AI analyzes your input and delivers a curated list of highly
            relevant keywords to improve your ad targeting and SEO strategy.
          </p>
        </div>
      </div>
      <div className="p-6 sm:p-8">
        <h2 className="text-2xl font-medium text-primary">Key Benefits</h2>
        <div className="gap-2 mt-3 flex flex-col">
          <p className="text-sm">
            <span className="font-medium mr-1">
              1. Discover Untapped Keywords:
            </span>
            Uncover high-performing keywords that aren’t easily accessible
            through traditional tools.
          </p>
          <p className="text-sm">
            <span className="font-medium mr-1">2. AI-Powered Insights:</span>
            Get data-driven keyword suggestions tailored to your niche and
            target audience.
          </p>
          <p className="text-sm">
            <span className="font-medium mr-1">
              3. Gain a Competitive Edge:
            </span>
            Target keywords that your competitors might be missing, improving
            your ad reach and SEO rankings.
          </p>
          <p className="text-sm">
            <span className="font-medium mr-1">4. User-Friendly & Fast:</span>
            No technical expertise required—simply enter your details and
            receive your optimized keyword list in minutes.
          </p>
        </div>
      </div>
      <div className="p-6 sm:p-8">
        <h2 className="text-2xl font-medium text-primary">
          Why Use This Tool?
        </h2>
        <div className="gap-2 mt-3 flex flex-col">
          <p className="text-sm">
            - Google Ads Keyword Planner is not suitable for beginners.
          </p>
          <p className="text-sm">
            - Many keyword research tools focus only on generic terms, leading
            to poor targeting.
          </p>
          <p className="text-sm">
            - GrowEasy’s AI-driven analysis creates a custom keyword list based
            on your entire product/service description, ensuring precision and
            effectiveness.
          </p>
        </div>
      </div>
      <div className="p-6 sm:p-8">
        <h2 className="text-2xl font-medium text-primary">Who Can Benefit?</h2>
        <div className="gap-2 mt-3 flex flex-col">
          <p className="text-sm">
            - Small Business Owners looking to optimize their online presence.
          </p>
          <p className="text-sm">
            - Digital Marketing Agencies aiming for better ad targeting.
          </p>
          <p className="text-sm">
            - Startups seeking cost-effective keyword strategies.
          </p>
          <p className="text-sm">
            - E-commerce Brands wanting to increase search visibility.
          </p>
          <p className="text-sm">
            - Lead Generation Professionals focusing on high-converting
            keywords.
          </p>
        </div>
      </div>
      <FooterComp />
      {submissionInProgress ? <FullScreenLoader /> : null}
    </div>
  );
};

export default GoogleKeywordIdeasGenerator;
