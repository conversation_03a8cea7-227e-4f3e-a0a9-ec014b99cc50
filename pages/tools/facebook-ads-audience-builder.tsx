import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import Head from 'next/head';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FormEventHandler, useRef, useState } from 'react';
import { getCommonHeaders } from 'src/actions';
import { submitFacebookAdsAudienceBuilderData } from 'src/actions/seo';
import { showToastMessage } from 'src/modules/toast';
import { getMetaTags, logApiErrorAndShowToastMessage } from 'src/utils';
import DownArrow from '@/images/common/down-arrow.svg';
import SearchCategoryModal from '@/components/onboarding/business_details/SearchCategoryModal';
import BUSINESS_CATEGORIES from 'src/constants/business_categories';

const FacebookAdsAudienceBuilder = () => {
  const [submissionInProgress, setSubmissionInProgress] = useState(false);

  const formRef = useRef<HTMLFormElement>(null);

  const [category, setCategory] = useState('');
  const [showCategoryModal, setShowCategoryModal] = useState(false);

  const router = useRouter();

  const onFormSubmit: FormEventHandler<HTMLFormElement> = (event) => {
    event.preventDefault();

    const formData = new FormData(event.currentTarget); // Get the form data
    const formValues: { [key: string]: string } = {};

    // Loop through formData and construct a key-value pair object
    formData.forEach((value, key) => {
      if (typeof value === 'string') {
        formValues[key] = value.toString();
      }
    });
    formValues.business_category = category;
    const formIncomplete = Object.keys(formValues).some(
      (key) => !formValues[key],
    );
    if (formIncomplete) {
      showToastMessage('Please fill all the details', 'error');
      return;
    }

    setSubmissionInProgress(true);

    submitFacebookAdsAudienceBuilderData({
      headers: getCommonHeaders(),
      queryParams: router.query as Record<string, string>,
      data: {
        form_values: formValues,
      },
    })
      .then(() => {
        showToastMessage(
          'Thank you! We have sent you detailed targeting for your product/service.',
          'success',
        );
        // Reset the form after submission
        if (formRef.current) {
          formRef.current.reset();
        }
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'FacebookAdsAudienceBuilder.onFormSubmit',
        );
      })
      .finally(() => {
        setSubmissionInProgress(false);
      });
  };

  const bgImageUrl =
    'https://images.unsplash.com/photo-1517292987719-0369a794ec0f';

  return (
    <div>
      <Head>{getMetaTags('/tools/facebook-ads-audience-builder')}</Head>
      <div className="grid md:grid-cols-2">
        <div
          className="bg-cover min-h-[580px] lg:min-h-screen"
          style={{ backgroundImage: `url(${bgImageUrl})` }}
        >
          <div className="flex flex-col h-full w-full bg-black/50 px-8 pt-8 pb-12">
            <Link href="/" target="_blank">
              <div className="flex items-center cursor-pointer">
                <div className="w-16 mr-3">
                  <Image
                    src="/images/groweasy-logo-square.png"
                    alt={`groweasy-logo`}
                    width={80}
                    height={80}
                  />
                </div>
                <p className="text-xl font-medium text-white">GrowEasy</p>
              </div>
            </Link>
            <div className="flex-1" />
            <h1 className="text-3xl font-semibold text-white mt-8">
              Discover Hidden Facebook Interests for Optimized Ad Campaigns
            </h1>
            <p className="text-white font-medium mt-5">
              Let GrowEasy’s AI-powered Facebook Audience Builder generate a
              custom audience for your next lead ad.
            </p>
          </div>
        </div>

        <div className="px-6 pt-4">
          <h2 className="mt-6 text-2xl font-medium">
            Stand Out in the Competitive World of Facebook Advertising
          </h2>
          <p className="mt-3 !text-gray-dark">
            Facebook ads manager shows limited suggestions based on a keyword,
            leading to oversaturated interests. With GrowEasy, discover hidden
            Facebook interests using our AI-powered tool to generate a highly
            optimized audience from a detailed product or service description.
          </p>
          <p className="text-sm mt-8">
            Please Share a detailed description of your offering and we’ll email
            you optimized audience suggestions.
          </p>
          <form onSubmit={onFormSubmit} className="mt-8" ref={formRef}>
            <div className="gap-x-5 gap-y-8 grid lg:grid-cols-1">
              <div className="w-full">
                <p className="text-sm text-black">
                  How do you classify your business as?
                </p>
                <div
                  className={`mt-3 border border-gray-medium rounded-lg p-2 w-full flex items-center `}
                  onClick={() => setShowCategoryModal(true)}
                >
                  <div className="flex-1">{category || 'Select'}</div>
                  <div>
                    <DownArrow
                      width="24"
                      height="24"
                      className="text-gray-dark"
                    />
                  </div>
                </div>
                {showCategoryModal && (
                  <SearchCategoryModal
                    categories={BUSINESS_CATEGORIES}
                    onChange={(value: string) => setCategory(value)}
                    hideModal={() => setShowCategoryModal(false)}
                  />
                )}
              </div>
              <div className="w-full">
                <p className="text-sm capitalize">
                  What do you want to sell or promote?
                </p>
                <textarea
                  className="w-full border border-gray-medium mt-3 rounded-lg outline-none p-2 text-gray-dark"
                  rows={4}
                  name="product_or_service_description"
                  placeholder="Describe your product or service here."
                ></textarea>
              </div>

              <div className="w-full">
                <p className="text-sm capitalize">Enter your email</p>
                <input
                  type="email"
                  name="email"
                  className="mt-2 border border-gray-medium p-2 rounded w-full outline-none focus:border-primary hover:border-gray-dark"
                  placeholder="We'll send you optimized audience suggestions on this email"
                />
              </div>
            </div>
            <div className="mt-8 w-full">
              <input
                type="submit"
                className="w-full bg-primary text-white py-3 px-6 rounded-md cursor-pointer hover:shadow-md text-lg"
                value="Send me Audience suggestions"
              />
            </div>
          </form>
        </div>
      </div>
      <div className="p-6 mt-6">
        <div className="w-full border rounded-lg border-primary p-2">
          <Image
            alt="facebook-audience-buider-by-groweasy"
            src="/images/tools/audience-suggestions-groweasy-email.png"
            width="2102"
            height="1380"
          />
        </div>
      </div>
      <div className="p-6 sm:p-8">
        <h2 className="text-2xl font-medium text-primary">How It Works</h2>
        <div className="gap-2 mt-3 flex flex-col">
          <p className="text-sm">
            <span className="font-medium mr-1">
              1. Enter Product/Service Description:
            </span>
            Share a detailed description of your offering.
          </p>
          <p className="text-sm">
            <span className="font-medium mr-1">2. Provide Your Email:</span>
            Enter your email to receive the optimized audience suggestions.
          </p>
          <p className="text-sm">
            <span className="font-medium mr-1">3. Get Your Audience:</span>Our
            AI processes your input and emails a custom, highly targeted
            audience to maximize ad performance.
          </p>
        </div>
      </div>
      <div className="p-6 sm:p-8">
        <h2 className="text-2xl font-medium text-primary">Key Benefits</h2>
        <div className="gap-2 mt-3 flex flex-col">
          <p className="text-sm">
            <span className="font-medium mr-1">
              1. Unlock Hidden Interests:
            </span>
            Access Facebook interests unavailable via the ads manager.
          </p>
          <p className="text-sm">
            <span className="font-medium mr-1">2. AI-Driven Optimization:</span>
            Tailored audience suggestions based on your detailed input.
          </p>
          <p className="text-sm">
            <span className="font-medium mr-1">
              3. Stay Ahead of Competitors:
            </span>
            Avoid common and oversaturated interests used by most advertisers.
          </p>
          <p className="text-sm">
            <span className="font-medium mr-1">4. Easy to Use:</span>No
            technical knowledge required—just input your product/service
            description.
          </p>
        </div>
      </div>
      <div className="p-6 sm:p-8">
        <h2 className="text-2xl font-medium text-primary">
          Why Use This Tool?
        </h2>
        <div className="gap-2 mt-3 flex flex-col">
          <p className="text-sm">
            - Facebook Ads Manager limits suggestions to just 25 keywords
          </p>
          <p className="text-sm">
            - Competing tools rely solely on keywords, which can result in less
            effective targeting.
          </p>
          <p className="text-sm">
            - GrowEasy generates a custom audience by analyzing your entire
            product/service description, giving you a competitive edge.
          </p>
        </div>
      </div>
      <div className="p-6 sm:p-8">
        <h2 className="text-2xl font-medium text-primary">Who Can Benefit?</h2>
        <div className="gap-2 mt-3 flex flex-col">
          <p className="text-sm">- Small Business Owners</p>
          <p className="text-sm">- Digital Marketing Agencies</p>
          <p className="text-sm">- Startups</p>
          <p className="text-sm">- E-commerce Brands</p>
          <p className="text-sm">- Lead Generation Professionals</p>
        </div>
      </div>
      <FooterComp />
      {submissionInProgress ? <FullScreenLoader /> : null}
    </div>
  );
};

export default FacebookAdsAudienceBuilder;
