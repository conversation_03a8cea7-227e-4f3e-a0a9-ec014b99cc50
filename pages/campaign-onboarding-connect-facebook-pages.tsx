import Button from '@/components/lib/Button';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import Head from 'next/head';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useMutation } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import { processFbLoginDetails } from 'src/actions/onboarding';
import {
  FB_LOGIN_TO_RUN_ADS_USING_USERS_PAGE_CONFIG_ID,
  QueryParams,
} from 'src/constants';
import { EVENT_NAMES } from 'src/constants/events';
import { logEvent } from 'src/modules/firebase';
import {
  IFbLoginStatusResult,
  IGroweasyUser,
  IProcessedFbLoginResponse,
} from 'src/types';
import {
  getMetaTags,
  logApiErrorAndShowToastMessage,
  openUrlInNewTab,
} from 'src/utils';

interface ICampaignOnboardingConnectFacebookPagesProps {
  user?: IGroweasyUser;
}

const CampaignOnboardingConnectFacebookPages = (
  props: ICampaignOnboardingConnectFacebookPagesProps,
) => {
  const { user } = props;

  const [fbLoginStatus, setFbLoginStatus] =
    useState<IFbLoginStatusResult | null>(null);
  const [loggedInUserDetails, setLoggedInUserDetails] =
    useState<IProcessedFbLoginResponse | null>(null);

  const router = useRouter();

  const processLoginDetailsMutation = useMutation(processFbLoginDetails);
  const token = router.query[QueryParams.TOKEN] as string;

  useEffect(() => {
    const checkFB = () => {
      if (window.FB) {
        window.FB.getLoginStatus((response) => {
          setFbLoginStatus(response);
        });
      } else {
        setTimeout(checkFB, 500); // Retry after 500ms if FB SDK isn't loaded
      }
    };

    checkFB();
  }, []);

  useEffect(() => {
    const authToken = user?.authToken || token;

    // connected: The person is logged into Facebook, and has logged into your webpage.
    // not_authorized: The person is logged into Facebook, but has not logged into your webpage.
    // unknown: The person is not logged into Facebook, so you don't know if they have logged into your webpage.
    // Or FB.logout() was called before, and therefore, it cannot connect to Facebook.
    if (fbLoginStatus?.status === 'connected' && authToken) {
      // fetch User's Pages and render
      /*window.FB?.api('/me', function(response: {
          name: string
          id: string
        }) {
          setLoggedInUserDetails(response)
        });
        window.FB?.api('/me/assigned_pages', function (response) {
          console.log(response)
        });*/
      processLoginDetailsMutation
        .mutateAsync({
          headers: {
            ...getCommonHeaders(),
            Authorization: `Bearer ${authToken}`,
          },
          queryParams: router.query as Record<string, string>,
          body: { result: fbLoginStatus },
        })
        .then((response) => {
          setLoggedInUserDetails(response.data);
        })
        .catch((error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'CampaignOnboardingConnectFacebookPages.processLoginDetailsMutation',
          );
        });
    } else {
      setLoggedInUserDetails(null);
    }
  }, [fbLoginStatus, user]);

  const onFbLoginClick = () => {
    logEvent(EVENT_NAMES.fb_page_login_with_fb_clicked);
    window.FB?.login(
      function (response) {
        setFbLoginStatus(response);
      },
      {
        config_id: FB_LOGIN_TO_RUN_ADS_USING_USERS_PAGE_CONFIG_ID,
        // response_type & override_default_response_type for SUAT only
        //response_type: 'code', // must be set to 'code' for System User access token
        //override_default_response_type: true,
      },
    );
  };

  const onFbLogoutClick = () => {
    logEvent(EVENT_NAMES.fb_page_logout_clicked);
    window.FB?.logout(function (response) {
      // this does not get called in case of SUAT
      setFbLoginStatus(response);
    });
    // explicitly resetting login status since callback does not get called in SUAT case
    setFbLoginStatus(null);
  };

  return (
    <div>
      <Head>
        {getMetaTags('/campaign-onboarding-connect-facebook-pages')}
        <meta name="robots" key="robots" content="noindex, nofollow" />
      </Head>
      <div className="px-4 py-4 sm:px-36 text-center">
        {loggedInUserDetails?.account_details ? (
          <div className="mt-8">
            <div className="flex flex-col items-center">
              <Image
                src="/images/common/tick-icon-large.png"
                width="112"
                height="112"
                alt="Success"
              />
              <h2 className="mt-4 text-lg font-semibold text-green-600">
                Pages Successfully Connected!
              </h2>
              <p className="mt-3 text-xs font-medium">
                Go back to App and resume campaign
              </p>
            </div>

            <div className="mt-8 flex items-center justify-center">
              <Image
                src="/images/common/facebook.svg"
                alt="Facebook"
                width="40"
                height="40"
                className="rounded-full"
              />
              <p className="text-sm ml-3">
                {loggedInUserDetails?.account_details?.name}
              </p>
            </div>

            <div className="mt-6 text-left">
              <h3 className="text-sm font-medium">Connected Pages:</h3>
              {loggedInUserDetails?.assigned_pages?.length ? (
                loggedInUserDetails?.assigned_pages?.map((item, index) => (
                  <div key={index} className="mt-3 px-3 flex items-center">
                    <p className="text-sm font-medium">
                      {index + 1}. {item.name}
                      <span
                        onClick={() =>
                          openUrlInNewTab(
                            `https://www.facebook.com/profile.php?id=${item.id}`,
                          )
                        }
                        className="text-sm text-hyperlink cursor-pointer ml-2"
                      >
                        ({item.id})
                      </span>
                    </p>
                  </div>
                ))
              ) : (
                <p className="text-sm font-medium text-red-500">
                  No Facebook Page found
                </p>
              )}
            </div>

            {loggedInUserDetails?.assigned_pages?.length ? (
              <div className="mt-6 text-left">
                <p className="text-xs">
                  For Social Media Ads, make sure you have accepted Facebook
                  T&Cs:{' '}
                  <span
                    onClick={() =>
                      openUrlInNewTab(
                        'https://www.facebook.com/ads/leadgen/tos',
                      )
                    }
                    className="text-hyperlink cursor-pointer"
                  >
                    Click here
                  </span>
                </p>
                <p className="text-xs mt-3">
                  For WhatsApp campaigns, link your WhatsApp business account to
                  Facebook Page:{' '}
                  <span
                    onClick={() =>
                      openUrlInNewTab(
                        'https://faq.whatsapp.com/***************/?cms_platform=android',
                      )
                    }
                    className="text-hyperlink cursor-pointer"
                  >
                    Click here
                  </span>
                </p>
              </div>
            ) : null}

            <Button
              className="mt-12 !py-2 !px-4 !text-sm bg-red-500 hover:bg-red-600 text-white"
              onClick={onFbLogoutClick}
            >
              Logout
            </Button>
          </div>
        ) : (
          <div className="flex flex-col items-center mt-8">
            <h2 className="text-xl font-medium text-center">
              Login to Facebook and connect your Pages with us
            </h2>
            <Button
              className="mt-8 !py-2 !px-4 !text-sm flex items-center bg-blue-600 hover:bg-blue-700 text-white"
              onClick={onFbLoginClick}
            >
              <div className="mr-2">
                <Image
                  src="/images/common/facebook.svg"
                  width="20"
                  height="20"
                  alt="fb-logo"
                />
              </div>
              <p>Login with Facebook</p>
            </Button>
          </div>
        )}

        {processLoginDetailsMutation.isLoading ? <FullScreenLoader /> : null}
      </div>
    </div>
  );
};

export default CampaignOnboardingConnectFacebookPages;
