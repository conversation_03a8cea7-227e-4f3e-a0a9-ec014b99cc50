import ContactUsComp from '@/components/home_pages/groweasy_old/ContactUsComp';
import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import NavigationHeader from '@/components/lib/NavigationHeader';
import Head from 'next/head';
import {
  GROWEASY_FAQS_SCHEMA,
  GROWEASY_ORG_SCHEMA,
} from 'src/constants/schemas';
import { getMetaTags } from 'src/utils';

const FAQs = () => {
  return (
    <div>
      <Head>{getMetaTags('/faqs')}</Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([GROWEASY_ORG_SCHEMA, GROWEASY_FAQS_SCHEMA]),
        }}
      />
      <NavigationHeader />
      <div className="px-4 py-4 sm:px-36">
        <h1 className="mt-6 text-2xl font-medium">
          Frequently Asked Questions
        </h1>
        {GROWEASY_FAQS_SCHEMA.mainEntity.map((item, index) => {
          return (
            <div key={index}>
              <h2 className="mt-8 text-lg">{item.name}</h2>
              <p
                className="mt-4 text-sm"
                dangerouslySetInnerHTML={{ __html: item.acceptedAnswer?.text }}
              />
            </div>
          );
        })}
        <ContactUsComp />
      </div>
      <FooterComp />
    </div>
  );
};

export default FAQs;
