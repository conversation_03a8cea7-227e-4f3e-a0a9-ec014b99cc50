import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import NavigationHeader from '@/components/lib/NavigationHeader';
import Head from 'next/head';
import Image from 'next/image';
import MARKETING_TERMS_DATA from 'src/constants/seo/data/marketing-terms';
import { getMetaTags } from 'src/utils';

const MarketingGlossaryEssentialTerms = () => {
  return (
    <div>
      <Head>{getMetaTags('/marketing-glossary-essential-terms')}</Head>
      <NavigationHeader />
      <div className="px-4 py-4 sm:px-36">
        <h1 className="mt-6 text-2xl font-medium">
          Marketing Glossary: Essential Terms Every Marketer Should Know
        </h1>

        {/* Render Categories and Terms */}
        <div className="mt-8 space-y-8">
          {MARKETING_TERMS_DATA.map((category) => (
            <div key={category.category}>
              <h2 className="text-xl font-semibold text-primary">
                {category.category}
              </h2>
              <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {category.terms.map((term) => (
                  <div
                    key={term.id}
                    className="rounded-lg border bg-white p-4 shadow-sm transition-all hover:shadow-md"
                  >
                    <h3 className="text-lg font-medium text-gray-900">
                      {term.term}
                    </h3>
                    <p className="mt-2 text-sm">{term.description}</p>
                    <div>
                      {term.image.url ? (
                        <Image
                          src={term.image.url}
                          alt={term.term}
                          className="mt-2 rounded-md"
                          width={term.image.width}
                          height={term.image.height}
                        />
                      ) : null}
                    </div>
                    <p className="mt-3 text-xs text-gray-600">
                      {term.detailed_description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
      <FooterComp />
    </div>
  );
};

export default MarketingGlossaryEssentialTerms;
