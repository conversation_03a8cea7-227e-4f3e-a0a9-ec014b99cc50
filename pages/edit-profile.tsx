import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig, IUserProfile } from 'src/types';
import BackIcon from '@/images/common/back-arrow.svg';
import MobileContainer from '@/components/lib/MobileContainer';
import { useMutation, useQuery } from 'react-query';
import { createOrUpdateUserProfile, getUserProfile } from 'src/actions/profile';
import { getCommonHeaders } from 'src/actions';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import Button from '@/components/lib/Button';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import { QueryParams } from 'src/constants';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { showToastMessage } from 'src/modules/toast';
import CountryCodeDropdown from '@/components/lib/CountryCodeDropdown';

interface IEditProfileProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

const EditProfile = (props: IEditProfileProps) => {
  const { user, partnerConfig } = props;

  const [userProfile, setUserProfile] = useState<IUserProfile | null>(null);

  const router = useRouter();

  const source = router.query[QueryParams.SOURCE] as string;
  const onboardingFlow = source === 'login';

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const createOrUpdateUserProfileMutation = useMutation(
    createOrUpdateUserProfile,
  );

  const userProfileResponse = useQuery(
    'getUserProfile',
    () => {
      return getUserProfile({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'EditProfile.getUserProfile');
      },
      onSuccess: (response) => {
        setUserProfile(response.data);
        // for repeat users coming from login, skip this step
        if (response.data?.mobile && onboardingFlow) {
          void router.push('/dashboard');
        }
      },
    },
  );

  const onUserProfileChange = (
    key: keyof IUserProfile,
    value: string | boolean,
  ) => {
    const userProfileCopy = {
      ...userProfile,
      [key]: value,
    };
    setUserProfile(userProfileCopy);
  };

  const onSaveDetailsClick = () => {
    // CTA will be disabled for name & mobile, no need to verify again
    if (
      !userProfile?.number_of_employees ||
      !userProfile?.has_calling_team ||
      !userProfile?.monthly_marketing_budget
    ) {
      showToastMessage('Please fill in all the details', 'error');
      return;
    }
    logEvent(EVENT_NAMES.save_user_profile_clicked);
    // set default values
    if (userProfile.mobile_dial_code === undefined) {
      userProfile.mobile_dial_code = '+91';
    }
    if (userProfile.is_affiliate_marketing === undefined) {
      userProfile.is_affiliate_marketing = false;
    }
    if (userProfile.whatsapp_opt_in === undefined) {
      userProfile.whatsapp_opt_in = true;
    }
    // for first time login (which is signup), set acquisition source
    if (onboardingFlow && !userProfile.acquisition_source) {
      userProfile.acquisition_source = window?.platform;
    }
    // update partner, only one time
    if (!userProfile.partner && partnerConfig?.partner) {
      userProfile.partner = partnerConfig.partner;
    }

    createOrUpdateUserProfileMutation
      .mutateAsync({
        queryParams: router.query as Record<string, string>,
        headers: getCommonHeaders(user),
        data: userProfile,
      })
      .then(() => {
        // for edit profile flow, there is back button
        if (onboardingFlow) {
          void router.push('/dashboard');
        }
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(error, 'EditProfile.onSaveDetailsClick');
      });
  };

  const onBackPressed = () => {
    router.back();
  };

  return user ? (
    <MobileContainer>
      {onboardingFlow && userProfileResponse.isLoading ? (
        <div className="w-full h-full flex items-center justify-center">
          <SpinnerLoader />
        </div>
      ) : (
        <div className="flex flex-col flex-1 w-full bg-white h-full">
          {onboardingFlow ? (
            <div className="p-4 mt-4">
              <p className="text-black text-lg">
                Great! Welcome to {partnerConfig?.name ?? 'GrowEasy'}
              </p>
              <p className="mt-3 text-gray-dark">
                Please enter your business details for which you want to
                generate leads
              </p>
            </div>
          ) : (
            <div className="flex items-center mt-4">
              <div className="p-4 cursor-pointer" onClick={onBackPressed}>
                <BackIcon />
              </div>
              <p className="text-black text-lg">Edit Profile</p>
              <div className="flex-1" />
            </div>
          )}
          <div className="mt-3 mb-3 flex flex-col px-4 flex-1 overflow-y-scroll no-scrollbar h-full">
            <p className="block text-sm font-medium text-gray-700 mb-1">
              Name of business
            </p>
            <div className="mt-1 flex">
              <input
                className="outline-none border border-gray-medium rounded-lg px-2 py-2 text-sm w-full"
                type="text"
                onChange={(event) =>
                  onUserProfileChange('business_name', event.target.value)
                }
                value={userProfile?.business_name ?? ''}
              />
            </div>

            <div className="flex items-center mt-6">
              <p className="block text-sm font-medium text-gray-700 mb-1 mr-1">
                Enter your business phone number
              </p>
            </div>
            <div className="mt-1 flex items-stretch gap-2">
              <CountryCodeDropdown
                countryCode={userProfile?.mobile_dial_code || '+91'}
                setCountryCode={(value) =>
                  onUserProfileChange('mobile_dial_code', value as string)
                }
              />
              <input
                className="outline-none border border-gray-medium rounded-lg px-2 py-2 text-sm w-full"
                type="number"
                onChange={(event) =>
                  onUserProfileChange('mobile', event.target.value)
                }
                value={userProfile?.mobile ?? ''}
                placeholder="7206XXXXXX"
              />
            </div>

            <div className="mt-6">
              <p className="block text-sm font-medium text-gray-700 mb-1">
                How many employees does your business have?
              </p>
              <div className="mt-1 flex">
                <input
                  className="outline-none border border-gray-medium rounded-lg px-2 py-2 text-sm"
                  type="text"
                  onChange={(event) =>
                    onUserProfileChange(
                      'number_of_employees',
                      event.target.value,
                    )
                  }
                  value={userProfile?.number_of_employees ?? ''}
                />
              </div>
            </div>

            <div className="mt-6">
              <p className="block text-sm font-medium text-gray-700 mb-1">
                What is your monthly marketing budget?
              </p>
              <div className="mt-1 flex">
                <input
                  className="outline-none border border-gray-medium rounded-lg px-2 py-2 text-sm"
                  type="text"
                  onChange={(event) =>
                    onUserProfileChange(
                      'monthly_marketing_budget',
                      event.target.value,
                    )
                  }
                  value={userProfile?.monthly_marketing_budget ?? ''}
                  placeholder="0"
                />
              </div>
            </div>

            <div className="flex items-center mt-6">
              <p className="block text-sm font-medium text-gray-700 mb-1 mr-1">
                Is your business into Affiliate/Network marketing?
              </p>
            </div>
            <div className="mt-1 flex">
              <input
                type="radio"
                value="yes"
                className="mr-2"
                checked={!!userProfile?.is_affiliate_marketing}
                onChange={() =>
                  onUserProfileChange('is_affiliate_marketing', true)
                }
              />
              <span className="text-sm mr-5">Yes</span>
              <input
                type="radio"
                value="no"
                className="mr-2"
                checked={!userProfile?.is_affiliate_marketing}
                onChange={() =>
                  onUserProfileChange('is_affiliate_marketing', false)
                }
              />
              <span className="text-sm mr-5">No</span>
            </div>

            <div className="flex items-center mt-6">
              <p className="block text-sm font-medium text-gray-700 mb-1 mr-1">
                Do you have a dedicated calling team or telecallers?
              </p>
            </div>
            <div className="mt-1 flex">
              {[
                { label: 'Yes', value: 'yes' },
                { label: 'No', value: 'no' },
                { label: 'Online sales only', value: 'online_sales' },
              ].map((item, index) => {
                return (
                  <div key={index} className="flex items-center">
                    <input
                      type="radio"
                      value="yes"
                      className="mr-2"
                      checked={userProfile?.has_calling_team === item.value}
                      onChange={() =>
                        onUserProfileChange('has_calling_team', item.value)
                      }
                    />
                    <span className="text-sm mr-5">{item.label}</span>
                  </div>
                );
              })}
            </div>

            <div className="flex items-center mt-6">
              <input
                type="checkbox"
                checked={
                  userProfile?.whatsapp_opt_in === undefined ||
                  userProfile?.whatsapp_opt_in
                }
                onChange={() =>
                  onUserProfileChange(
                    'whatsapp_opt_in',
                    !userProfile?.whatsapp_opt_in,
                  )
                }
              />
              <p className="block text-sm font-medium text-gray-700 mb-1 ml-3">
                I agree to receive updates and promotions via WhatsApp.
              </p>
            </div>
          </div>
          <div className="px-4 w-full flex flex-col my-3">
            <Button
              onClick={onSaveDetailsClick}
              disabled={!userProfile?.mobile || !userProfile?.business_name}
              className="w-full"
            >
              <p>Save</p>
            </Button>
          </div>
          {userProfileResponse.isLoading ||
          createOrUpdateUserProfileMutation.isLoading ? (
            <FullScreenLoader />
          ) : null}
        </div>
      )}
    </MobileContainer>
  ) : null;
};

export default EditProfile;
