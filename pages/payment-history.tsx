import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser } from 'src/types';
import BackIcon from '@/images/common/back-arrow.svg';
import MobileContainer from '@/components/lib/MobileContainer';
import { useQuery } from 'react-query';
import { getOrders } from 'src/actions/profile';
import { getCommonHeaders } from 'src/actions';
import {
  formatCurrencyAmount,
  getCurrencySymbol,
  getFormattedDateString,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import NoDataFound from '@/components/lib/NoDataFound';
import classNames from 'classnames';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { FaCalendarAlt, FaRegCheckCircle, FaRegClock } from 'react-icons/fa';
import { BiRefresh } from 'react-icons/bi';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { MdBarChart, MdList } from 'react-icons/md';
import { FaCaretRight } from 'react-icons/fa6';
import { Currency } from 'src/types/campaigns';

interface IPaymentHistoryProps {
  user?: IGroweasyUser;
}

const PaymentHistory = (props: IPaymentHistoryProps) => {
  const { user } = props;
  const router = useRouter();
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [isFilterApplied, setIsFilterApplied] = useState(false);
  const [showChart, setShowChart] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [activeFilters, setActiveFilters] = useState<{
    start_date?: string;
    end_date?: string;
  }>({});

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const ordersResponse = useQuery(
    ['getOrders', activeFilters],
    () => {
      const queryParams = {
        ...(router.query as Record<string, string>),
        ...activeFilters,
      };

      return getOrders({
        headers: getCommonHeaders(user),
        queryParams,
      });
    },
    {
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'PaymentHistory.getOrders');
      },
    },
  );

  const onBackPressed = () => {
    router.back();
  };

  const resetFilters = () => {
    setStartDate(null);
    setEndDate(null);
    setIsFilterApplied(false);
    setActiveFilters({});
  };

  const applyFilters = () => {
    const filters: {
      start_date?: string;
      end_date?: string;
    } = {};

    if (startDate) {
      // this will give 1 day ago date when used in timezone like +5:30
      //filters.start_date = startDate.toISOString().split('T')[0];
      filters.start_date = startDate.toLocaleDateString('en-CA'); // 'en-CA' locale gives you the ISO-style format (YYYY-MM-DD) in local time.
    }

    if (endDate) {
      const endOfDay = new Date(endDate);
      endOfDay.setDate(endOfDay.getDate() + 1);
      endOfDay.setHours(23, 59, 59, 999);
      filters.end_date = endOfDay.toISOString().split('T')[0];
    }
    setActiveFilters(filters);
    setIsFilterApplied(Object.keys(filters).length > 0);
  };

  const orders = ordersResponse?.data?.data ?? null;

  const filteredOrders = orders;

  const chartData = React.useMemo(() => {
    if (!filteredOrders)
      return {
        daysData: [],
        totalsByCurrency: {},
      };

    const monthStart = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      1,
    );
    const monthEnd = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth() + 1,
      0,
      23,
      59,
      59,
      999,
    );

    const monthlyOrders = filteredOrders.filter((order) => {
      const orderDate = new Date(order.created_at._seconds * 1000);
      return orderDate >= monthStart && orderDate <= monthEnd;
    });

    const daysInMonth = new Array(monthEnd.getDate()).fill(0).map((_, i) => {
      return {
        day: `${i + 1} ${monthStart.toLocaleString('default', {
          month: 'short',
        })}`,
        sales: 0,
        amount: 0,
      };
    });

    const totalsByCurrency: Record<string, number> = {};

    monthlyOrders.forEach((order) => {
      const date = new Date(order.created_at._seconds * 1000);
      if (date >= monthStart && date <= monthEnd) {
        const dayIndex = date.getDate() - 1;
        daysInMonth[dayIndex].sales += 1;
        daysInMonth[dayIndex].amount += order.amount / 100;

        const currency = order.currency || 'unknown';
        totalsByCurrency[currency] =
          (totalsByCurrency[currency] || 0) + order.amount / 100;
      }
    });

    return {
      daysData: daysInMonth,
      totalsByCurrency,
    };
  }, [filteredOrders, currentMonth]);

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    if (direction === 'prev') {
      newMonth.setMonth(newMonth.getMonth() - 1);
    } else {
      newMonth.setMonth(newMonth.getMonth() + 1);
    }
    setCurrentMonth(newMonth);
  };

  const toggleChartView = () => {
    setShowChart(!showChart);
    if (!showChart) {
      setCurrentMonth(new Date());
    }
  };

  const formatMonthYear = (date: Date) => {
    return date.toLocaleString('default', {
      month: 'short',
      year: 'numeric',
    });
  };

  return user ? (
    <MobileContainer>
      <div className="flex flex-col flex-1 w-full h-full">
        <div className="flex items-center mt-4 px-4 border-b border-gray-light pb-2">
          <div
            className="p-2 cursor-pointer hover:bg-primary-light rounded-full transition-all duration-200"
            onClick={onBackPressed}
          >
            <BackIcon className="w-5 h-5" />
          </div>
          <p className="text-lg font-semibold font-poppins ml-2">
            Payment History
          </p>
        </div>

        <div className="p-4 pb-2 flex flex-col">
          <p className="text-xs font-medium mb-2 opacity-60">
            Filter by date range
          </p>
          <div className="flex items-center gap-2 justify-between">
            <div className="flex gap-2">
              <div className="flex-1">
                <DatePicker
                  showIcon
                  icon={<FaCalendarAlt />}
                  calendarIconClassName="!w-3 !h-3"
                  selected={startDate}
                  onChange={(date: Date | null) => setStartDate(date)}
                  selectsStart
                  startDate={startDate}
                  endDate={endDate}
                  placeholderText="Start date"
                  className="w-full border border-gray-light rounded-lg md:text-xs text-xxs focus:outline-none focus:ring-1 focus:ring-primary"
                  popperPlacement="bottom-start"
                />
              </div>
              <div className="flex-1">
                <DatePicker
                  showIcon
                  icon={<FaCalendarAlt />}
                  calendarIconClassName="!w-3 !h-3"
                  selected={endDate}
                  onChange={(date: Date | null) => setEndDate(date)}
                  selectsEnd
                  startDate={startDate}
                  endDate={endDate}
                  minDate={startDate}
                  placeholderText="End date"
                  className="w-full border border-gray-light rounded-lg md:text-xs text-xxs focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>
            </div>
            <div className="flex items-center gap-1">
              <button
                title="Reset filters"
                type="button"
                onClick={resetFilters}
                className="px-3 py-1 text-xs border border-gray-light rounded-lg text-gray-dark hover:bg-red/10 transition-all duration-200 active:scale-95"
              >
                <BiRefresh size={18} />
              </button>
              <button
                type="button"
                onClick={applyFilters}
                className="px-3 py-1.5 text-xs bg-primary text-white rounded-lg hover:bg-primary2 transition-all duration-200"
              >
                Apply
              </button>
            </div>
          </div>
        </div>

        {isFilterApplied && (
          <div className="mx-4 text-xxs mb-3">
            <div className="flex items-center bg-primary-light bg-opacity-50 rounded-lg px-4 py-2.5 gap-1">
              <p className=" text-primary font-medium">
                {startDate && endDate
                  ? `Showing results from ${getFormattedDateString(
                      startDate,
                    )} to ${getFormattedDateString(endDate)}`
                  : startDate
                    ? `Showing results from ${getFormattedDateString(
                        startDate,
                      )}`
                    : endDate
                      ? `Showing results until ${getFormattedDateString(
                          endDate,
                        )}`
                      : ''}
              </p>
              <div className="flex-1" />
              <button
                type="button"
                onClick={resetFilters}
                className="text-xxs text-primary font-medium hover:underline transition-all duration-200"
              >
                Clear
              </button>
            </div>
          </div>
        )}

        {orders ? (
          <>
            <div className="flex justify-end px-4 mb-2">
              <button
                type="button"
                onClick={toggleChartView}
                className="flex items-center gap-1 text-xs bg-primary text-white rounded-lg px-3 py-1.5 hover:bg-primary2 transition-all duration-200"
              >
                {showChart ? (
                  <>
                    <MdList size={16} /> Show List
                  </>
                ) : (
                  <>
                    <MdBarChart size={16} /> Show Chart
                  </>
                )}
              </button>
            </div>

            {showChart ? (
              <div className="px-4 flex flex-col">
                <div className="flex items-center justify-between mb-2">
                  <button
                    title="Previous month"
                    onClick={() => navigateMonth('prev')}
                    className="p-1 hover:bg-primary-light rounded-full transition-all duration-200"
                  >
                    <FaCaretRight className="rotate-180" size={22} />
                  </button>
                  <h3 className="text-sm font-medium text-gray-800">
                    {formatMonthYear(currentMonth)}
                  </h3>
                  <button
                    title="Next month"
                    onClick={() => navigateMonth('next')}
                    className="p-1 hover:bg-primary-light rounded-full transition-all duration-200"
                  >
                    <FaCaretRight size={22} />
                  </button>
                </div>

                <div className="h-60 mb-4 bg-white rounded-xl border border-gray-light p-3 shadow-sm">
                  {chartData.daysData && chartData.daysData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={chartData.daysData}
                        margin={{ top: 10, right: 10, left: 0, bottom: 20 }}
                      >
                        <defs>
                          <linearGradient
                            id="colorSales"
                            x1="0"
                            y1="0"
                            x2="0"
                            y2="1"
                          >
                            <stop
                              offset="5%"
                              stopColor="#289F9F"
                              stopOpacity={0.8}
                            />
                            <stop
                              offset="95%"
                              stopColor="#E2F1F1"
                              stopOpacity={0.2}
                            />
                          </linearGradient>
                        </defs>
                        <CartesianGrid
                          strokeDasharray="3 3"
                          vertical={false}
                          opacity={0.3}
                        />
                        <XAxis
                          dataKey="day"
                          tickLine={false}
                          axisLine={false}
                          tick={{ fontSize: 10 }}
                          interval="preserveStartEnd"
                        />
                        <YAxis
                          tickLine={false}
                          axisLine={false}
                          tick={{ fontSize: 12 }}
                          allowDecimals={false}
                        />
                        <Tooltip
                          formatter={(value) => [
                            `${String(value)} payment(s)`,
                            'Payments',
                          ]}
                          labelFormatter={(label) => `${label}`}
                          contentStyle={{
                            borderRadius: '10px',
                            border: 'none',
                            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="sales"
                          stroke="#289F9F"
                          strokeWidth={2}
                          fillOpacity={1}
                          fill="url(#colorSales)"
                          activeDot={{ r: 6, strokeWidth: 0 }}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <p className="text-gray-dark">
                        No payments for {formatMonthYear(currentMonth)}
                      </p>
                    </div>
                  )}
                </div>

                <div className="mb-4 bg-white rounded-xl border border-gray-light overflow-hidden">
                  <div className="px-4 py-3 bg-primary-light/20 border-b border-gray-light">
                    <h4 className="text-xs font-semibold text-primary">
                      Monthly Summary
                    </h4>
                  </div>
                  <div className="px-4 py-3 border flex items-start gap-8 flex-wrap">
                    <div className="flex justify-between items-center mb-3">
                      <div>
                        <p className="text-xs text-gray-600">Total Payments</p>
                        <p className="text-lg font-semibold text-gray-800">
                          {chartData.daysData.reduce(
                            (sum, day) => sum + day.sales,
                            0,
                          )}
                        </p>
                      </div>
                    </div>

                    {Object.keys(chartData.totalsByCurrency).length > 0 && (
                      <div>
                        <p className="text-xs text-gray-600 mb-2">
                          Total Amount by Currency
                        </p>
                        <div className="space-y-2">
                          {Object.entries(chartData.totalsByCurrency).map(
                            ([currency, amount]) => (
                              <div
                                key={currency}
                                className="flex items-center justify-between"
                              >
                                <p className="text-sm font-medium text-gray-700">
                                  {getCurrencySymbol(currency as Currency)}{' '}
                                  {currency.toUpperCase()} :
                                </p>
                                <p className="text-sm font-semibold text-primary">
                                  {getCurrencySymbol(currency as Currency)}{' '}
                                  {amount.toFixed(2)}
                                </p>
                              </div>
                            ),
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="mt-2 mb-3 flex flex-col px-4 flex-1 overflow-y-scroll no-scrollbar">
                {filteredOrders &&
                  filteredOrders.map((item, index) => {
                    const creationDate = new Date(
                      item.created_at._seconds * 1000,
                    );
                    const isPaid = item.status === 'paid';

                    return (
                      <div
                        key={index}
                        className="mb-1 py-3 px-4 rounded-xl border border-gray-light bg-white"
                      >
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium min-w-28">
                            {getFormattedDateString(creationDate)}
                          </p>
                          <div className="flex items-center">
                            {isPaid ? (
                              <FaRegCheckCircle
                                className="text-green-dark mr-2"
                                size={16}
                              />
                            ) : (
                              <FaRegClock
                                className="text-yellow-dark mr-2"
                                size={16}
                              />
                            )}
                            <p
                              className={classNames(
                                'text-xs py-1 px-2.5 rounded-full border font-medium',
                                {
                                  'bg-green-light text-green-dark border-primary2/30':
                                    isPaid,
                                  'bg-yellow-light text-yellow-dark border-secondary/30':
                                    !isPaid,
                                },
                              )}
                            >
                              {item.status.toUpperCase()}
                            </p>
                          </div>
                          <p className="text-sm font-semibold text-primary min-w-20 text-end">
                            {formatCurrencyAmount(
                              item.amount /
                                ([Currency.IDR, Currency.VND].includes(
                                  item?.currency,
                                )
                                  ? 1
                                  : 100),
                              item.currency,
                            )}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                {filteredOrders && filteredOrders.length === 0 ? (
                  <div className="flex justify-center mt-8">
                    <NoDataFound
                      illustration={{
                        url: '/images/dashboard/payment-illustration.svg',
                        width: 200,
                        height: 200,
                      }}
                      title="No Payments Found!"
                      className="bg-primary-light bg-opacity-20 p-8 rounded-xl"
                    />
                  </div>
                ) : null}
              </div>
            )}
          </>
        ) : (
          <div className="flex items-center justify-center h-full">
            <SpinnerLoader />
          </div>
        )}
      </div>
    </MobileContainer>
  ) : null;
};

export default PaymentHistory;
