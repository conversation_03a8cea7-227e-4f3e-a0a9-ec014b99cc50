import OnboardingStepsComp from '@/components/blogs/OnboardingStepsComp';
import ContactUsComp from '@/components/home_pages/groweasy_old/ContactUsComp';
import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import NavigationHeader from '@/components/lib/NavigationHeader';
import { GetServerSidePropsContext } from 'next';
import Head from 'next/head';
import {
  LEAD_GEN_SEO_CITIES_LIST,
  WHY_GROWEASY_LEAD_GEN_VALUE_PROPS,
} from 'src/constants/seo';

interface ILeadGenerationInCityProps {
  city: string;
}

const FAQS = [
  {
    title: 'How does GrowEasy generate leads in minutes?',
    description:
      'GrowEasy harnesses advanced AI algorithms to streamline lead generation processes, enabling rapid campaign setup and real-time lead acquisition.',
  },
  {
    title:
      'How does GrowEasy ensure Qualified Leads for my business in <CITY>?',
    description:
      'We prioritize quality over quantity. GrowEasy utilizes sophisticated targeting parameters and AI-driven audience selection to deliver highly relevant and qualified leads tailored to your business needs in <CITY>.',
  },
  {
    title: 'Do I need marketing skills to boost my business in <CITY>?',
    description:
      'No marketing expertise is required! GrowEasy simplifies lead generation with user-friendly tools and intuitive interfaces, enabling businesses of all sizes to achieve marketing success in <CITY>.',
  },
  {
    title: 'How much does it cost to start receiving Leads in <CITY>?',
    description:
      'Start your lead generation journey with GrowEasy for as little as Rs 1000. Our flexible pricing options cater to businesses of all scales in <CITY>.',
  },
];

const INDUSTRIES_LIST = [
  'Real Estate',
  'Higher Education',
  'IT Services',
  'Construction Companies',
  'E-Learning Platforms',
  'Advertising and Marketing',
];

const LeadGenerationInCity = (props: ILeadGenerationInCityProps) => {
  const { city } = props;

  const cityDetails = LEAD_GEN_SEO_CITIES_LIST.find(
    (item) => item.city.toLowerCase() === city.toLowerCase(),
  );

  const getCityName = () => {
    const firstLetter = city?.[0].toUpperCase();
    return ` ${firstLetter}${city?.slice(1)}`;
  };

  return (
    <div>
      <Head>
        <title key="title">{`How to generate leads using AI in ${getCityName()} - GrowEasy`}</title>
        <meta
          name="description"
          key="description"
          content={`Discover how AI-powered lead generation can transform your business in ${getCityName()}. Launch targeted campaigns effortlessly with GrowEasy's AI tools. Start generating quality leads and growing your business today!`}
        />
        <link
          rel="canonical"
          key="canonical"
          href={`https://groweasy.ai/lead-generation/cities/${city?.toLowerCase()}`}
        />
        <meta
          property="og:url"
          key="og:url"
          content={`https://groweasy.ai/lead-generation/cities/${city?.toLowerCase()}`}
        />
      </Head>
      <NavigationHeader />
      <div className="px-4 py-4 sm:px-36">
        <h1 className="mt-6 text-3xl font-medium">
          How to generate leads using AI in
          {getCityName()}?
        </h1>
        <p className="mt-4 text-md">
          Whether you are looking to expand your business presence or increase
          customer engagement in {getCityName()}, GrowEasy is here to empower
          you with AI-driven lead generation solutions tailored to your needs.
        </p>

        <h2 className="text-2xl font-medium mt-8">About {getCityName()}</h2>
        <p className="mt-3 text-sm">{cityDetails?.description ?? ''}</p>

        <h3 className="text-xl font-medium mt-8">
          Why Choose GrowEasy for Lead Generation in {getCityName()}?
        </h3>
        <div>
          {WHY_GROWEASY_LEAD_GEN_VALUE_PROPS.map((item, index) => {
            return (
              <p className="mt-3 text-sm" key={index}>
                <span className="font-semibold">{item.title}: </span>
                {item.description.replaceAll('<CITY>', getCityName())}
              </p>
            );
          })}
        </div>

        <h3 className="text-xl font-medium mt-8">
          Which businesses benefit from GrowEasy in {getCityName()}?
        </h3>
        <p className="mt-3 text-sm">
          GrowEasy serves a diverse range of businesses in {getCityName()},
          including but not limited to:
        </p>
        <div className="ml-2">
          {INDUSTRIES_LIST.map((industry, index) => {
            return (
              <p key={index} className="mt-1 text-sm">
                {index + 1}. {industry}
              </p>
            );
          })}
        </div>
        <p className="mt-3 text-sm">
          Our DIY platform allows businesses from various industries to initiate
          lead generation effortlessly.
        </p>

        <h3 className="text-xl font-medium mt-8">
          What are the steps to generate leads in {getCityName()}?
        </h3>
        <OnboardingStepsComp />

        <h3 className="text-xl font-medium mt-8">Frequently Asked Questions</h3>
        <div>
          {FAQS.map((item, index) => {
            return (
              <div key={index}>
                <p className="mt-5 text-sm font-semibold">
                  {item.title.replaceAll('<CITY>', getCityName())}
                </p>
                <p className="mt-3 text-sm">
                  {item.description.replaceAll('<CITY>', getCityName())}
                </p>
              </div>
            );
          })}
        </div>
        <ContactUsComp />
      </div>
      <FooterComp />
    </div>
  );
};

export const getServerSideProps = (context: GetServerSidePropsContext) => {
  const slug = context?.params?.['slug'] as string;
  return { props: { city: slug } };
};

export default LeadGenerationInCity;
