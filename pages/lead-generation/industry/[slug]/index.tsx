import OnboardingStepsComp from '@/components/blogs/OnboardingStepsComp';
import ContactUsComp from '@/components/home_pages/groweasy_old/ContactUsComp';
import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import NavigationHeader from '@/components/lib/NavigationHeader';
import { GetServerSidePropsContext } from 'next';
import Head from 'next/head';
import Link from 'next/link';
import {
  LEAD_GEN_SEO_INDUSTRIES_LIST,
  WHY_GROWEASY_LEAD_GEN_VALUE_PROPS,
} from 'src/constants/seo';

interface ILeadGenerationInCityProps {
  slug: string;
}

const FAQS = [
  {
    title: 'How does GrowEasy generate leads in minutes?',
    description:
      'GrowEasy uses advanced AI to quickly set up and run targeted ads on Facebook, Instagram, and Google, generating quality leads in real-time.',
  },
  {
    title: 'How does GrowEasy ensure Quality Leads for my <INDUSTRY> business?',
    description:
      'We prioritize quality over quantity. GrowEasy utilizes sophisticated targeting parameters and AI-driven audience selection to deliver highly relevant and qualified leads tailored to your <INDUSTRY> business.',
  },
  {
    title: 'Do I need marketing skills to boost my <INDUSTRY> business?',
    description:
      'No marketing expertise is required! GrowEasy simplifies lead generation with user-friendly tools and intuitive interfaces, enabling businesses of all sizes to achieve marketing success in <INDUSTRY>.',
  },
  {
    title:
      'How much does it cost to start receiving Leads in <INDUSTRY> business?',
    description:
      'Start your lead generation journey with GrowEasy for as little as Rs 1000. Our flexible pricing options cater to businesses of all scales in <INDUSTRY>.',
  },
];

const LeadGenerationInCity = (props: ILeadGenerationInCityProps) => {
  const { slug } = props;

  const industryDetails = LEAD_GEN_SEO_INDUSTRIES_LIST.find(
    (item) => item.slug === slug,
  );

  const getIndustryName = () => {
    return industryDetails.industry;
  };

  return (
    <div>
      <Head>
        <title key="title">{`How to generate quality leads using AI in ${getIndustryName()} - GrowEasy`}</title>
        <meta
          name="description"
          key="description"
          content={`Discover how AI-powered lead generation can transform your business in ${getIndustryName()}. Launch targeted ad campaigns on Facebook, Instagram, and Google effortlessly with GrowEasy's AI tools. Start generating quality leads and grow your business today!`}
        />
        <link
          rel="canonical"
          key="canonical"
          href={`https://groweasy.ai/lead-generation/industry/${slug}`}
        />
        <meta
          property="og:url"
          key="og:url"
          content={`https://groweasy.ai/lead-generation/industry/${slug}`}
        />
      </Head>
      <NavigationHeader />
      <div className="px-4 py-4 sm:px-36">
        <h1 className="mt-6 text-3xl font-medium">
          How to generate quality leads using AI in {getIndustryName()}?
        </h1>
        <p className="mt-4 text-md">
          Whether you are looking to expand your business presence or increase
          customer engagement in {getIndustryName()}, GrowEasy is here to
          empower you with AI-driven lead generation solutions tailored to your
          needs.
        </p>

        <h2 className="text-2xl font-medium mt-8">
          About {getIndustryName()} industry
        </h2>
        <p className="mt-3 text-sm">{industryDetails?.description ?? ''}</p>

        <h3 className="text-xl font-medium mt-8">
          Why Choose GrowEasy for Lead Generation in {getIndustryName()}?
        </h3>
        <div>
          {WHY_GROWEASY_LEAD_GEN_VALUE_PROPS.map((item, index) => {
            return (
              <p className="mt-3 text-sm" key={index}>
                <span className="font-semibold">{item.title}: </span>
                {item.description.replaceAll('<CITY>', getIndustryName())}
              </p>
            );
          })}
        </div>

        <h3 className="text-xl font-medium mt-8">
          What are the steps to generate leads in {getIndustryName()}?
        </h3>
        <OnboardingStepsComp />
        <p className="mt-5 text-sm">
          Our DIY platform allows any business person to initiate lead
          generation effortlessly.
        </p>

        <h3 className="text-xl font-medium mt-8">Frequently Asked Questions</h3>
        <div>
          {FAQS.map((item, index) => {
            return (
              <div key={index}>
                <p className="mt-5 text-sm font-semibold">
                  {item.title.replaceAll('<INDUSTRY>', getIndustryName())}
                </p>
                <p className="mt-3 text-sm">
                  {item.description.replaceAll('<INDUSTRY>', getIndustryName())}
                </p>
              </div>
            );
          })}
        </div>
        {industryDetails.related?.length ? (
          <div>
            <h3 className="text-xl font-medium mt-8">Related</h3>
            {industryDetails.related.map((item, index) => {
              return (
                <p key={index} className="mt-3 text-sm text-hyperlink">
                  <Link href={item.href}>{item.name}</Link>
                </p>
              );
            })}
          </div>
        ) : null}
        <ContactUsComp />
      </div>
      <FooterComp />
    </div>
  );
};

export const getServerSideProps = (context: GetServerSidePropsContext) => {
  const slug = context?.params?.['slug'] as string;
  return { props: { slug } };
};

export default LeadGenerationInCity;
