import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import MobileContainer from '@/components/lib/MobileContainer';
import { CAMPAIGN_DETAILS_TABS, QueryParams } from 'src/constants';
import { useQuery } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import {
  getGoogleCampaignInsightDetails,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import { getCampaignDetails } from 'src/actions/onboarding';
import BackIcon from '@/images/common/back-arrow.svg';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import classNames from 'classnames';

import {
  getGoogleInsights,
  getGoogleLeads,
  getGoogleMediaAssetsDetails,
} from 'src/actions/campaign_details';
import CampaignStatusComp from '@/components/dashboard/campaigns/CampaignStatusComp';
import GoogleIcon from '@/images/common/google.svg';
import GoogleCampaignInfoTab from '@/components/campaign_details/google/GoogleCampaignInfoTab';
import { getLeadsCrmRecords } from 'src/actions/crm';
import { ILeadsCrmDetails } from 'src/types/leads_crm';
import EditLeadsCrmDetailsBs from '@/components/campaign_details/bottom_sheets/EditLeadsCrmDetailsBs';
import CampaignLeadsTab from '@/components/campaign_details/CampaignLeadsTab';
import EditCampaignPostLaunchBs from '@/components/campaign_details/bottom_sheets/EditCampaignPostLaunchBs';
import { GROWEASY_CAMPAIGN_TYPE } from 'src/types/campaigns';

interface IGoogleCampaignDetailsProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

const GoogleCampaignDetails = (props: IGoogleCampaignDetailsProps) => {
  const { user, partnerConfig } = props;

  const router = useRouter();

  const campaignId = router.query[QueryParams.CAMPAIGN_ID];

  const [selectedTabId, setSelectedTabId] = useState(
    CAMPAIGN_DETAILS_TABS[0].id,
  );

  const [showEditCampaignPostLaunchBs, setShowEditCampaignPostLaunchBs] =
    useState(false);

  const [selectedLeadsCrmDetails, setSelectedLeadsCrmDetails] =
    useState<Partial<ILeadsCrmDetails> | null>(null);

  const campaignDetailsResponse = useQuery(
    ['getCampaignDetails', campaignId],
    () => {
      return getCampaignDetails({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled: !!campaignId,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'GoogleCampaignDetails.getCampaignDetails',
        );
      },
    },
  );

  const campaignDetails = campaignDetailsResponse?.data?.data ?? null;
  const googleAdsData = campaignDetails?.google_ads_data;

  const mediaAssetsDetailsResponse = useQuery(
    ['getGoogleMediaAssetsDetails', campaignId],
    () => {
      return getGoogleMediaAssetsDetails({
        headers: getCommonHeaders(user),
        queryParams: {
          ...router.query,
          [QueryParams.ASSET_RESOURCE_NAMES]: [
            ...(googleAdsData?.media_assets?.MARKETING_IMAGE ?? []),
            ...(googleAdsData?.media_assets?.YOUTUBE_VIDEO ?? []),
            ...(googleAdsData?.media_assets?.SQUARE_MARKETING_IMAGE ?? []),
            ...(googleAdsData?.media_assets?.PORTRAIT_MARKETING_IMAGE ?? []),
          ]
            ?.map((item) => item.resource_name)
            ?.join(','),
        } as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled:
        !!googleAdsData?.media_assets &&
        campaignDetails.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'GoogleCampaignDetails.getGoogleMediaAssetsDetails',
        );
      },
    },
  );

  const insightsResponse = useQuery(
    ['getGoogleInsights', campaignId],
    () =>
      getGoogleInsights({
        queryParams: {
          ...router.query,
          [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignDetails?.google_ads_data?.campaign_resource,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'GoogleCampaignDetails.getGoogleInsights',
        );
      },
    },
  );

  const googleLeadsResponse = useQuery(
    ['getGoogleLeads', campaignId],
    () =>
      getGoogleLeads({
        queryParams: {
          ...router.query,
          [QueryParams.CAMPAIGN_ID]: campaignDetails?.id,
          [QueryParams.LIMIT]: '500',
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignDetails?.id,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'GoogleCampaignDetails.getGoogleLeads',
        );
      },
    },
  );

  const leadsCrmResponse = useQuery(
    ['getLeadsCrmRecords', campaignId],
    () =>
      getLeadsCrmRecords({
        queryParams: {
          ...router.query,
          [QueryParams.CAMPAIGN_ID]: campaignId,
          [QueryParams.LIMIT]: '1000',
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignId,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'GoogleCampaignDetails.getLeadsCrmRecords',
        );
      },
    },
  );

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const onBackPressed = () => {
    router.back();
  };

  const onEditLeadsCrmClick = (details: Partial<ILeadsCrmDetails>) => {
    details.uid = user.uid;
    details.campaign_id = campaignDetails.id;

    setSelectedLeadsCrmDetails(details);
  };

  const campaignName =
    campaignDetails?.friendly_name ??
    campaignDetails?.details?.business_details?.business_category ??
    '';
  const mediaAssetsDetails = mediaAssetsDetailsResponse?.data?.data ?? [];
  const campaignInsightsDetails = insightsResponse?.data?.data?.campaign
    ? getGoogleCampaignInsightDetails({
        insightsArr: insightsResponse?.data?.data?.campaign,
      })
    : null;
  const keywordsInsightsDetails = insightsResponse?.data?.data?.keywords;
  const locationsInsightsDetails = insightsResponse?.data?.data?.locations;
  const callInsightsDetails = insightsResponse?.data?.data?.calls;

  const leadsCrmRecords = leadsCrmResponse?.data?.data ?? [];
  const leadsCrmRecordsObj = {};
  leadsCrmRecords.forEach((item) => {
    leadsCrmRecordsObj[item.leadgen_id] = item;
  });

  return user ? (
    <MobileContainer>
      <div className="flex flex-col flex-1 h-full w-full">
        <div className="flex items-center mt-4">
          <div className="p-4 cursor-pointer" onClick={onBackPressed}>
            <BackIcon />
          </div>
          <p
            className="text-black text-lg cursor-pointer line-clamp-2"
            onClick={() => setShowEditCampaignPostLaunchBs(true)}
          >
            {campaignName} {campaignName ? '🖊️' : ''}
          </p>
          <div className="flex-1" />
          <div className="flex items-center space-x-2">
            <div>
              <GoogleIcon className="w-4 h-4" />
            </div>
            <div className="pl-2 pr-4">
              {campaignDetails ? (
                <CampaignStatusComp campaignDetails={campaignDetails} />
              ) : null}
            </div>
          </div>
        </div>
        {campaignDetails ? (
          <>
            {campaignDetails?.id ? (
              <div className="flex items-center px-4 mt-3 border-b border-gray-light">
                {CAMPAIGN_DETAILS_TABS.map((item, index) => {
                  if (
                    [
                      GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
                      GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
                    ].includes(campaignDetails?.type)
                  ) {
                    return null;
                  }
                  if (item.id === 'conversions') {
                    // todo day wise conversion breakdown
                    return null;
                  }
                  const selected = item.id === selectedTabId;

                  return (
                    <div
                      key={index}
                      className={classNames('flex-1 cursor-pointer pb-4', {
                        'border-b-2 border-primary': selected,
                      })}
                      onClick={() => setSelectedTabId(item.id)}
                    >
                      <p
                        className={classNames(
                          'text-sm text-primary text-center',
                          {
                            'font-bold': selected,
                          },
                        )}
                      >
                        {item.id === CAMPAIGN_DETAILS_TABS[0].id
                          ? item.label
                          : `${item.label}(${
                              googleLeadsResponse?.data?.data?.length ?? 0
                            })`}
                      </p>
                    </div>
                  );
                })}
              </div>
            ) : null}
            {selectedTabId === CAMPAIGN_DETAILS_TABS[0].id ? (
              <GoogleCampaignInfoTab
                campaignDetails={campaignDetails}
                mediaAssetsDetails={mediaAssetsDetails}
                user={user}
                campaignInsightsDetails={campaignInsightsDetails}
                onCampaignStatusUpdate={() =>
                  void campaignDetailsResponse.refetch()
                }
                partnerConfig={partnerConfig}
                keywordsInsightsDetails={keywordsInsightsDetails}
                locationsInsightsDetails={locationsInsightsDetails}
                callInsightsDetails={callInsightsDetails}
              />
            ) : (
              <CampaignLeadsTab
                campaignDetails={campaignDetails}
                campaignLeads={googleLeadsResponse?.data?.data}
                leadsCrmRecordsObj={leadsCrmRecordsObj}
                onEditLeadsCrmClick={onEditLeadsCrmClick}
                partnerConfig={partnerConfig}
                user={user}
              />
            )}
          </>
        ) : (
          <div className="flex flex-col items-center justify-center mt-3 flex-1">
            <SpinnerLoader />
          </div>
        )}
      </div>
      {selectedLeadsCrmDetails ? (
        <EditLeadsCrmDetailsBs
          leadsCrmDetails={selectedLeadsCrmDetails}
          onClose={() => setSelectedLeadsCrmDetails(null)}
          user={user}
          onUpdateDone={() => void leadsCrmResponse.refetch()}
        />
      ) : null}
      {showEditCampaignPostLaunchBs ? (
        <EditCampaignPostLaunchBs
          campaignDetails={campaignDetails}
          user={user}
          onClose={() => setShowEditCampaignPostLaunchBs(false)}
          onCampaignUpdated={() => void campaignDetailsResponse.refetch()}
        />
      ) : null}
    </MobileContainer>
  ) : null;
};

export default GoogleCampaignDetails;
