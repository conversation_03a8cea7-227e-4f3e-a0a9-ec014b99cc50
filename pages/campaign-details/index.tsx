import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import MobileContainer from '@/components/lib/MobileContainer';
import { CAMPAIGN_DETAILS_TABS, QueryParams } from 'src/constants';
import { useQuery } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import { logApiErrorAndShowToastMessage, isAdmin } from 'src/utils';
import {
  getAdImages,
  getAdVideosDetails,
  getCampaignDetails,
} from 'src/actions/onboarding';
import BackIcon from '@/images/common/back-arrow.svg';
import CampaignStatusComp from '@/components/dashboard/campaigns/CampaignStatusComp';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { ConsumerType, GROWEASY_CAMPAIGN_TYPE } from 'src/types/campaigns';
import classNames from 'classnames';
import CampaignLeadsTab from '@/components/campaign_details/CampaignLeadsTab';
import {
  getCampaignInsights,
  getCtwaLeads,
  getInstantFormLeads,
} from 'src/actions/dashboard';
import CampaignInfoTab from '@/components/campaign_details/CampaignInfoTab';
import { getLeadsCrmRecords } from 'src/actions/crm';
import { ILeadsCrmDetails } from 'src/types/leads_crm';
import EditLeadsCrmDetailsBs from '@/components/campaign_details/bottom_sheets/EditLeadsCrmDetailsBs';
import EditCampaignPostLaunchBs from '@/components/campaign_details/bottom_sheets/EditCampaignPostLaunchBs';
import dynamic from 'next/dynamic';
import AiMediaButtonComp from '@/components/admin/AiMediaButtonComp';

interface ICampaignDetailsProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

const ConversionsChartComp = dynamic(
  () => import('../../src/components/campaign_details/ConversionsChartComp'),
);

const CampaignDetails = (props: ICampaignDetailsProps) => {
  const { user, partnerConfig } = props;

  const [selectedTabId, setSelectedTabId] = useState(
    CAMPAIGN_DETAILS_TABS[0].id,
  );
  const [showEditCampaignPostLaunchBs, setShowEditCampaignPostLaunchBs] =
    useState(false);

  const router = useRouter();

  const campaignId = router.query[QueryParams.CAMPAIGN_ID];

  const [selectedLeadsCrmDetails, setSelectedLeadsCrmDetails] =
    useState<Partial<ILeadsCrmDetails> | null>(null);

  const campaignDetailsResponse = useQuery(
    ['getCampaignDetails', campaignId],
    () => {
      return getCampaignDetails({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled: !!campaignId,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignDetails.getCampaignDetails',
        );
      },
    },
  );

  const campaignDetails = campaignDetailsResponse?.data?.data ?? null;

  const adImagesResponse = useQuery(
    ['getAdImages', campaignId],
    () =>
      getAdImages({
        queryParams: {
          ...router.query,
          hashes: JSON.stringify(
            campaignDetails?.details?.ad_banners?.map(
              (item) => item.image?.hash,
            ),
          ),
          [QueryParams.AD_ACCOUNT_ID]:
            campaignDetails?.details?.config?.ad_account_id ?? '',
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignDetails?.details?.ad_banners?.length,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'CampaignDetails.getAdImages');
      },
    },
  );

  const adVideosResponse = useQuery(
    ['getAdVideosDetails', campaignId],
    () =>
      getAdVideosDetails({
        queryParams: {
          ...router.query,
          ids: campaignDetails?.details?.ad_videos
            ?.map((item) => item.id)
            ?.join(','),
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      enabled: !!campaignDetails?.details?.ad_videos?.length,
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignDetails.getAdVideosDetails',
        );
      },
    },
  );

  const formLeadsResponse = useQuery(
    ['getInstantFormLeads', campaignId],
    () =>
      getInstantFormLeads({
        queryParams: {
          ...router.query,
          [QueryParams.LEADGEN_FORM_ID]: campaignDetails?.meta_leadgen_form_id,
          [QueryParams.LIMIT]: '1000',
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignDetails?.meta_leadgen_form_id,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignDetails.getInstantFormLeads',
        );
      },
    },
  );

  const ctwaLeadsResponse = useQuery(
    ['getCtwaLeads', campaignId],
    () =>
      getCtwaLeads({
        queryParams: {
          ...router.query,
          [QueryParams.CAMPAIGN_ID]: campaignDetails?.id,
          [QueryParams.LIMIT]: '500',
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: campaignDetails?.type === GROWEASY_CAMPAIGN_TYPE.CTWA,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'CampaignDetails.getCtwaLeads');
      },
    },
  );

  const leadsCrmResponse = useQuery(
    ['getLeadsCrmRecords', campaignId],
    () =>
      getLeadsCrmRecords({
        queryParams: {
          ...router.query,
          [QueryParams.CAMPAIGN_ID]: campaignId,
          [QueryParams.LIMIT]: '1000',
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignId,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignDetails.getLeadsCrmRecords',
        );
      },
    },
  );

  const insightsResponse = useQuery(
    ['getCampaignInsights', campaignId],
    () =>
      getCampaignInsights({
        queryParams: {
          ...router.query,
          [QueryParams.CAMPAIGN_ID]: campaignDetails.meta_id,
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignDetails?.meta_id,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CampaignDetails.getCampaignInsights',
        );
      },
    },
  );

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const onBackPressed = () => {
    router.back();
  };

  const onEditLeadsCrmClick = (details: Partial<ILeadsCrmDetails>) => {
    details.uid = user.uid;
    details.campaign_id = campaignDetails.id;

    setSelectedLeadsCrmDetails(details);
  };

  const leadsCrmRecords = leadsCrmResponse?.data?.data ?? [];
  const leadsCrmRecordsObj = {};
  leadsCrmRecords.forEach((item) => {
    leadsCrmRecordsObj[item.leadgen_id] = item;
  });

  const renderTabs = () => {
    if (selectedTabId === 'campaigns') {
      // todo: to get no of leads, use cost_per_action_type: { action_type : 'lead' } from Insights API, check for CTWA
      // reason: Meta Leads API will not return exact count beyond a certain date range, alternate: rely on Firestore leads collection
      return (
        <CampaignInfoTab
          campaignDetails={campaignDetails}
          adImages={adImagesResponse?.data?.data}
          adVideos={Object.values(adVideosResponse.data?.data ?? {})}
          campaignInsightsDetails={{
            ...insightsResponse?.data?.data?.[0],
            leads:
              formLeadsResponse?.data?.data?.length ??
              ctwaLeadsResponse?.data?.data?.length,
          }}
          user={user}
          onCampaignStatusUpdate={() => void campaignDetailsResponse.refetch()}
          partnerConfig={partnerConfig}
        />
      );
    } else if (selectedTabId === 'leads') {
      return (
        <CampaignLeadsTab
          campaignDetails={campaignDetails}
          campaignLeads={
            formLeadsResponse?.data?.data ?? ctwaLeadsResponse?.data?.data
          }
          leadsCrmRecordsObj={leadsCrmRecordsObj}
          onEditLeadsCrmClick={onEditLeadsCrmClick}
          partnerConfig={partnerConfig}
          user={user}
        />
      );
    } else if (selectedTabId === 'conversions') {
      return (
        <ConversionsChartComp campaignDetails={campaignDetails} user={user} />
      );
    }
  };

  const campaignName =
    campaignDetails?.friendly_name ??
    campaignDetails?.details?.business_details?.business_category ??
    '';
  const consumerType =
    campaignDetails?.details?.business_details?.consumer_type ?? '';

  return user ? (
    <MobileContainer>
      <div className="flex flex-col flex-1 h-full w-full">
        <div className="flex items-center mt-4">
          <div className="p-4 cursor-pointer" onClick={onBackPressed}>
            <BackIcon />
          </div>
          <p
            className="text-black text-lg cursor-pointer line-clamp-2"
            onClick={() => setShowEditCampaignPostLaunchBs(true)}
          >
            {campaignName}{' '}
            {!consumerType || consumerType === ConsumerType.All
              ? ''
              : `(${consumerType})`}{' '}
            {campaignName ? '🖊️' : ''}
          </p>
          <div className="flex-1" />
          <div className="px-4 flex items-center space-x-2">
            {campaignDetails ? (
              <CampaignStatusComp campaignDetails={campaignDetails} />
            ) : null}
            {campaignDetails?.id && isAdmin(user) && (
              <AiMediaButtonComp campaignDetails={campaignDetails} />
            )}
          </div>
        </div>
        {campaignDetails?.id ? (
          <div className="flex items-center px-4 mt-3 border-b border-gray-light">
            {CAMPAIGN_DETAILS_TABS.map((item, index) => {
              const selected = item.id === selectedTabId;

              if (
                item.id === 'leads' &&
                [
                  GROWEASY_CAMPAIGN_TYPE.META_SALES,
                  GROWEASY_CAMPAIGN_TYPE.CTWA,
                ].includes(campaignDetails?.type)
              ) {
                return null;
              }
              if (
                item.id === 'conversions' &&
                [GROWEASY_CAMPAIGN_TYPE.LEAD_FORM].includes(
                  campaignDetails?.type,
                )
              ) {
                return null;
              }

              return (
                <div
                  key={index}
                  className={classNames('flex-1 cursor-pointer pb-4', {
                    'border-b-2 border-primary': selected,
                  })}
                  onClick={() => setSelectedTabId(item.id)}
                >
                  <p
                    className={classNames('text-sm text-primary text-center', {
                      'font-bold': selected,
                    })}
                  >
                    {item.id === 'leads'
                      ? `${item.label}(${
                          formLeadsResponse?.data?.data?.length ||
                          ctwaLeadsResponse?.data?.data?.length ||
                          0
                        })`
                      : item.label}
                  </p>
                </div>
              );
            })}
          </div>
        ) : null}
        {campaignDetails ? (
          renderTabs()
        ) : (
          <div className="flex flex-col items-center mt-3">
            <SpinnerLoader />
          </div>
        )}
        {selectedLeadsCrmDetails ? (
          <EditLeadsCrmDetailsBs
            leadsCrmDetails={selectedLeadsCrmDetails}
            onClose={() => setSelectedLeadsCrmDetails(null)}
            user={user}
            onUpdateDone={() => void leadsCrmResponse.refetch()}
          />
        ) : null}
        {showEditCampaignPostLaunchBs ? (
          <EditCampaignPostLaunchBs
            campaignDetails={campaignDetails}
            user={user}
            onClose={() => setShowEditCampaignPostLaunchBs(false)}
            onCampaignUpdated={() => void campaignDetailsResponse.refetch()}
          />
        ) : null}
      </div>
    </MobileContainer>
  ) : null;
};

export default CampaignDetails;
