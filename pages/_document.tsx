import { Html, Head, Main, NextScript } from 'next/document';
import Script from 'next/script';
import { FB_PIXEL_ID } from 'src/constants';

export default function Document() {
  const isAdGlobalAI =
    typeof window !== 'undefined' &&
    window.location.hostname === 'adglobalai.com';

  return (
    <Html lang="en">
      <Head>
        {/* Google Tag Manager */}
        {isAdGlobalAI && (
          <Script id="adglobalai-gtm-script" strategy="afterInteractive">
            {`
              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer','GTM-W9QJDWLC');
            `}
          </Script>
        )}
        {/* End Google Tag Manager  */}

        <noscript>
          <img
            height="1"
            width="1"
            alt=""
            style={{ display: 'none' }}
            src={`https://www.facebook.com/tr?id=${FB_PIXEL_ID}&ev=PageView&noscript=1`}
          />
        </noscript>
      </Head>
      <body>
        {/* Google Tag Manager (noscript)  */}
        {isAdGlobalAI && (
          <noscript>
            <iframe
              src="https://www.googletagmanager.com/ns.html?id=GTM-W9QJDWLC"
              height="0"
              width="0"
              style={{ display: 'none', visibility: 'hidden' }}
            ></iframe>
          </noscript>
        )}
        {/* End Google Tag Manager (noscript) */}
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
