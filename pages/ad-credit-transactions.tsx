import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser } from 'src/types';
import BackIcon from '@/images/common/back-arrow.svg';
import MobileContainer from '@/components/lib/MobileContainer';
import { useQuery } from 'react-query';
import { getAdCreditTransactions } from 'src/actions/profile';
import { getCommonHeaders } from 'src/actions';
import {
  formatCurrencyAmount,
  getFormattedDateString,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import NoDataFound from '@/components/lib/NoDataFound';
import classNames from 'classnames';

interface IAdCreditTransactionsProps {
  user?: IGroweasyUser;
}

const AdCreditTransactions = (props: IAdCreditTransactionsProps) => {
  const { user } = props;

  const router = useRouter();

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const transactionsResponse = useQuery(
    'getAdCreditTransactions',
    () => {
      return getAdCreditTransactions({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'AdCreditTransactions.getAdCreditTransactions',
        );
      },
    },
  );

  const onBackPressed = () => {
    router.back();
  };

  const transactions = transactionsResponse?.data?.data ?? null;

  return user ? (
    <MobileContainer>
      <div className="flex flex-col flex-1 w-full bg-white h-full">
        <div className="flex items-center mt-4">
          <div className="p-4 cursor-pointer" onClick={onBackPressed}>
            <BackIcon />
          </div>
          <p className="text-black text-lg">Ad Credit Transactions</p>
          <div className="flex-1" />
        </div>
        {transactions ? (
          <div className="mt-3 mb-3 flex flex-col px-5 flex-1 overflow-y-scroll no-scrollbar">
            {transactions.map((item, index) => {
              const creationDate = new Date(item.created_at._seconds * 1000);
              return (
                <div key={index} className="border-b border-gray-light py-3">
                  <div className="flex items-center">
                    <p className="text-sm w-28">
                      {getFormattedDateString(creationDate)}
                    </p>
                    <p
                      className={classNames('text-xs  py-1 px-2 rounded-lg', {
                        'bg-green-light': item.type === 'CREDIT',
                        'bg-red-light': item.type === 'DEBIT',
                      })}
                    >
                      {item.type}
                    </p>
                    <div className="flex-1" />
                    <p className="text-sm font-medium">
                      {formatCurrencyAmount(item.value, item.currency)}
                    </p>
                  </div>
                  <div>
                    <p className="text-xxs mt-1">{item.description}</p>
                  </div>
                </div>
              );
            })}
            {transactions.length === 0 ? (
              <div className="flex justify-center">
                <NoDataFound
                  illustration={{
                    url: '/images/dashboard/payment-illustration.svg',
                    width: 200,
                    height: 200,
                  }}
                  title="No Transactions Found!"
                />
              </div>
            ) : null}
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <SpinnerLoader />
          </div>
        )}
      </div>
    </MobileContainer>
  ) : null;
};

export default AdCreditTransactions;
