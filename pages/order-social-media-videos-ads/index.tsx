import React from 'react';
import { useRouter } from 'next/router';
import Container from '@/components/lib/Container';
import Image from 'next/image';
import GrowEasyLogo from '@/components/order-social-media-videos/logos/VideoGrowEasyLogo';
import Heading from '@/components/lib/typography/Heading';
import ButtonV2 from '@/components/lib/ButtonV2';
import ScriptIcon from '@/images/icons/script-icon.svg';
import ActorProfileIcon from '@/images/icons/actor-profile-icon.svg';
import EditIcon from '@/images/icons/edit-icon.svg';
import CardPaymentIcon from '@/images/icons/card-payment-icon.svg';
import Body from '@/components/lib/typography/Body';
import VideoGallery from '@/components/order-social-media-videos/VideoGallery';
import Head from 'next/head';
import { getMetaTags } from 'src/utils';
import FAQs from '@/components/order-social-media-videos/FAQs';
import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';

const OrderSocialMediaVideos = () => {
  return (
    <>
      <Container
        className={`px-4 !pt-4 sm:!px-8 sm:!py-8 md:!px-12 lg:!px-16 xl:!px-20 2xl:!px-24`}
      >
        <main>
          <MainBody />
        </main>
        <div className="fixed inset-0 w-full h-full z-[-1] pointer-events-none">
          <Image
            src={'/images/groweasy-videos/background/background-line.svg'}
            layout="fill"
            objectFit="cover"
            alt="Background lines"
            className="pointer-events-none"
          />
        </div>
      </Container>
      <FooterComp />
    </>
  );
};

const MainBody = () => {
  const router = useRouter();
  return (
    <>
      <Head>{getMetaTags('/order-social-media-videos-ads')}</Head>
      <div className="pb-4">
        <div>
          <GrowEasyLogo className="pointer-events-none" />
        </div>
        <div className="mt-8 md:mt-14 lg:mt-20">
          <div className="flex max-lg:flex-col max-lg:items-center justify-between">
            <div className="flex flex-col justify-between max-lg:items-center">
              <div className="flex flex-col max-sm:gap-5 gap-7 max-lg:items-center max-lg:text-center">
                <Heading level={4} className="text-[#565656] !font-medium">
                  Did you know? Image Ads Don&apos;t Work Anymore
                </Heading>
                <Heading level={1} className="videoOrderGradientHeadingLeft">
                  <span className="sm:whitespace-nowrap">
                    Stunning Video Ads to
                  </span>{' '}
                  <br /> increase your ROAS
                </Heading>
              </div>
              <div className="max-md:mt-7 max-lg:mt-10">
                <ButtonV2
                  variant="primary"
                  onClick={() =>
                    void router.push('/order-social-media-videos-ads/order')
                  }
                  className="capitalize"
                >
                  Order your video Ad
                </ButtonV2>
              </div>
            </div>
            <div className="max-sm:mt-8 max-lg:mt-12 w-[320px] md:w-[350px] lg:w-[400px] rounded-xl p-4 md:p-6 lg:p-8 flex flex-col gap-6 max-[360px]:w-full border-2 border-[#286053] backdrop-blur-[2px] md:backdrop-blur-sm">
              {[
                {
                  Icon: ScriptIcon as React.FC,
                  title: 'High Converting Scripts',
                },
                {
                  Icon: ActorProfileIcon as React.FC,
                  title: 'By Trained Actors',
                },
                { Icon: EditIcon as React.FC, title: 'Professional Editing' },
                { Icon: CardPaymentIcon as React.FC, title: 'Flexible Plans' },
              ].map(
                ({ Icon, title }: { Icon: React.FC; title: string }, index) => (
                  <div className="flex items-center gap-4" key={index}>
                    <IconContainer>
                      <Icon />
                    </IconContainer>
                    <Body className="!text-base lg:!text-lg xl:!text-xl">
                      {title}
                    </Body>
                  </div>
                ),
              )}
            </div>
          </div>
          <div className="mt-8 md:mt-16 lg:mt-20">
            <Heading level={3} className="videoOrderGradientHeadingTop">
              See What Our Team Can Do!
            </Heading>
            <Body className="mt-3 sm:mt-2" size="large">
              Check out these stunning video ads created by our Team. These
              examples show the quality and creativity we can bring to your
              campaigns.
            </Body>
          </div>
          <VideoGallery />
          <FAQs />
        </div>
      </div>
    </>
  );
};

function IconContainer({ children }: { children: React.ReactNode }) {
  return (
    <div className="w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-[#286053] flex justify-center items-center rounded-lg flex-shrink-0">
      <div className="w-7 h-7 md:w-8 md:h-8 lg:w-9 lg:h-9 flex justify-center items-center text-white">
        {children}
      </div>
    </div>
  );
}

export default OrderSocialMediaVideos;
