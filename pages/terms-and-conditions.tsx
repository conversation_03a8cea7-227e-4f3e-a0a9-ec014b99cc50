import ContactUsComp from '@/components/home_pages/groweasy_old/ContactUsComp';
import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import NavigationHeader from '@/components/lib/NavigationHeader';
import Head from 'next/head';
import { getMetaTags } from 'src/utils';

const termsAndConditions = [
  {
    title: '1. Acceptance of Terms',
    content: [
      {
        subtitle: '',
        text: "By accessing and using GrowEasy's services and website, you agree to comply with and be bound by these terms and conditions. If you do not agree with any part of these terms, you may not use our services.",
      },
    ],
  },
  {
    title: '2. User Accounts',
    content: [
      {
        subtitle: '2.1 Account Creation',
        text: 'To use certain features of GrowEasy, you may be required to create a user account. You agree to provide accurate and complete information during the registration process.',
      },
      {
        subtitle: '2.2 Account Security',
        text: 'You are responsible for maintaining the confidentiality of your account credentials and ensuring the security of your account. Notify us immediately of any unauthorized use or breach of security.',
      },
    ],
  },
  {
    title: '3. Use of Services',
    content: [
      {
        subtitle: '3.1 Eligibility',
        text: 'You must be at least 18 years old to use our services. By using our services, you represent and warrant that you meet this eligibility requirement.',
      },
      {
        subtitle: '3.2 Prohibited Activities',
        text: 'You agree not to engage in any activities that violate these terms, applicable laws, or the rights of others. Prohibited activities include but are not limited to unauthorized access, data scraping, and any form of harassment',
      },
    ],
  },
  {
    title: '4. Intellectual Property',
    content: [
      {
        subtitle: '4.1 Ownership',
        text: 'GrowEasy retains ownership of all intellectual property rights associated with our services, including trademarks, copyrights, and proprietary software.',
      },
      {
        subtitle: '4.2 License',
        text: 'We grant you a limited, non-exclusive, and revocable license to use our services in accordance with these terms.',
      },
    ],
  },
  {
    title: '5. Limitation of Liability',
    content: [
      {
        subtitle: '',
        text: 'GrowEasy is not liable for any direct, indirect, incidental, consequential, or punitive damages arising from the use of our services.',
      },
    ],
  },
  {
    title: '6. Changes to Terms and Conditions',
    content: [
      {
        subtitle: '',
        text: 'We may update these terms and conditions periodically. The date of the latest update will be reflected at the top of the document.',
      },
    ],
  },
  {
    title: '7. Governing Law',
    content: [
      {
        subtitle: '',
        text: 'These terms and conditions are governed by and construed in accordance with the laws of Indian government. Any disputes arising from these terms will be subject to the exclusive jurisdiction of the courts in Delhi.',
      },
    ],
  },
  {
    title: '8. Contact Us',
    content: [
      {
        subtitle: '',
        text: 'If you have any questions or concerns regarding these terms and conditions, please contact us at [<EMAIL>].',
      },
    ],
  },
];

const TermsAndConditions = () => {
  return (
    <div>
      <Head>{getMetaTags('/terms-and-conditions')}</Head>
      <NavigationHeader />
      <div className="px-4 py-4 sm:px-36">
        <h1 className="mt-6 text-2xl font-medium">
          Terms and Conditions for AUTOTME SOFTWARE PRIVATE LIMITED, a company
          registered in India, operates GrowEasy, an AI-enabled lead generation
          platform
        </h1>
        <p className="text-sm font-medium mt-3">Last Updated: 14 Dec, 2023</p>
        <p className="mt-5 text-base">
          Thank you for choosing GrowEasy (we, our, or us).
        </p>
        <p className="text-base">
          These terms and conditions outline the rules and regulations for the
          use of our services and website.
        </p>
        {termsAndConditions.map((item, index) => {
          return (
            <div key={index}>
              <h2 className="mt-8 text-xl">{item.title}</h2>
              {item.content.map((contentItem, innerIndex) => {
                return (
                  <div key={innerIndex} className="mt-4">
                    {contentItem.subtitle ? (
                      <h3 className="text-base">{contentItem.subtitle}</h3>
                    ) : null}
                    <p className="text-sm mt-2">{contentItem.text}</p>
                  </div>
                );
              })}
            </div>
          );
        })}
        <p className="mt-8 text-base">
          By using our services, you agree to these terms and conditions.
        </p>
        <ContactUsComp />
      </div>
      <FooterComp />
    </div>
  );
};

export default TermsAndConditions;
