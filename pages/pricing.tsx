import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import NavigationHeader from '@/components/lib/NavigationHeader';
import Head from 'next/head';

// Correct import paths based on your file structure
import GrowEasyPricingDesktop from '@/components/pricing/GroweasyPricingDesktop';
import GrowEasyPricingMobile from '@/components/pricing/GroweasyPricingMobile';

const Pricing = () => {
  return (
    <div>
      <Head>
        <title key="title">Pricing | GrowEasy</title>
        <meta
          name="description"
          key="description"
          content="Explore GrowEasy's pricing plans for AI-powered digital marketing solutions, including automated creative design, copywriting, and campaign optimization."
        />
        <link
          rel="canonical"
          key="canonical"
          href="https://groweasy.ai/pricing"
        />
      </Head>

      <NavigationHeader />

      {/*  Replaced Pricing Section */}
      <div>
        <div className="hidden md:block">
          <GrowEasyPricingDesktop />
        </div>
        <div className="md:hidden">
          <GrowEasyPricingMobile />
        </div>
      </div>

      <FooterComp />
    </div>
  );
};

export default Pricing;
