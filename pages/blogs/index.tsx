import BlogListItem from '@/components/blogs/BlogListItem';
import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import NavigationHeader from '@/components/lib/NavigationHeader';
import Head from 'next/head';
import { IPost } from 'src/types/blogs';
import { getMetaTags } from 'src/utils';

interface IBlogsProps {
  posts: Partial<IPost>[];
}

const Blogs = (props: IBlogsProps) => {
  const { posts } = props;

  return (
    <div>
      <Head>{getMetaTags('/blogs')}</Head>
      <NavigationHeader />
      <div className="px-4 py-4 sm:px-36">
        <h1 className="mt-6 text-3xl font-medium">Blogs</h1>
        {posts.map((item, index) => {
          return <BlogListItem post={item} className="mt-12" key={index} />;
        })}
      </div>
      <FooterComp />
    </div>
  );
};

export const getServerSideProps = async () => {
  const url = `https://api.blogstatic.io/${process.env.BSTATIC_API_KEY}/posts.json`;
  try {
    // Fetch data from API
    const res = await fetch(url);
    const posts: IPost[] = (await res.json()) as IPost[];
    const data = posts.map((post) => {
      delete post.post_text;
      return post;
    });
    // Pass data to the page via props
    return { props: { posts: data } };
  } catch (error) {
    console.log(error);
    return { props: { posts: [] } };
  }
};

export default Blogs;
