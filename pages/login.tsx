import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { getAdditionalUserInfo, UserCredential } from 'firebase/auth';
import GroweasyLogo from '@/images/logos/groweasy.svg';
import { GrowEasyPartners, IGroweasyUser, IPartnerConfig } from 'src/types';
import { useRouter } from 'next/router';
import {
  sendWelcomeEmail,
  updateProfileInCustomClaims,
} from 'src/actions/login';
import { getCommonHeaders } from 'src/actions';
import Head from 'next/head';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import FooterComp from '@/components/home_pages/groweasy_old/FooterLightComp';
import { getMetaTags, logApiErrorAndShowToastMessage } from 'src/utils';
import dynamic from 'next/dynamic';
import { useMutation } from 'react-query';
import { BiCheck } from 'react-icons/bi';
import bg1 from './assets/man2.jpg';
const MobileAuthenticationComp = dynamic(
  () => import('@/components/login/MobileAuthenticationComp'),
);

interface ILoginProps {
  onUserChange?: (user: IGroweasyUser) => void;
  partnerConfig?: IPartnerConfig;
  user: IGroweasyUser;
}

const Login = (props: ILoginProps) => {
  const { onUserChange, partnerConfig, user } = props;

  const [errorText, setErrorText] = useState('');
  const newUserSignedupRef = useRef(false);

  const router = useRouter();

  const updateProfileInCustomClaimsMutation = useMutation(
    updateProfileInCustomClaims,
  );

  useEffect(() => {
    if (user) {
      if (newUserSignedupRef.current) {
        // in case of new signip, `onLoginSuccess` will redirect to /edit-profile
        return;
      }
      // redirecting to /dashboard when user is logged in
      void router.push('/dashboard');
    }
  }, [user, router]);

  const onLoginSuccess = (user: IGroweasyUser) => {
    onUserChange(user);
    // BE will check & send welcome email
    void sendWelcomeEmail({
      headers: getCommonHeaders(user),
      queryParams: router.query as Record<string, string>,
    });
    // todo: if user drops here, i.e. login -> success or login -> success -> edit-profile,
    // his profile will only be updated on next manual edit profile

    // todo contextual redirection
    void router.push('/edit-profile?source=login');
  };

  const onFirebaseAuthSuccess = async (credential: UserCredential) => {
    const idTokenResult = await credential.user.getIdTokenResult(true); // force refresh
    const { isNewUser } = getAdditionalUserInfo(credential);

    if (isNewUser) {
      newUserSignedupRef.current = true;
    }

    // update partner in custom claims (e.g. GENIUS_ADS), only for new users
    if (partnerConfig?.partner && isNewUser) {
      await updateProfileInCustomClaimsMutation
        .mutateAsync({
          queryParams: {},
          headers: {
            Authorization: `Bearer ${idTokenResult.token}`,
            'Content-Type': 'application/json',
          },
          data: {
            partner: partnerConfig.partner,
          },
        })
        .catch((error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'Login.updateProfileInCustomClaimsMutation',
          );
        });
    }

    if (onUserChange) {
      const user = {
        displayName: credential.user.displayName,
        email: credential.user.email,
        authToken: idTokenResult.token,
        uid: credential.user.uid,
        photoUrl: credential.user.photoURL,
        mobile: credential.user.phoneNumber,
      };
      // for phone auth users
      if (!user.email && user.mobile && idTokenResult) {
        const profile = idTokenResult.claims.profile as {
          email: string;
          name: string;
        };
        user.email = profile?.email;
        user.displayName = profile?.name;
      }
      onLoginSuccess(user);
    }
  };

  const onLoginWithAppleClick = () => {
    if (window.bridge) {
      window.bridge.postMessage('loginWithApple');
      return;
    }
  };

  const onLoginWithGoogleClick = async () => {
    if (window.bridge) {
      window.bridge.postMessage('login');
      return;
    }
    logEvent(EVENT_NAMES.signin_with_google_clicked);

    const { getAuth, signInWithPopup, GoogleAuthProvider } = await import(
      'firebase/auth'
    );
    const auth = getAuth();
    const provider = new GoogleAuthProvider();
    signInWithPopup(auth, provider)
      .then(async (result) => {
        await onFirebaseAuthSuccess(result);
      })
      .catch((error: { code: string; message: string }) => {
        const errorCode = error.code;
        const errorMessage = error.message;
        setErrorText(`Code: ${errorCode}, Error: ${errorMessage}`);
      });
  };

  useEffect(() => {
    // flutter webview will call these methods
    window.onLoginSuccess = (user?: IGroweasyUser) => {
      if (user && user.authToken && user.email && user.uid) {
        onLoginSuccess(user);
      }
    };
  }, []);

  const insideWebview = typeof window !== 'undefined' && !!window?.bridge;
  const platformIos =
    typeof window !== 'undefined' && window.platform === 'ios';

  const renderLogo = () => {
    if (partnerConfig?.partner === GrowEasyPartners.NIVIDA) {
      return (
        <div className="flex justify-center items-center">
          <div className="w-56">
            <Image
              src={partnerConfig.logoImage}
              alt={partnerConfig.name}
              width="2481"
              height="890"
            />
          </div>
        </div>
      );
    }
    if (
      [GrowEasyPartners.BANNERBOT, GrowEasyPartners.GENIUS_ADS].includes(
        partnerConfig?.partner,
      )
    ) {
      return (
        <div className="flex justify-center items-center">
          <div className="mr-2">
            <Image
              src={partnerConfig.logoImage}
              alt={partnerConfig.name}
              width="60"
              height="60"
            />
          </div>
          <p
            className="text-4xl font-medium"
            style={
              partnerConfig.partner === GrowEasyPartners.BANNERBOT
                ? { color: '#d9bc76' }
                : {}
            }
          >
            {partnerConfig?.name}
          </p>
        </div>
      );
    }
    if (partnerConfig?.partner === GrowEasyPartners.ZENDOT) {
      return (
        <div className="flex justify-center items-center">
          <div className="w-56">
            <Image
              src={partnerConfig.logoImage}
              alt={partnerConfig.name}
              width="300"
              height="120"
            />
          </div>
        </div>
      );
    }
    if (partnerConfig?.partner === GrowEasyPartners.AD_GLOBAL_AI) {
      return (
        <div className="flex justify-center items-center">
          <div className="w-64">
            <Image
              src={partnerConfig.logoImage}
              alt={partnerConfig.name}
              width="976"
              height="156"
            />
          </div>
        </div>
      );
    }
    return (
      <div className="flex justify-center items-center">
        <div className="mr-3">
          <GroweasyLogo width={212 / 4} height={212 / 4} />
        </div>
        <p className="text-4xl font-medium text-black">
          {partnerConfig?.name ?? 'GrowEasy'}
        </p>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Head>
        {partnerConfig ? (
          <>
            <title>
              Login | {partnerConfig?.name} | AI Lead generation tool
            </title>
            <meta
              name="description"
              content={partnerConfig?.meta?.description ?? ''}
            />
          </>
        ) : (
          getMetaTags('/login')
        )}
      </Head>

      <div className="grid lg:grid-cols-7 min-h-screen w-full relative">
        <Image
          src={bg1}
          placeholder="blur"
          alt=""
          width={1300}
          height={800}
          className="absolute inset-0 object-cover w-full h-full z-0 -scale-x-100 sm:block hidden"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-blue-500 to-yellow-400 opacity-10 z-0  sm:block hidden"></div>
        <div className="absolute w-full bottom-0 left-0 h-3/5 bg-gradient-to-t from-black/95 to-transparent via-black/70 z-0  sm:block hidden"></div>
        <div className="absolute w-80 blur-3xl opacity-75 -translate-y-14 top-0 left-0 h-1/6 bg-gradient-to-b -rotate-12 from-black/70 to-transparent via-black/80 z-0  sm:block hidden"></div>

        <div className="hidden lg:flex lg:col-span-4 flex-col justify-end p-12 relative overflow-hidden">
          <div className="absolute backdrop-blur hover:shadow-xl transition-all duration-500 cursor-pointer bg-black/20 rounded-2xl p-3 z-10 top-8">
            {renderLogo()}
          </div>

          <div className="relative z-10 text-white max-w-3xl mb-10">
            <h1 className="text-4xl font-bold mb-6 tracking-tight">
              Join 100+{' '}
              <span className="text-blue-300 underline">Businesses</span>
            </h1>
            <p className="text-xl mb-6">
              Empower your business with the next generation of AI-driven ad
              campaign automation designed for SMEs
              {partnerConfig?.partner === GrowEasyPartners.AD_GLOBAL_AI
                ? ' in Southeast Asia'
                : ''}
            </p>

            <div className="grid grid-cols-2 tracking-tight leading-tight gap-5 mt-2 text-lg">
              <div className="flex items-center">
                <div className="bg-white/20 backdrop-blur-sm p-1 rounded-full mr-4">
                  <BiCheck size={30} />
                </div>
                <span className="text-white leading-tight text-base opacity-90">
                  Launch AI-Powered Ad Campaigns in 5 Minutes
                </span>
                <p className="sr-only">
                  Zero setup hassle. Just pick your goal, and GrowEasy handles
                  the rest.
                </p>
              </div>
              <div className="flex items-center">
                <div className="bg-white/20 backdrop-blur-sm p-1 rounded-full mr-4">
                  <BiCheck size={30} />
                </div>
                <span className="text-white leading-tight text-base opacity-90">
                  Maximize ROAS Across Google, Facebook & Instagram
                </span>
                <p className="sr-only">
                  Smart targeting and creatives that convert — every rupee works
                  harder.
                </p>
              </div>
              <div className="flex items-center">
                <div className="bg-white/20 backdrop-blur-sm p-1 rounded-full mr-4">
                  <BiCheck size={30} />
                </div>
                <span className="text-white leading-tight text-base opacity-90">
                  24/7 Always-On Campaign Optimization
                </span>
                <p className="sr-only">
                  Your ads never sleep. Real-time AI tuning ensures peak
                  performance 24/7.
                </p>
              </div>
              <div className="flex items-center">
                <div className="bg-white/20 backdrop-blur-sm p-1 rounded-full mr-4">
                  <BiCheck size={30} />
                </div>
                <span className="text-white leading-tight text-base opacity-90">
                  Actionable Insights, Not Just Data
                </span>
                <p className="sr-only">
                  Dive deep with powerful, easy-to-understand analytics that
                  drive decisions.
                </p>
              </div>
            </div>
          </div>

          {/* <div className="relative z-10 text-white text-sm">
            <p>
              © 2025 {partnerConfig?.name ?? 'GrowEasy'}. All rights reserved.
            </p>
          </div> */}
        </div>

        {/* Right side login form */}
        <div className="w-full lg:col-span-3 flex items-center justify-center z-10">
          <div className="max-w-md w-full bg-white/80 backdrop-blur-lg py-20 px-8 rounded-xl ">
            <div className="lg:hidden mb-12">{renderLogo()}</div>

            <div className="mb-8 px-4 text-center sm:text-start">
              <h2 className="text-3xl tracking-tight font-poppins font-semibold text-gray-800 mb-2">
                Welcome back
              </h2>
              <p className="text-gray-600">
                AI powered lead generation via Facebook, Instagram, Google &
                TikTok marketing
              </p>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-8 mb-6">
              <div className="mb-6">
                <h3 className="text-xl font-medium text-gray-800 mb-1">
                  Get started
                </h3>
                <p className="text-gray-500 text-sm">
                  Login to continue with your account
                </p>
              </div>

              {partnerConfig &&
              ![
                GrowEasyPartners.GENIUS_ADS,
                GrowEasyPartners.AD_GLOBAL_AI,
              ].includes(partnerConfig?.partner) ? (
                <MobileAuthenticationComp
                  onErrorTextChange={(text: string) => setErrorText(text)}
                  onFirebaseAuthSuccess={onFirebaseAuthSuccess}
                  partnerConfig={partnerConfig}
                />
              ) : (
                <div className="space-y-4">
                  <div className="relative flex items-center justify-center">
                    <div className="border-t border-gray-300 absolute w-full"></div>
                    <div className="bg-white px-4 relative z-10 text-sm text-gray-500">
                      Login options
                    </div>
                  </div>

                  {platformIos && (
                    <button
                      className="w-full flex justify-center items-center bg-zinc-800 text-zinc-100 border py-3 px-4 rounded-lg hover:bg-zinc-700 transition-all transform hover:shadow-md"
                      onClick={onLoginWithAppleClick}
                    >
                      <div className="mr-3">
                        <Image
                          src="/images/common/apple.svg"
                          width="20"
                          height="20"
                          alt="apple-logo"
                        />
                      </div>
                      <span className="font-medium">Sign in with Apple</span>
                    </button>
                  )}

                  <button
                    className="w-full flex justify-center items-center bg-gradient-to-tr from-blue-50 to-green-50 via-cyan-50 text-zinc-800 border border-gray-300 py-3 px-4 rounded-lg hover:bg-gray-50 transition-all transform hover:shadow-md"
                    onClick={() => void onLoginWithGoogleClick()}
                  >
                    <div className="mr-3">
                      <Image
                        src="/images/common/google.svg"
                        width="20"
                        height="20"
                        alt="google-logo"
                      />
                    </div>
                    <span className="font-medium">Sign in with Google</span>
                  </button>
                </div>
              )}

              {errorText ? (
                <div className="mt-6 p-3 bg-rose-100 border border-rose-200 rounded-lg">
                  <p className="text-rose-700 text-center w-full text-sm">
                    {errorText}
                  </p>
                </div>
              ) : null}
            </div>

            <div className="text-center mt-4 px-10 text-gray-500 text-sm">
              <p>
                By signing in, you agree to our Terms of Service and Privacy
                Policy
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* do not show footer for partners or in app */}
      {insideWebview || !!partnerConfig?.partner ? null : (
        <FooterComp className="!mt-0" />
      )}
    </div>
  );
};

export default Login;
