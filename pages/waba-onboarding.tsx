import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser } from 'src/types';
import BackIcon from '@/images/common/back-arrow.svg';
import MobileContainer from '@/components/lib/MobileContainer';
import Button from '@/components/lib/Button';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import {
  GROWEASY_APP_ID,
  GROWEASY_INTERAKT_SOLUTION_ID,
  GROWEASY_WABA_ONBOARDING_CONFIG_ID,
} from 'src/constants';
import { useMutation } from 'react-query';
import { processWabaOnboarding } from 'src/actions/onboarding';
import { getCommonHeaders } from 'src/actions';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import { showToastMessage } from 'src/modules/toast';
import FullScreenLoader from '@/components/lib/FullScreenLoader';

interface IWabaOnboardingProps {
  user?: IGroweasyUser;
}

const WabaOnboarding = (props: IWabaOnboardingProps) => {
  const { user } = props;

  const router = useRouter();

  const processWabaOnboardingMutation = useMutation(processWabaOnboarding);

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  useEffect(() => {
    /**
     * {
        data: {
          phone_number_id: "<PHONE-ID>",
          waba_id: "<WABA-ID>",
        },
        type: "WA_EMBEDDED_SIGNUP",
        event: "FINISH", // or FINISH_ONLY_WABA when only_waba_sharing
        version: 2
      }
      or
      {
        data: {
          current_step: "<Current Step ID>",
        },
        type: "WA_EMBEDDED_SIGNUP",
        event: "CANCEL",
        version: 2
      }

      Current Step Id: BUSINESS_ACCOUNT_SELECTION/WABA_PHONE_PROFILE_PICKER/WHATSAPP_BUSINESS_PROFILE_SETUP/PHONE_NUMBER_SETUP/PHONE_NUMBER_VERIFICATION/PERMISSIONS
     */
    /*const sessionInfoListener = (event) => {
      if (event.origin !== "https://www.facebook.com" && event.origin !== "https://web.facebook.com") {
        return;
      }
      
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'WA_EMBEDDED_SIGNUP') {
          // if user finishes the Embedded Signup flow
          if (data.event === 'FINISH') {
            const {phone_number_id, waba_id} = data.data;
          }
          // if user cancels the Embedded Signup flow
          else {
           const{current_step} = data.data;
          }
        }
      } catch {
        // Don’t parse info that’s not a JSON
        console.log('Non JSON Response', event.data);
      }
    };
    
    window.addEventListener('message', sessionInfoListener); */
  }, []);

  const onBackPressed = () => {
    router.back();
  };

  const onOnboardingSuccess = (code: string) => {
    processWabaOnboardingMutation
      .mutateAsync({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
        body: { code },
      })
      .then((response) => {
        const phoneNumbers = response?.data?.phone_numbers?.map(
          (item) => item.display_phone_number,
        );
        showToastMessage(
          `Onboarding Success: ${phoneNumbers.join(', ')}`,
          'success',
        );
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'WabaOnboarding.onOnboardingSuccess',
        );
      });
  };

  const launchWhatsAppSignup = () => {
    logEvent(EVENT_NAMES.whatsapp_onboarding_start, {
      feature: 'whatsapp_embedded_signup',
      appId: GROWEASY_APP_ID,
    });

    // Launch Facebook login
    window.FB?.login(
      function (response) {
        if (response.authResponse) {
          const code = response.authResponse.code;
          void onOnboardingSuccess(code);
          // The returned code must be transmitted to your backend,
          // which will perform a server-to-server call from there to our servers for an access token
          // S2S: GET https://graph.facebook.com/v17.0/oauth/access_token?client_id=<APP_ID>&client_secret=<APP_SECRET>&code=<CODE>
          // Token generated will be Business Integration System User access tokens , Use this (recommended) or Admin system user access token
        } else {
          console.log('User cancelled login or did not fully authorize.');
        }
      },
      {
        config_id: GROWEASY_WABA_ONBOARDING_CONFIG_ID, // configuration ID goes here
        response_type: 'code', // must be set to 'code' for System User access token
        override_default_response_type: true, // when true, any response types passed in the "response_type" will take precedence over the default types
        extras: {
          //feature: "whatsapp_embedded_signup", // or only_waba_sharing (Bypass phone number selection)
          //"sessionInfoVersion": 2,  //  Receive Session Logging Info
          setup: {
            solutionID: GROWEASY_INTERAKT_SOLUTION_ID,
            // Prefilled data can go here
            /*
            business: {
              name: 'Acme Inc.',
              email: '<EMAIL>',
              phone: {
                code: 1,
                number: '6505551234'
              },
              website: 'https://www.acme.com',
              address: {
                streetAddress1: '1 Acme Way',
                city: 'Acme Town',
                state: 'CA',
                zipPostal: '94000',
                country: 'US'
              },
              timezone: 'UTC-08:00'
            },
            phone: {
              displayName: 'Acme Inc',
              category: 'ENTERTAIN',
              description: 'Acme Inc. is a leading entertainment company.',
            },
            preVerifiedPhone: {
              ids: ['106540352242922','105954558954427']
            }
            */
          },
        },
      },
    );
  };

  return user ? (
    <MobileContainer>
      <div className="flex flex-col flex-1 w-full h-full">
        <div className="flex items-center mt-4">
          <div className="p-4 cursor-pointer" onClick={onBackPressed}>
            <BackIcon />
          </div>
          <p className="text-black text-lg">WhatsApp Onboarding</p>
          <div className="flex-1" />
        </div>

        {/* Main Content */}
        <div className="mt-3 flex flex-col px-5 flex-1 overflow-y-scroll no-scrollbar">
          {/* Welcome Message */}
          <div>
            <p className="text-sm">
              Sign up using WhatsApp to start running Click-to-WhatsApp (CTWA)
              Ads.
            </p>
          </div>

          {/* What Happens After Signup Section */}
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">
              What Happens After Signup:
            </h3>
            <ul className="list-disc list-inside text-gray-600 text-sm">
              <li className="mt-3">
                <strong>GrowEasy Access:</strong> GrowEasy will have access to
                your WhatsApp Business Account to manage your CTWA Ads.
              </li>
              <li className="mt-3">
                <strong>Run CTWA Ads:</strong> You‘ll be able to create and
                manage Click-to-WhatsApp Ads directly through GrowEasy.
              </li>
              <li className="mt-3">
                <strong>Seamless Integration:</strong> Your WhatsApp Business
                Account will be integrated with GrowEasy for a smooth
                advertising experience.
              </li>
            </ul>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center p-4">
          <Button onClick={launchWhatsAppSignup} className="w-full">
            Signup using WhatsApp
          </Button>
        </div>
      </div>
      {processWabaOnboardingMutation.isLoading ? <FullScreenLoader /> : null}
    </MobileContainer>
  ) : null;
};

export default WabaOnboarding;
