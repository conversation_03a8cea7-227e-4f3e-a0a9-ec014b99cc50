import ContactUsComp from '@/components/home_pages/groweasy_old/ContactUsComp';
import FooterComp from '@/components/home_pages/groweasy/FooterDarkComp';
import NavigationHeader from '@/components/lib/NavigationHeader';
import Head from 'next/head';
import Image from 'next/image';
import Link from 'next/link';
import { getMetaTags } from 'src/utils';
import { BsGraphUp } from 'react-icons/bs';

const content = [
  {
    title: '🎯 AI-Powered Lead Generation',
    description:
      'Transform your digital marketing strategy with our cutting-edge AI technology. GrowEasy automates lead generation, creating high-converting Facebook and Instagram campaigns in under 5 minutes. Our machine learning algorithms optimize ad creatives, targeting, and conversion rates for maximum ROI.',
  },
  {
    title: '⏱️ Instant Campaign Deployment',
    description:
      'Launch professional marketing campaigns instantly with our automated platform. Watch real-time leads flow in as our AI optimizes your Facebook Ads, Instagram marketing, and social media advertising for peak performance and customer acquisition.',
  },
  {
    title: '🚀 Scale Your Business Growth',
    description:
      'Accelerate your business growth with data-driven marketing automation. Our SaaS platform delivers consistent lead generation, improved conversion rates, and measurable results that drive revenue growth for businesses of all sizes.',
  },
];

const features = [
  {
    icon: '🤖',
    title: 'AI Marketing Automation',
    description:
      'Advanced machine learning algorithms that optimize your campaigns automatically',
  },
  {
    icon: '📊',
    title: 'Real-Time Analytics',
    description:
      'Comprehensive dashboard with actionable insights and performance metrics',
  },
  {
    icon: '🎯',
    title: 'Precision Targeting',
    description:
      'Smart audience segmentation and targeting for maximum conversion rates',
  },
  {
    icon: '💰',
    title: 'Cost-Effective Solutions',
    description:
      'Reduce marketing costs while increasing lead quality and conversion rates',
  },
];

const founders = [
  {
    name: 'Tej Pandya',
    designation: 'Co-founder and CEO',
    intro: 'Formerly at INDmoney, 5paisa, ShopClues',
    education: [
      {
        degree: 'Master of Business Administration - MBA, Analytics',
        college: "SVKM's Narsee Monjee Institute of Management Studies (NMIMS)",
        image_url: '/images/about-us/nmims.png',
        url: 'https://www.nmims.edu/',
      },
      {
        degree: 'Bachelor of Engineering - BE, Mechanical Engineering',
        college:
          'LDRP Institute of Technology & Research, Gujarat Technological University',
        image_url: '/images/about-us/ldrp-itr.jpeg',
        url: 'https://www.ldrp.ac.in/',
      },
    ],
    about: '',
    linkedin: 'https://www.linkedin.com/in/tejpandya/',
    image_url: '/images/about-us/tej-pandya.jpeg',
  },
  {
    name: 'Varun Kumar',
    designation: 'Co-founder and CTO',
    intro: 'Formerly at INDmoney, Goibibo, Oracle',
    education: [
      {
        degree:
          'Bachelor of Technology (B.Tech.), Information Technology, 2013 - 2017',
        college: 'NIT Kurukshetra',
        image_url: '/images/about-us/nitkkr.png',
        url: 'https://nitkkr.ac.in/',
      },
    ],
    about: '',
    linkedin: 'https://www.linkedin.com/in/varunon9/',
    image_url: '/images/about-us/varun.jpeg',
  },
];

const investors = [
  {
    name: 'StartupLift',
    logo: '/images/about-us/startuplift.jpeg',
    alt: 'StartupLift',
    url: 'https://www.startuplift.in/',
    desc: 'Empowering startups with strategic guidance and growth acceleration programs.',
    cta: 'Visit StartupLift →',
  },
  {
    name: 'Build3 Impact Accelerator',
    logo: '/images/about-us/build3.png',
    alt: 'Build3 Impact Accelerator',
    url: 'https://www.build3.org/accelerator',
    desc: 'Accelerating impact-driven startups with technology and innovation focus.',
    cta: 'Visit Build3 →',
  },
  {
    name: 'Mediatech Accelerator',
    logo: '/images/about-us/iiml-logo.png',
    alt: 'IIM Incubator',
    url: 'https://www.iimlincubator.com/',
    desc: 'Joint initiative by IIM Lucknow and Graphisads fostering mediatech innovation.',
    cta: 'Visit IIM Incubator →',
  },
];

const AboutUs = () => {
  return (
    <div className="min-h-screen">
      <Head>{getMetaTags('/about-us')}</Head>
      <h3 className="sr-only">
        GrowEasy is a leading AI-powered marketing platform that automates lead
        generation, conversion optimization, and ROI maximization for
        businesses. Our cutting-edge technology leverages machine learning
        algorithms to create high-converting Facebook and Instagram campaigns,
        optimize ad creatives, targeting, and conversion rates for maximum ROI.
      </h3>
      <NavigationHeader />
      <div className="max-w-7xl mx-auto px-4 py-16 md:py-24 sm:px-6 lg:px-8 md:grid grid-cols-6 gap-8">
        <div className="col-span-4 flex flex-col">
          <div className="px-5 bg-teal-200/30 border border-teal-600 w-fit rounded-full py-1 mb-4 sm:mb-6 tracking-wide">
            About Us
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-tight">
            We Revolutionize Digital Marketing with AI
          </h1>
          <p className="md:text-2xl mb-4 mx-auto opacity-70">
            Empowering businesses with cutting-edge AI technology for automated
            lead generation, conversion optimization, and scalable growth
            solutions.
          </p>
          <div className="flex flex-wrap gap-4 text-xs sm:text-sm font-medium">
            <span className="bg-teal-100/70 px-4 py-2 rounded-full cursor-pointer">
              Facebook Ads Automation
            </span>
            <span className="bg-teal-100/70 px-4 py-2 rounded-full cursor-pointer">
              Instagram Marketing
            </span>
            <span className="bg-teal-100/70 px-4 py-2 rounded-full cursor-pointer">
              Lead Generation
            </span>
            <span className="bg-teal-100/70 px-4 py-2 rounded-full cursor-pointer">
              ROI Optimization
            </span>
          </div>
          <Link href={'/login'}>
            <button className="mt-4 w-fit py-3 md:px-10 rounded-full bg-primary text-white px-6 font-semibold md:text-xl hover:bg-primary2 transition-all active:scale-95">
              Start Now
            </button>
          </Link>
        </div>
        <div className="col-span-2">
          <Image
            src="/images/Marketing consulting-cuate.svg"
            alt="Groweasy Ads Marketing Illustration - AI Marketing Automation"
            width={380}
            height={380}
            fetchPriority="high"
            priority
            sizes="(max-width: 768px) 100vw, 380px"
            className="w-full h-auto"
          />
        </div>
      </div>

      {/* Mission Section */}
      <div className="py-16 md:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="order-2 lg:order-1">
              <Image
                src="/images/img7.avif"
                alt="Groweasy marketing mission - AI-powered marketing for all businesses"
                width={600}
                height={400}
                loading="lazy"
                className="w-full h-auto rounded-xl shadow-lg"
              />
            </div>
            <div className="order-1 lg:order-2">
              <div className="px-5 bg-teal-200/30 border border-teal-600 w-fit rounded-full py-1 mb-6 tracking-wide">
                Our Mission
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Democratizing AI-Powered Marketing
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                We believe every business deserves access to enterprise-level
                marketing automation. GrowEasy transforms complex digital
                marketing into simple, AI-driven solutions that deliver
                measurable results for businesses of all sizes.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Investors and Advisors Section */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="px-5 bg-teal-200/30 border border-teal-600 w-fit rounded-full py-1 mb-6 mx-auto tracking-wide inline-flex items-center gap-2">
              Our Supporters <BsGraphUp />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Backed by Leading Investors & Accelerators
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Trusted and supported by top-tier accelerators and innovation hubs
              that recognize our potential to transform digital marketing.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {investors.map((inv, idx) => (
              <div
                key={idx}
                className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all text-center group"
              >
                <Image
                  src={inv.logo}
                  alt={inv.alt + ' - GrowEasy Investor'}
                  width={150}
                  height={150}
                  fetchPriority="low"
                  loading="lazy"
                  className="mx-auto mb-6 rounded-md shadow-md w-40 h-40 hover:scale-105 transition-all duration-300 object-contain"
                />
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {inv.name}
                </h3>
                <p className="text-gray-600 mb-4 text-sm">{inv.desc}</p>
                <a
                  href={inv.url}
                  target="_blank"
                  className="text-primary2 hover:underline font-medium"
                >
                  {inv.cta}
                </a>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Features Grid */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Why Leading Businesses Choose GrowEasy
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our AI-powered platform delivers superior results through advanced
              automation, real-time optimization, and data-driven insights.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* GrowEasy in Media Section */}
      <div className="py-16 md:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="px-5 bg-teal-200/30 border border-teal-600 w-fit rounded-full py-1 mb-6 tracking-wide">
                GrowEasy in Media
              </div>
              <h2 className="text-3xl md:text-4xl font-semibold tracking-tight text-gray-900 mb-6">
                Featured on <span className="font-black">INC</span>
                <span className="bg-red font-black rounded-lg px-1 text-white">
                  91
                </span>
                : AI Marketing Platform Secures Investment
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                GrowEasy has raised a fresh round of investment from
                StartupLift, marking a significant step forward in empowering
                small and mid-sized businesses with AI-driven marketing
                automation.
              </p>
              <blockquote className="border-l-4 border-teal-500 pl-6 mb-6">
                <p className="text-sm italic text-gray-700 mb-1">
                  &quot;GrowEasy empowers anyone — from D2C founders to SaaS
                  startups — to launch high-ROI campaigns effortlessly. We are
                  thrilled to join Tej and Varun on their journey to accelerate
                  marketing for everyone, with greater speed and
                  simplicity.&quot;
                </p>
                <cite className="text-xs font-semibold text-gray-900">
                  — Shekhar Sahu, serial entrepreneur and another StartupLift
                  partner,
                </cite>
              </blockquote>
              <a
                href="https://www.inc91.com/ai-marketing-platform-groweasy-secures-investment-from-startuplift-to-revolutionize-digital-marketing-for-indian-small-businesses"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-teal-600 hover:text-teal-800 hover:underline transition-all font-bold"
              >
                Read the full article on Inc91 →
              </a>
            </div>
            <div>
              <Image
                src="https://www.inc91.com/uploads/images/202505/image_750x_6818db7214171.jpg"
                alt="GrowEasy featured on Inc91 - AI Marketing Platform News"
                width={550}
                height={400}
                fetchPriority="low"
                loading="lazy"
                className="w-full h-auto rounded-xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Content Sections */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {content.map((item, index) => (
              <div
                key={index}
                className="bg-gradient-to-br from-teal-50 to-gray-50 p-8 rounded-xl"
              >
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {item.title}
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Founders Section */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Meet Our Visionary Founders
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Led by industry veterans with deep expertise in fintech,
              e-commerce, and AI technology, bringing decades of combined
              experience in scaling digital platforms.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto">
            {founders.map((founder, index) => (
              <div
                key={index}
                className="bg-white px-6 md:px-8 py-10 rounded-xl shadow-md hover:shadow-xl transition-all text-center cursor-pointer"
              >
                <div className="relative inline-block mb-6">
                  <div className="w-32 h-32 rounded-full overflow-hidden mx-auto ring-4 ring-teal-100">
                    <Link href={founder.linkedin} target="_blank">
                      <Image
                        src={founder.image_url}
                        width={128}
                        height={128}
                        fetchPriority="low"
                        loading="lazy"
                        alt={founder.name}
                        className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                      />
                    </Link>
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  {founder.name}
                </h3>
                <p className="text-lg font-semibold text-primary2 mb-3">
                  {founder.designation}
                </p>
                <p className="text-gray-600 mb-4">{founder.intro}</p>
                <div className="border-t pt-4">
                  <Link
                    target="_blank"
                    href={founder.education?.[0]?.url}
                    className="text-teal-600 hover:text-teal-800 transition-colors"
                  >
                    <div className="text-sm">
                      <p className="font-medium">
                        {founder.education?.[0]?.degree}
                      </p>
                      <p className="text-gray-500">
                        {founder.education?.[0]?.college}
                      </p>
                    </div>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="py-16 bg-gradient-to-r from-teal-600 to-primary2 rounded-md text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl md:text-4xl font-bold mb-2">5 min</div>
              <div className="text-sm md:text-base opacity-90">
                Campaign Launch Time
              </div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold mb-2">AI</div>
              <div className="text-sm md:text-base opacity-90">
                Powered Optimization
              </div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold mb-2">24/7</div>
              <div className="text-sm md:text-base opacity-90">
                Automated Monitoring
              </div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold mb-2">ROI</div>
              <div className="text-sm md:text-base opacity-90">
                Maximized Returns
              </div>
            </div>
          </div>
        </div>
      </div>

      <ContactUsComp />
      <FooterComp />
    </div>
  );
};

export default AboutUs;
