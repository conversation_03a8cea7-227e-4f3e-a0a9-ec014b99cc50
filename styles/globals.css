@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .new-scroll-bar::-webkit-scrollbar {
    width: 15px;
  }

  .new-scroll-bar::-webkit-scrollbar-track {
    border-right: 6px solid #e7e3e3;
  }

  .new-scroll-bar::-webkit-scrollbar-thumb {
    border-right: 6px solid #979797;
  }
}

@font-face {
  font-family: 'durerregular';
  src: url('/fonts/durer-webfont.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/*@font-face {
  font-family: 'inter';
  src: url('/fonts/Inter-Regular.ttf') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'inter';
  src: url('/fonts/Inter-Bold.ttf') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}*/

html {
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: 'Inter';
}

.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
  -webkit-appearance: none;
}

@keyframes ModalB2T {
  0% {
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
  }

  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

.animate-ModalB2T {
  -webkit-animation: ModalB2T 0.5s forwards;
  animation: ModalB2T 0.5s forwards;
}

@keyframes preLoader {
  50% {
    background-size: 80%;
  }

  100% {
    background-position: 125% 0;
  }
}

.animate-preLoader {
  -webkit-animation: preLoader 1.2s ease-in-out infinite;
  animation: preLoader 1.2s ease-in-out infinite;
}

#mobile-container-content {
  max-width: 550px;
}

/* iPads only */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
  html {
    font-size: 20px; /* Slightly increase the base font size */
  }

  #mobile-container-content {
    max-width: 700px;
  }
}

#blog-details h1 {
  font-size: 1.875rem; /* 30px */
  line-height: 2.25rem; /* 36px */
  margin: 32px 0 16px 0;
  font-weight: 600;
}

#blog-details h2 {
  font-size: 1.5rem; /* 24px */
  line-height: 2rem; /* 32px */
  margin: 28px 0 14px 0;
  font-weight: 500;
}

#blog-details h3 {
  font-size: 1.25rem; /* 20px */
  line-height: 1.75rem; /* 28px */
  margin: 24px 0 12px 0;
  font-weight: 500;
}

#blog-details ul {
  list-style: disc;
  margin: 0 8px;
  padding: 0 8px;
}

#blog-details p {
  font-size: 1rem; /* 16px */
  line-height: 1.5rem; /* 24px */
  margin: 16px 0 8px 0;
  font-weight: 400;
}

#blog-details img {
  max-width: 512px;
  margin: 36px auto 18px auto;
}

#blog-details a {
  color: #0070cc;
}

.videoOrderGradientHeadingLeft {
  background: linear-gradient(90deg, #286053 0%, #2e9340 57%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.videoOrderGradientHeadingTop {
  background: linear-gradient(180deg, #286053 0%, #2e9340 57%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media print {
  /* Example: Hide specific elements from printing */
  .hide-on-print {
    display: none;
  }

  #mobile-container {
    padding: 0;
  }

  #mobile-container-content {
    max-width: 100%;
    border-radius: 0;
  }
}

@keyframes moveLeft {
  to {
    left: -160px;
  }
}

.custom-range-input {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 10px;
  background: linear-gradient(
    to right,
    #f57141 0%,
    #f57141 var(--value),
    #e5e7eb var(--value),
    #e5e7eb 100%
  );
  border-radius: 5px;
  outline: none;
}

.custom-range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 22px;
  height: 22px;
  background: #f57141;
  border-radius: 50%;
  border: 5px solid white;
  box-shadow: 0 0 4px 0px black;
  cursor: pointer;
}

/* For Firefox */
.custom-range-input::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: #f57141;
  border-radius: 50%;
  border: 5px solid white;
  box-shadow: 0 0 4px 0px black;
  cursor: pointer;
}

/* For IE */
.custom-range-input::-ms-thumb {
  width: 12px;
  height: 12px;
  background: #f57141;
  border-radius: 50%;
  border: 5px solid white;
  box-shadow: 0 0 4px 0px black;
  cursor: pointer;
}

.custom-radio-button-container {
  height: 24px;
  width: 24px;
  position: relative;
}

.custom-radio-button-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.custom-radio-button-container .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 24px;
  width: 24px;
  background-color: #fff;
  border: 2px solid #294744;
  border-radius: 5px;
}

.custom-radio-button-container:hover .checkmark {
  background-color: #ccc;
}

.custom-radio-button-container input:checked + .checkmark {
  background-color: #294744;
}

.custom-radio-button-container .checkmark:after {
  content: '';
  position: absolute;
  display: none;
}

.custom-radio-button-container input:checked + .checkmark:after {
  display: block;
}

.custom-radio-button-container .checkmark:after {
  left: 6px;
  top: 1px;
  width: 8px;
  height: 15px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-48px);
  }
}

.initial-bouncy-animation {
  animation: bounce 1.3s ease-in;
}

@keyframes bg-pan {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.animate-bg-pan {
  animation: bg-pan 2.5s ease-in infinite;
  /* we will use with bg-[length:200%_200%] animate-bg-pan */
}

::selection {
  background: #325a56;
  color: #fff;
}

.heroclamp {
  width: clamp(300px, 50vw, 680px);
}

.text-glow {
  text-shadow: 0 2px 12px rgba(147, 241, 205, 0.35);
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.heroclamp {
  width: clamp(300px, 50vw, 680px);
}

.text-glow {
  text-shadow: 0 2px 12px rgba(147, 241, 205, 0.35);
}
