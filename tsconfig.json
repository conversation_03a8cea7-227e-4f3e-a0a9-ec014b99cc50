{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/components/*": [
        "src/components/*"
      ],
      "@/images/*": [
        "public/images/*"
      ],
      "@/constants/*": [
        "src/constants/*"
      ],
    },
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": false,
    "noEmit": true,
    "incremental": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "plugins": [
      {
        "name": "next"
      }
    ],
    "strictNullChecks": false
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    "./.eslintrc.js",
    "_error.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules"
  ]
}
