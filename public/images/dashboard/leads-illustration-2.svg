<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 27.5.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 500 500" style="enable-background:new 0 0 500 500;" xml:space="preserve">
<g id="BACKGROUND">
	<rect style="fill:#FFFFFF;" width="500" height="500"/>
	<g>
		<g>
			<g>
				<path style="fill:#C7EAFC;" d="M46.815,381.417c-28.349-46.104-27.307-110.598-2.094-157.692
					c9.689-18.099,30.99-20.048,49.418-18.881c16.036,1.015,34.576,0.896,49.203-7.61c18.098-10.524,23.622-32.347,31.294-50.399
					c32.463-76.377,154.779-88.887,196.165-12.961c16.764,30.756,42.807,55.969,65.871,82.455
					c24.645,28.301,45.441,56.817,34.062,96.067c-22.178,76.499-55.161,90.252-55.162,90.252
					c-58.881,0.065-117.764-0.284-176.644-0.37c-36.69-0.054-73.326,2.522-110.014,2.543
					C100.915,404.836,63.801,409.041,46.815,381.417z"/>
			</g>
			<g>
				<ellipse style="fill:#C1DDED;" cx="252.135" cy="406.708" rx="222.184" ry="10.449"/>
			</g>
		</g>
		<g>
			<g>
				<path style="fill:#5BB039;" d="M254.656,84.485h-65.433c-2.567,0-4.648,2.081-4.648,4.648v11.353
					c0,2.567,2.081,4.648,4.648,4.648l1.892,7.758l7.053-7.758h56.488c2.567,0,4.648-2.081,4.648-4.648V89.134
					C259.304,86.566,257.223,84.485,254.656,84.485z"/>
				<g>
					
						<line style="fill:none;stroke:#FFFFFF;stroke-width:1.0759;stroke-miterlimit:10;" x1="190.024" y1="88.56" x2="253.855" y2="88.56"/>
					
						<line style="fill:none;stroke:#FFFFFF;stroke-width:1.0759;stroke-miterlimit:10;" x1="190.024" y1="91.381" x2="253.855" y2="91.381"/>
					
						<line style="fill:none;stroke:#FFFFFF;stroke-width:1.0759;stroke-miterlimit:10;" x1="190.024" y1="94.203" x2="253.855" y2="94.203"/>
					
						<line style="fill:none;stroke:#FFFFFF;stroke-width:1.0759;stroke-miterlimit:10;" x1="190.024" y1="97.024" x2="253.855" y2="97.024"/>
					
						<line style="fill:none;stroke:#FFFFFF;stroke-width:1.0759;stroke-miterlimit:10;" x1="190.024" y1="99.845" x2="253.855" y2="99.845"/>
				</g>
			</g>
			<g>
				<path style="fill:#FF795C;" d="M265.529,105.836h57.293c2.248,0,4.07,1.822,4.07,4.07v9.941c0,2.248-1.822,4.07-4.07,4.07
					l-1.656,6.793l-6.176-6.793h-49.461c-2.248,0-4.07-1.822-4.07-4.07v-9.941C261.459,107.658,263.281,105.836,265.529,105.836z"/>
				<g>
					
						<line style="fill:none;stroke:#FFFFFF;stroke-width:1.0759;stroke-miterlimit:10;" x1="322.12" y1="109.403" x2="266.231" y2="109.403"/>
					
						<line style="fill:none;stroke:#FFFFFF;stroke-width:1.0759;stroke-miterlimit:10;" x1="322.12" y1="111.873" x2="266.231" y2="111.873"/>
					
						<line style="fill:none;stroke:#FFFFFF;stroke-width:1.0759;stroke-miterlimit:10;" x1="322.12" y1="114.344" x2="266.231" y2="114.344"/>
					
						<line style="fill:none;stroke:#FFFFFF;stroke-width:1.0759;stroke-miterlimit:10;" x1="322.12" y1="116.814" x2="266.231" y2="116.814"/>
					
						<line style="fill:none;stroke:#FFFFFF;stroke-width:1.0759;stroke-miterlimit:10;" x1="322.12" y1="119.284" x2="266.231" y2="119.284"/>
				</g>
			</g>
		</g>
		<g>
			<path style="fill:#85BFF1;" d="M430.979,145.332c-0.267-2.117,1.232-4.049,3.348-4.316c0.134-0.017,0.267-0.026,0.399-0.03
				c-0.695-1.987-1.692-3.838-2.936-5.5c-0.009,0.01-0.017,0.021-0.027,0.031c-1.466,1.55-3.911,1.619-5.461,0.153
				c-1.526-1.443-1.613-3.835-0.215-5.388c-1.669-1.024-3.5-1.816-5.452-2.331c-0.321,1.944-2.073,3.361-4.079,3.221
				c-2.005-0.14-3.544-1.787-3.592-3.756c-2.599,0.309-5.06,1.109-7.283,2.311c1.057,1.598,0.791,3.769-0.698,5.053
				c-1.545,1.332-3.84,1.222-5.256-0.202c-1.264,1.503-2.318,3.193-3.113,5.03c1.965,0.471,3.254,2.391,2.904,4.411
				c-0.364,2.102-2.363,3.511-4.466,3.147c-0.037-0.006-0.072-0.017-0.109-0.025c-0.064,2.131,0.201,4.2,0.754,6.157
				c0.065-0.042,0.128-0.086,0.196-0.125c1.855-1.053,4.213-0.403,5.266,1.453c1.053,1.855,0.402,4.213-1.453,5.266
				c-0.187,0.106-0.38,0.19-0.575,0.262c1.35,1.768,2.984,3.312,4.84,4.564c0.043-0.241,0.107-0.481,0.198-0.718
				c0.765-1.992,3-2.986,4.991-2.221c1.992,0.765,2.986,3,2.221,4.992c-0.131,0.342-0.308,0.651-0.517,0.93
				c1.008,0.228,2.047,0.382,3.11,0.456c1.336,0.093,2.65,0.052,3.931-0.105c-0.193-0.285-0.353-0.599-0.467-0.941
				c-0.673-2.024,0.423-4.211,2.447-4.884c2.025-0.673,4.211,0.423,4.884,2.447c0.128,0.384,0.191,0.773,0.197,1.157
				c2.099-1.113,3.986-2.582,5.579-4.333c-0.302-0.097-0.598-0.229-0.88-0.406c-1.805-1.137-2.347-3.522-1.21-5.327
				c1.137-1.805,3.522-2.347,5.328-1.21c0.264,0.166,0.498,0.362,0.707,0.576c0.722-1.866,1.181-3.869,1.328-5.969
				c0.014-0.2,0.018-0.399,0.027-0.598c-0.179,0.05-0.362,0.091-0.551,0.115C433.179,148.948,431.246,147.449,430.979,145.332z
				 M414.723,157.447c-5.362-0.374-9.405-5.024-9.031-10.386c0.374-5.362,5.024-9.405,10.385-9.031
				c5.362,0.374,9.405,5.024,9.031,10.386C424.735,153.778,420.085,157.821,414.723,157.447z"/>
			<path style="fill:#85BFF1;" d="M390.336,200.477c-0.46-3.641,2.119-6.964,5.759-7.424c0.23-0.029,0.459-0.046,0.686-0.051
				c-1.196-3.417-2.911-6.601-5.049-9.459c-0.016,0.018-0.03,0.036-0.046,0.054c-2.521,2.666-6.726,2.784-9.392,0.263
				c-2.626-2.483-2.774-6.595-0.37-9.266c-2.871-1.761-6.019-3.124-9.378-4.009c-0.551,3.344-3.566,5.781-7.015,5.54
				c-3.449-0.241-6.096-3.073-6.178-6.461c-4.47,0.531-8.703,1.907-12.525,3.974c1.819,2.748,1.36,6.482-1.201,8.69
				c-2.657,2.291-6.604,2.102-9.04-0.348c-2.175,2.585-3.987,5.493-5.354,8.651c3.38,0.811,5.596,4.113,4.995,7.586
				c-0.626,3.615-4.065,6.039-7.681,5.412c-0.064-0.011-0.124-0.03-0.187-0.042c-0.11,3.664,0.346,7.224,1.296,10.59
				c0.112-0.072,0.22-0.148,0.338-0.215c3.191-1.811,7.246-0.692,9.058,2.499c1.811,3.191,0.692,7.246-2.499,9.058
				c-0.321,0.182-0.653,0.327-0.988,0.451c2.323,3.041,5.133,5.697,8.325,7.849c0.073-0.414,0.184-0.827,0.34-1.235
				c1.316-3.425,5.16-5.135,8.585-3.819c3.425,1.316,5.135,5.16,3.819,8.585c-0.226,0.588-0.53,1.12-0.89,1.6
				c1.734,0.392,3.52,0.657,5.348,0.785c2.298,0.16,4.557,0.089,6.761-0.18c-0.331-0.491-0.607-1.03-0.802-1.619
				c-1.157-3.482,0.727-7.243,4.209-8.4c3.482-1.158,7.243,0.727,8.401,4.209c0.219,0.66,0.328,1.33,0.339,1.99
				c3.61-1.914,6.855-4.441,9.595-7.452c-0.52-0.167-1.029-0.394-1.513-0.698c-3.105-1.956-4.036-6.058-2.081-9.163
				c1.956-3.105,6.058-4.036,9.163-2.08c0.454,0.286,0.856,0.623,1.215,0.991c1.241-3.209,2.031-6.654,2.283-10.266
				c0.024-0.345,0.032-0.686,0.046-1.029c-0.308,0.085-0.622,0.157-0.948,0.198C394.119,206.696,390.795,204.118,390.336,200.477z
				 M362.377,221.314c-9.222-0.644-16.176-8.641-15.532-17.862s8.641-16.176,17.862-15.532c9.222,0.643,16.175,8.641,15.532,17.862
				S371.599,221.957,362.377,221.314z"/>
			<path style="fill:#85BFF1;" d="M78.633,266.317c-0.304-2.409,1.402-4.608,3.81-4.912c0.152-0.019,0.304-0.03,0.454-0.034
				c-0.791-2.261-1.925-4.367-3.341-6.258c-0.011,0.012-0.02,0.024-0.03,0.036c-1.668,1.764-4.45,1.842-6.214,0.174
				c-1.737-1.643-1.835-4.363-0.245-6.13c-1.899-1.165-3.982-2.067-6.204-2.652c-0.365,2.212-2.359,3.824-4.641,3.665
				c-2.281-0.159-4.033-2.033-4.087-4.274c-2.958,0.351-5.758,1.262-8.287,2.629c1.203,1.818,0.9,4.288-0.795,5.749
				c-1.758,1.516-4.369,1.39-5.981-0.23c-1.439,1.71-2.638,3.634-3.542,5.723c2.236,0.536,3.702,2.721,3.304,5.019
				c-0.414,2.392-2.689,3.995-5.081,3.581c-0.042-0.007-0.082-0.02-0.124-0.028c-0.072,2.424,0.229,4.779,0.857,7.006
				c0.074-0.048,0.146-0.098,0.223-0.142c2.111-1.198,4.794-0.458,5.992,1.653c1.198,2.111,0.458,4.794-1.653,5.992
				c-0.212,0.121-0.432,0.216-0.654,0.298c1.537,2.012,3.396,3.769,5.508,5.193c0.048-0.274,0.122-0.547,0.225-0.817
				c0.871-2.266,3.413-3.397,5.68-2.526c2.266,0.871,3.397,3.413,2.527,5.68c-0.149,0.389-0.351,0.741-0.589,1.059
				c1.147,0.259,2.329,0.435,3.538,0.519c1.52,0.106,3.015,0.059,4.473-0.119c-0.219-0.325-0.401-0.681-0.531-1.071
				c-0.766-2.304,0.481-4.792,2.785-5.558c2.304-0.766,4.792,0.481,5.558,2.785c0.145,0.437,0.217,0.88,0.224,1.317
				c2.388-1.266,4.535-2.938,6.348-4.93c-0.344-0.111-0.681-0.26-1.001-0.462c-2.054-1.294-2.67-4.008-1.376-6.062
				c1.294-2.054,4.008-2.67,6.062-1.376c0.301,0.189,0.566,0.412,0.804,0.656c0.821-2.123,1.344-4.402,1.511-6.792
				c0.016-0.228,0.021-0.454,0.03-0.681c-0.204,0.057-0.411,0.104-0.627,0.131C81.137,270.431,78.938,268.726,78.633,266.317z
				 M60.137,280.102c-6.101-0.426-10.701-5.716-10.275-11.817c0.426-6.101,5.716-10.701,11.817-10.276
				c6.101,0.426,10.701,5.716,10.276,11.817C71.528,275.927,66.238,280.528,60.137,280.102z"/>
		</g>
		<g>
			<g>
				<path style="fill:#5BB039;" d="M425.25,360.999c-3.056-7.537-10.802-8.577-17.909-7.81c-4.995,0.539-21.684,7.317-25.627,3.427
					c-5.19-5.123,12.791-6.565,14.794-6.752c7.909-0.741,15.913-0.956,21.975-6.881c4.501-4.4,7.044-10.932,6.124-17.214
					c-0.953-6.51-6.727-8.895-12.404-6.413c-4.153,1.818-9.669,4.23-13.01-0.224c-2.482-3.313-0.94-8.25,1.487-10.921
					c10.043-11.052,17.043-18.006,16.55-34.314c-0.395-13.084-7.613-23.78-21.879-22.005c-9.159,1.141-16.639,8.306-21.165,15.939
					c-4.681,7.895-3.969,17.255-4.995,26.041c-0.419,3.585-3.13,7.993-7.26,8.297c-5.553,0.405-7.355-5.339-8.712-9.665
					c-1.856-5.912-7.446-8.7-12.982-5.144c-5.341,3.431-8.598,9.639-8.961,15.922c-0.488,8.463,4.607,14.641,9.241,21.092
					c1.174,1.634,11.895,16.141,4.623,15.591c-5.524-0.42-11.372-17.456-14.245-21.577c-4.089-5.864-9.96-11.022-17.651-8.378
					c-19.327,6.646-9.616,40.81-4.524,54.122c3.105,8.118,7.528,16.484,14.051,22.384c17.123,15.486,42.479,21.549,64.631,13.436
					C400.784,395.052,432.93,379.941,425.25,360.999z"/>
				<g>
					<path style="fill:#D0EEFF;" d="M340.759,398.085c2.207-5.362,4.524-10.673,6.783-16.011l6.963-15.93
						c4.644-10.619,9.423-21.176,14.203-31.733c4.816-10.54,9.654-21.071,14.592-31.555c4.914-10.496,9.939-20.941,15.075-31.336
						c-4.467,10.699-9.044,21.348-13.733,31.947c-4.665,10.609-9.429,21.173-14.216,31.727c-4.823,10.537-9.646,21.074-14.605,31.55
						l-7.441,15.712C345.837,387.664,343.353,392.9,340.759,398.085z"/>
				</g>
				<g>
					<path style="fill:#D0EEFF;" d="M316.342,328.317c1.078,5.409,2.582,10.723,4.336,15.941c1.734,5.226,3.783,10.345,6.173,15.295
						c1.209,2.468,2.51,4.888,3.884,7.265c1.402,2.36,2.904,4.658,4.514,6.876c1.639,2.197,3.39,4.307,5.268,6.298
						c1.907,1.962,3.941,3.793,6.106,5.458l-0.188-0.085c5.492,0.946,11.173,1.131,16.78,0.873
						c5.616-0.269,11.22-0.984,16.713-2.219c5.484-1.241,10.923-2.803,16.117-5c2.655-0.962,5.167-2.249,7.722-3.449
						c1.296-0.566,2.493-1.32,3.731-1.997l3.692-2.08l-3.634,2.182c-1.22,0.712-2.4,1.502-3.681,2.104
						c-2.525,1.273-5.013,2.634-7.651,3.674c-5.184,2.291-10.63,3.946-16.136,5.282c-5.511,1.356-11.17,2.046-16.832,2.366
						c-5.672,0.281-11.358,0.15-17.011-0.815l-0.101-0.017l-0.088-0.068c-2.2-1.71-4.261-3.585-6.188-5.588
						c-1.897-2.032-3.661-4.179-5.308-6.412c-1.619-2.253-3.123-4.583-4.526-6.971c-1.385-2.399-2.65-4.863-3.829-7.366
						c-2.325-5.021-4.315-10.19-5.957-15.464C318.631,339.117,317.271,333.755,316.342,328.317z"/>
				</g>
				<g>
					<path style="fill:#D0EEFF;" d="M343.023,299.23c1.461,3.58,3.101,7.082,4.901,10.497c1.778,3.426,3.717,6.764,5.811,9.999
						c2.087,3.24,4.368,6.348,6.806,9.321c2.445,2.972,5.173,5.687,8.044,8.229l-0.225-0.106c3.655,0.525,7.439,0.58,11.17,0.449
						c3.743-0.141,7.472-0.552,11.183-1.091c1.845-0.331,3.705-0.594,5.533-1.023l2.751-0.6l2.727-0.705
						c1.832-0.422,3.612-1.03,5.42-1.546c1.792-0.566,3.559-1.211,5.348-1.801c-1.752,0.683-3.493,1.41-5.27,2.035
						c-1.79,0.583-3.555,1.26-5.376,1.751l-2.721,0.776l-2.751,0.666c-1.828,0.473-3.693,0.78-5.544,1.156
						c-3.721,0.654-7.487,1.055-11.264,1.236c-3.784,0.158-7.56,0.14-11.365-0.377l-0.126-0.017l-0.1-0.089
						c-2.909-2.594-5.645-5.376-8.097-8.396c-2.426-3.033-4.754-6.153-6.758-9.478c-2.033-3.304-3.908-6.701-5.61-10.181
						C345.844,306.439,344.283,302.888,343.023,299.23z"/>
				</g>
			</g>
			<g>
				<path style="fill:#5BB039;" d="M74.769,360.999c3.056-7.537,10.802-8.577,17.91-7.81c4.995,0.539,21.683,7.317,25.627,3.427
					c5.19-5.123-12.791-6.565-14.794-6.752c-7.908-0.741-15.913-0.956-21.975-6.881c-4.501-4.4-7.044-10.932-6.124-17.214
					c0.954-6.51,6.727-8.895,12.405-6.413c4.153,1.818,9.669,4.23,13.01-0.224c2.481-3.313,0.94-8.25-1.487-10.921
					c-10.043-11.052-17.043-18.006-16.55-34.314c0.395-13.084,7.613-23.78,21.879-22.005c9.159,1.141,16.639,8.306,21.165,15.939
					c4.681,7.895,3.969,17.255,4.995,26.041c0.419,3.585,3.13,7.993,7.26,8.297c5.553,0.405,7.355-5.339,8.712-9.665
					c1.856-5.912,7.446-8.7,12.982-5.144c5.342,3.431,8.598,9.639,8.961,15.922c0.488,8.463-4.607,14.641-9.241,21.092
					c-1.174,1.634-11.895,16.141-4.623,15.591c5.524-0.42,11.372-17.456,14.245-21.577c4.089-5.864,9.96-11.022,17.651-8.378
					c19.327,6.646,9.616,40.81,4.524,54.122c-3.105,8.118-7.528,16.484-14.051,22.384c-17.123,15.486-42.479,21.549-64.631,13.436
					C99.234,395.052,67.089,379.941,74.769,360.999z"/>
				<g>
					<path style="fill:#D0EEFF;" d="M159.26,398.085c-2.594-5.185-5.078-10.421-7.621-15.63l-7.441-15.712
						c-4.958-10.475-9.782-21.013-14.605-31.55c-4.786-10.554-9.551-21.118-14.216-31.727c-4.689-10.598-9.266-21.247-13.733-31.947
						c5.135,10.395,10.161,20.84,15.075,31.336c4.938,10.485,9.776,21.015,14.592,31.555c4.78,10.557,9.559,21.114,14.203,31.733
						l6.963,15.93C154.736,387.412,157.053,392.724,159.26,398.085z"/>
				</g>
				<g>
					<path style="fill:#D0EEFF;" d="M183.676,328.317c-0.929,5.438-2.289,10.8-3.904,16.081c-1.643,5.274-3.632,10.444-5.957,15.464
						c-1.179,2.503-2.444,4.967-3.829,7.366c-1.403,2.389-2.908,4.719-4.526,6.971c-1.647,2.232-3.411,4.38-5.308,6.412
						c-1.927,2.003-3.988,3.879-6.188,5.588l-0.088,0.068l-0.101,0.017c-5.653,0.965-11.34,1.096-17.011,0.815
						c-5.662-0.319-11.321-1.01-16.832-2.366c-5.506-1.337-10.952-2.991-16.136-5.282c-2.638-1.039-5.126-2.401-7.651-3.674
						c-1.281-0.602-2.461-1.391-3.682-2.104l-3.634-2.182l3.692,2.08c1.238,0.678,2.435,1.431,3.731,1.997
						c2.555,1.2,5.067,2.487,7.722,3.449c5.194,2.196,10.633,3.759,16.117,5c5.493,1.234,11.096,1.95,16.713,2.219
						c5.607,0.257,11.287,0.072,16.78-0.873l-0.188,0.085c2.165-1.665,4.199-3.496,6.106-5.458c1.878-1.991,3.628-4.101,5.268-6.298
						c1.611-2.219,3.112-4.517,4.514-6.876c1.374-2.376,2.674-4.797,3.884-7.265c2.39-4.95,4.439-10.069,6.173-15.295
						C181.094,339.04,182.599,333.726,183.676,328.317z"/>
				</g>
				<g>
					<path style="fill:#D0EEFF;" d="M156.996,299.23c-1.26,3.658-2.821,7.209-4.484,10.706c-1.702,3.48-3.578,6.877-5.61,10.181
						c-2.004,3.325-4.332,6.445-6.758,9.478c-2.451,3.019-5.187,5.801-8.097,8.395l-0.1,0.089l-0.126,0.017
						c-3.805,0.517-7.581,0.535-11.365,0.377c-3.777-0.182-7.543-0.582-11.264-1.236c-1.851-0.376-3.716-0.682-5.544-1.156
						l-2.751-0.666l-2.721-0.776c-1.821-0.491-3.586-1.168-5.376-1.751c-1.777-0.625-3.517-1.352-5.27-2.035
						c1.789,0.59,3.556,1.235,5.348,1.801c1.809,0.515,3.588,1.124,5.42,1.546l2.727,0.705l2.751,0.6
						c1.827,0.429,3.688,0.691,5.533,1.023c3.711,0.539,7.44,0.95,11.183,1.091c3.731,0.131,7.515,0.076,11.17-0.449l-0.225,0.106
						c2.871-2.541,5.598-5.257,8.044-8.229c2.438-2.973,4.719-6.081,6.806-9.321c2.093-3.235,4.033-6.574,5.811-9.999
						C153.895,306.312,155.535,302.81,156.996,299.23z"/>
				</g>
			</g>
		</g>
		<g>
			<g>
				<g>
					<path style="fill:#232D68;" d="M344.936,406.708H155.083c-3.524,0-6.381-2.857-6.381-6.38V141.797
						c0-3.524,2.857-6.38,6.381-6.38h189.853c3.524,0,6.38,2.857,6.38,6.38v258.531C351.316,403.852,348.46,406.708,344.936,406.708
						z"/>
					<g>
						<path style="fill:#C7EAFC;" d="M157.707,391.323v-240.52c0-3.524,2.857-6.38,6.381-6.38h171.843c3.524,0,6.38,2.857,6.38,6.38
							v240.52c0,3.524-2.857,6.38-6.38,6.38H164.088C160.564,397.703,157.707,394.846,157.707,391.323z"/>
					</g>
				</g>
			</g>
			<path style="fill:#232D68;" d="M187.341,137.668h-27.017v-3.443c0-1.535,1.244-2.779,2.779-2.779h21.458
				c1.535,0,2.779,1.244,2.779,2.779V137.668z"/>
		</g>
		<g>
			<rect x="168.989" y="241.443" style="fill:#5BB039;" width="168.496" height="66.13"/>
			<g>
				<rect x="168.989" y="317.297" style="fill:#C1DDED;" width="168.496" height="66.13"/>
			</g>
			<g>
				<rect x="168.989" y="166.037" style="fill:#C1DDED;" width="168.496" height="66.13"/>
			</g>
		</g>
		<g>
			<g>
				<g>
					<g>
						<rect x="63.274" y="131.525" style="fill:#E36C52;" width="74.801" height="99.669"/>
						<g>
							<rect x="69.47" y="135.692" style="fill:#FFFFFF;" width="62.41" height="91.336"/>
						</g>
						<path style="fill:#232D68;" d="M112.68,126.25v-3.595c0-1.681-1.363-3.044-3.044-3.044H91.714
							c-1.681,0-3.044,1.363-3.044,3.044v3.595h-1.845c-1.931,0-3.496,1.566-3.496,3.497v9.359h34.694v-9.359
							c0-1.931-1.565-3.497-3.496-3.497H112.68z"/>
						<g>
							<g>
								
									<rect x="76.329" y="151.184" style="fill:none;stroke:#85BFF1;stroke-width:0.5379;stroke-miterlimit:10;" width="6.999" height="6.999"/>
								
									<rect x="76.329" y="164.522" style="fill:none;stroke:#85BFF1;stroke-width:0.5379;stroke-miterlimit:10;" width="6.999" height="6.999"/>
								
									<rect x="76.329" y="177.86" style="fill:none;stroke:#85BFF1;stroke-width:0.5379;stroke-miterlimit:10;" width="6.999" height="6.999"/>
								
									<rect x="76.329" y="191.198" style="fill:none;stroke:#85BFF1;stroke-width:0.5379;stroke-miterlimit:10;" width="6.999" height="6.999"/>
								
									<rect x="76.329" y="204.536" style="fill:none;stroke:#85BFF1;stroke-width:0.5379;stroke-miterlimit:10;" width="6.999" height="6.999"/>
							</g>
							<g>
								<g>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="152.553" x2="125.096" y2="152.553"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="154.683" x2="125.096" y2="154.683"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="156.814" x2="125.096" y2="156.814"/>
								</g>
								<g>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="165.891" x2="125.096" y2="165.891"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="168.021" x2="125.096" y2="168.021"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="170.152" x2="125.096" y2="170.152"/>
								</g>
								<g>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="179.229" x2="125.096" y2="179.229"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="181.359" x2="125.096" y2="181.359"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="183.49" x2="125.096" y2="183.49"/>
								</g>
								<g>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="192.567" x2="125.096" y2="192.567"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="194.698" x2="125.096" y2="194.698"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="196.828" x2="125.096" y2="196.828"/>
								</g>
								<g>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="205.905" x2="125.096" y2="205.905"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="208.036" x2="125.096" y2="208.036"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:0.269;stroke-miterlimit:10;" x1="89.741" y1="210.166" x2="125.096" y2="210.166"/>
								</g>
							</g>
						</g>
						
							<polyline style="fill:none;stroke:#5BB039;stroke-width:1.0759;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" points="
							78.271,167.372 80.034,169.502 83.778,160.595 						"/>
						
							<polyline style="fill:none;stroke:#5BB039;stroke-width:1.0759;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" points="
							78.271,206.859 80.034,208.99 83.778,200.083 						"/>
					</g>
					<g>
						<path style="fill:#FFFFFF;" d="M85.886,137.013c-0.111-0.638-0.163-1.276-0.206-1.914c-0.049-0.638-0.056-1.276-0.063-1.914
							c0.006-0.638,0.013-1.276,0.062-1.914c0.043-0.638,0.095-1.276,0.207-1.914c0.112,0.638,0.164,1.276,0.207,1.914
							c0.049,0.638,0.056,1.276,0.062,1.914c-0.007,0.638-0.014,1.276-0.063,1.914C86.049,135.738,85.997,136.375,85.886,137.013z"
							/>
					</g>
					<g>
						<path style="fill:#FFFFFF;" d="M89.741,125.999c-0.111-0.349-0.163-0.698-0.206-1.046c-0.049-0.349-0.056-0.698-0.063-1.046
							c0.006-0.349,0.013-0.698,0.062-1.046c0.043-0.349,0.095-0.698,0.207-1.046c0.112,0.349,0.164,0.698,0.207,1.046
							c0.049,0.349,0.056,0.698,0.062,1.046c-0.007,0.349-0.014,0.698-0.063,1.046C89.904,125.302,89.852,125.65,89.741,125.999z"/>
					</g>
				</g>
				<g>
					<polygon style="fill:#FFFFFF;" points="65.84,135.692 66.037,147.245 66.149,158.799 66.243,181.906 66.151,205.013 
						66.038,216.567 65.84,228.12 65.643,216.567 65.53,205.013 65.438,181.906 65.532,158.799 65.644,147.245 					"/>
				</g>
			</g>
			<g>
				<path style="fill:#FFFFFF;" d="M102.429,123.511c0,0.969-0.785,1.754-1.754,1.754c-0.969,0-1.754-0.785-1.754-1.754
					c0-0.969,0.785-1.754,1.754-1.754C101.643,121.757,102.429,122.542,102.429,123.511z"/>
			</g>
		</g>
		<g>
			<g>
				<g>
					<g>
						<rect x="165.762" y="238.215" style="fill:#FFFFFF;" width="168.496" height="66.13"/>
					</g>
				</g>
				<g>
					<g>
						
							<ellipse transform="matrix(0.2272 -0.9738 0.9738 0.2272 -106.8549 397.7137)" style="fill:#C7EAFC;" cx="197.173" cy="266.186" rx="25.843" ry="25.843"/>
					</g>
					<g>
						<g>
							<rect x="236.971" y="244.039" style="fill:#5BB039;" width="92.384" height="9.014"/>
							<g>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="260.971" x2="332.302" y2="260.971"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="265.853" x2="332.302" y2="265.853"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="270.736" x2="332.302" y2="270.736"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="275.618" x2="332.302" y2="275.618"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="280.5" x2="332.302" y2="280.5"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="285.383" x2="332.302" y2="285.383"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="290.265" x2="332.302" y2="290.265"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="295.148" x2="332.302" y2="295.148"/>
							</g>
						</g>
						<g>
							<g>
								<polygon style="fill:#FFC722;" points="182.612,294.708 183.47,296.445 185.387,296.724 184,298.076 184.327,299.985 
									182.612,299.083 180.898,299.985 181.225,298.076 179.838,296.724 181.755,296.445 								"/>
								<polygon style="fill:#FFC722;" points="189.893,294.708 190.75,296.445 192.667,296.724 191.28,298.076 191.607,299.985 
									189.893,299.083 188.178,299.985 188.505,298.076 187.118,296.724 189.035,296.445 								"/>
								<polygon style="fill:#FFC722;" points="197.173,294.708 198.03,296.445 199.947,296.724 198.56,298.076 198.887,299.985 
									197.173,299.083 195.458,299.985 195.786,298.076 194.398,296.724 196.315,296.445 								"/>
								<polygon style="fill:#FFC722;" points="204.453,294.708 205.31,296.445 207.227,296.724 205.84,298.076 206.167,299.985 
									204.453,299.083 202.738,299.985 203.066,298.076 201.679,296.724 203.596,296.445 								"/>
								<g>
									<polygon style="fill:#C1DDED;" points="211.733,294.708 212.59,296.445 214.507,296.724 213.12,298.076 213.448,299.985 
										211.733,299.083 210.018,299.985 210.346,298.076 208.959,296.724 210.876,296.445 									"/>
								</g>
							</g>
						</g>
					</g>
					<g>
						<defs>
							
								<ellipse id="SVGID_1_" transform="matrix(0.2272 -0.9738 0.9738 0.2272 -106.8549 397.7137)" cx="197.173" cy="266.186" rx="25.843" ry="25.843"/>
						</defs>
						<use xlink:href="#SVGID_1_"  style="overflow:visible;fill:#C1DDED;"/>
						<clipPath id="SVGID_00000034073378997634560030000002187112774072544648_">
							<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
						</clipPath>
						<g style="clip-path:url(#SVGID_00000034073378997634560030000002187112774072544648_);">
							<path style="fill:#FFFFFF;" d="M204.336,271.359c2.814-2.157,4.636-5.542,4.636-9.361c0-6.516-5.283-11.799-11.799-11.799
								c-6.517,0-11.799,5.283-11.799,11.799c0,3.819,1.822,7.204,4.636,9.361c-8.788,2.984-15.114,11.297-15.114,21.094h44.555
								C219.45,282.657,213.124,274.343,204.336,271.359z"/>
						</g>
					</g>
				</g>
			</g>
			<g>
				<g>
					<g>
						<rect x="165.762" y="314.07" style="fill:#FFFFFF;" width="168.496" height="66.13"/>
					</g>
				</g>
				<g>
					<g>
						<circle style="fill:#C7EAFC;" cx="197.173" cy="342.041" r="25.843"/>
					</g>
					<g>
						<g>
							<rect x="236.971" y="319.893" style="fill:#85BFF1;" width="92.384" height="9.014"/>
							<g>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="336.825" x2="332.302" y2="336.825"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="341.708" x2="332.302" y2="341.708"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="346.59" x2="332.302" y2="346.59"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="351.472" x2="332.302" y2="351.472"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="356.355" x2="332.302" y2="356.355"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="361.237" x2="332.302" y2="361.237"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="366.12" x2="332.302" y2="366.12"/>
								
									<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="371.002" x2="332.302" y2="371.002"/>
							</g>
						</g>
						<g>
							<g>
								<polygon style="fill:#FFC722;" points="182.612,370.563 183.47,372.299 185.387,372.578 184,373.93 184.327,375.839 
									182.612,374.938 180.898,375.839 181.225,373.93 179.838,372.578 181.755,372.299 								"/>
								<polygon style="fill:#FFC722;" points="189.893,370.563 190.75,372.299 192.667,372.578 191.28,373.93 191.607,375.839 
									189.893,374.938 188.178,375.839 188.505,373.93 187.118,372.578 189.035,372.299 								"/>
								<g>
									<polygon style="fill:#C1DDED;" points="197.173,370.563 198.03,372.299 199.947,372.578 198.56,373.93 198.887,375.839 
										197.173,374.938 195.458,375.839 195.786,373.93 194.398,372.578 196.315,372.299 									"/>
								</g>
								<g>
									<polygon style="fill:#C1DDED;" points="204.453,370.563 205.31,372.299 207.227,372.578 205.84,373.93 206.167,375.839 
										204.453,374.938 202.738,375.839 203.066,373.93 201.679,372.578 203.596,372.299 									"/>
								</g>
								<g>
									<polygon style="fill:#C1DDED;" points="211.733,370.563 212.59,372.299 214.507,372.578 213.12,373.93 213.448,375.839 
										211.733,374.938 210.018,375.839 210.346,373.93 208.959,372.578 210.876,372.299 									"/>
								</g>
							</g>
						</g>
					</g>
					<g>
						<defs>
							<circle id="SVGID_00000107568089058217953860000010053582872591946119_" cx="197.173" cy="342.041" r="25.843"/>
						</defs>
						<use xlink:href="#SVGID_00000107568089058217953860000010053582872591946119_"  style="overflow:visible;fill:#C1DDED;"/>
						<clipPath id="SVGID_00000052791370165271299630000012991580521336235910_">
							<use xlink:href="#SVGID_00000107568089058217953860000010053582872591946119_"  style="overflow:visible;"/>
						</clipPath>
						<g style="clip-path:url(#SVGID_00000052791370165271299630000012991580521336235910_);">
							<path style="fill:#FFFFFF;" d="M204.336,347.214c2.814-2.156,4.636-5.542,4.636-9.361c0-6.517-5.283-11.799-11.799-11.799
								c-6.517,0-11.799,5.283-11.799,11.799c0,3.819,1.822,7.204,4.636,9.361c-8.788,2.984-15.114,11.297-15.114,21.094h44.555
								C219.45,358.511,213.124,350.198,204.336,347.214z"/>
						</g>
					</g>
				</g>
			</g>
			<g>
				<g>
					<g>
						<g>
							<rect x="165.762" y="162.81" style="fill:#FFFFFF;" width="168.496" height="66.13"/>
						</g>
					</g>
					<g>
						<g>
							<circle style="fill:#C7EAFC;" cx="197.173" cy="190.781" r="25.843"/>
						</g>
						<g>
							<g>
								<rect x="236.971" y="168.633" style="fill:#FF795C;" width="92.384" height="9.014"/>
								<g>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="185.565" x2="332.302" y2="185.565"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="190.447" x2="332.302" y2="190.447"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="195.33" x2="332.302" y2="195.33"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="200.212" x2="332.302" y2="200.212"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="205.095" x2="332.302" y2="205.095"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="209.977" x2="332.302" y2="209.977"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="214.859" x2="332.302" y2="214.859"/>
									
										<line style="fill:none;stroke:#C1DDED;stroke-width:1.0759;stroke-miterlimit:10;" x1="234.025" y1="219.742" x2="332.302" y2="219.742"/>
								</g>
							</g>
							<g>
								<g>
									<polygon style="fill:#FFC722;" points="182.612,219.302 183.47,221.039 185.387,221.318 184,222.67 184.327,224.579 
										182.612,223.678 180.898,224.579 181.225,222.67 179.838,221.318 181.755,221.039 									"/>
									<polygon style="fill:#FFC722;" points="189.893,219.302 190.75,221.039 192.667,221.318 191.28,222.67 191.607,224.579 
										189.893,223.678 188.178,224.579 188.505,222.67 187.118,221.318 189.035,221.039 									"/>
									<polygon style="fill:#FFC722;" points="197.173,219.302 198.03,221.039 199.947,221.318 198.56,222.67 198.887,224.579 
										197.173,223.678 195.458,224.579 195.786,222.67 194.398,221.318 196.315,221.039 									"/>
									<g>
										<polygon style="fill:#C1DDED;" points="204.453,219.302 205.31,221.039 207.227,221.318 205.84,222.67 206.167,224.579 
											204.453,223.678 202.738,224.579 203.066,222.67 201.679,221.318 203.596,221.039 										"/>
									</g>
									<g>
										<polygon style="fill:#C1DDED;" points="211.733,219.302 212.59,221.039 214.507,221.318 213.12,222.67 213.448,224.579 
											211.733,223.678 210.018,224.579 210.346,222.67 208.959,221.318 210.876,221.039 										"/>
									</g>
								</g>
							</g>
						</g>
						<g>
							<defs>
								<circle id="SVGID_00000145047479046233323420000010571551824358943649_" cx="197.173" cy="190.781" r="25.843"/>
							</defs>
							<use xlink:href="#SVGID_00000145047479046233323420000010571551824358943649_"  style="overflow:visible;fill:#C1DDED;"/>
							<clipPath id="SVGID_00000108996931556592177640000018044344141116590978_">
								<use xlink:href="#SVGID_00000145047479046233323420000010571551824358943649_"  style="overflow:visible;"/>
							</clipPath>
							<g style="clip-path:url(#SVGID_00000108996931556592177640000018044344141116590978_);">
								<path style="fill:#FFFFFF;" d="M204.336,195.954c2.814-2.157,4.636-5.542,4.636-9.361c0-6.516-5.283-11.799-11.799-11.799
									c-6.517,0-11.799,5.283-11.799,11.799c0,3.819,1.822,7.204,4.636,9.361c-8.788,2.984-15.114,11.297-15.114,21.094h44.555
									C219.45,207.251,213.124,198.937,204.336,195.954z"/>
							</g>
						</g>
					</g>
				</g>
			</g>
		</g>
		<g>
			
				<ellipse transform="matrix(0.9255 -0.3788 0.3788 0.9255 -70.4603 141.4868)" style="fill:#5BB039;" cx="324.372" cy="249.825" rx="16.796" ry="16.796"/>
			
				<ellipse transform="matrix(0.9255 -0.3788 0.3788 0.9255 -70.4603 141.4868)" style="fill:#5BB039;" cx="324.372" cy="249.825" rx="16.796" ry="16.796"/>
			<circle style="fill:none;stroke:#FFFFFF;stroke-width:0.9851;stroke-miterlimit:10;" cx="324.372" cy="249.825" r="15.379"/>
			
				<polyline style="fill:none;stroke:#FFFFFF;stroke-width:1.9702;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" points="
				316.841,249.386 324.372,256.923 330.12,240.494 			"/>
		</g>
		<g>
			<g>
				<path style="fill:#F9AF88;" d="M348.902,272.335c-0.523-0.55-1.351-0.822-1.998-1.196c-0.706-0.407-1.402-0.869-1.955-1.474
					c-0.259-0.283-0.575-0.427-0.911-0.599c-0.243-0.124-0.486-0.247-0.733-0.364c-0.851-0.404-1.726-0.792-2.647-1.006l0.213-0.408
					c0.078-0.15,0.252-0.224,0.415-0.174c0.583,0.177,1.929,0.548,2.503,0.404c0.481-0.121,0.106-0.538-0.107-0.731
					c-0.328-0.298-0.701-0.571-1.029-0.879c-0.342-0.322-0.672-0.659-1.012-0.984l-1.066-1.015l0.096-0.171
					c0.196-0.348,0.653-0.445,0.974-0.209c0.023,0.017,0.04,0.03,0.048,0.035c0.422,0.289,0.838,0.589,1.233,0.914
					c0.38,0.312,0.727,0.668,1.144,0.931c0.143,0.091,0.634,0.44,0.807,0.315c0.106-0.077,0.065-0.287,0.032-0.394
					c-0.125-0.41-0.447-0.731-0.737-1.032c-0.881-0.915-1.766-1.828-2.649-2.741c0.323-0.416,0.905-0.444,1.245-0.082l1.837,1.954
					c0.253,0.269,0.506,0.539,0.759,0.808c0.186,0.198,0.408,0.508,0.699,0.536c0.659,0.064-0.227-1.483-0.346-1.725
					c-0.363-0.738-0.726-1.475-1.09-2.213c-0.01-0.02-0.19-0.372-0.185-0.375c0.423-0.264,0.979-0.139,1.247,0.281
					c0.583,0.911,1.15,1.837,1.65,2.793c0.333,0.637,0.655,1.318,1.126,1.867c0.233,0.272,0.53,0.678,0.902,0.757
					c0.056,0.012,0.117,0.014,0.168-0.011c0.053-0.026,0.089-0.078,0.121-0.128c0.413-0.637,0.703-1.339,1.007-2.031
					c0.158-0.359,0.579-0.691,0.947-0.807c0.488-0.155,0.78,0.34,0.613,0.761c-0.123,0.31-0.329,0.582-0.441,0.897
					c-0.129,0.366-0.339,1.237-0.306,1.607c0.063,0.729,0.22,1.34,0.022,2.077c-0.108,0.402-0.371,0.909-0.343,1.354
					L348.902,272.335z"/>
				<polygon style="fill:#F9AF88;" points="348.902,272.335 352.566,276.713 355.457,273.349 351.16,269.877 				"/>
			</g>
			<g>
				<path style="fill:#F9AF88;" d="M391.183,249.314l0.507,8.011l-8.649,7.104l-2.838-6.008c0,0,3.703-3.214,2.408-6.43
					L391.183,249.314z"/>
				<path style="fill:#F9AF88;" d="M394.43,240.132c0.027-2.608-0.588-5.211-1.907-7.472c-1.464-2.51-3.953-4.693-6.858-4.735
					c-2.887-0.042-5.429,2.043-6.957,4.493c-2.268,3.636-2.691,8.276-1.411,12.335c1.043,3.31,4.333,8.716,8.378,8.725
					C391.234,253.492,394.385,244.51,394.43,240.132z"/>
			</g>
			<path style="fill:#F9AF88;" d="M391.901,405.559c0.47-1.249,0.754-2.088,0.754-2.088l-0.073-7.027l-5.996,0.005
				c-0.131,2.726-0.174,5.135-0.462,7.85c-0.045,0.421-0.1,0.841-0.154,1.26H391.901z"/>
			<path style="fill:#F9AF88;" d="M362.123,404.665c0.268-0.737,0.423-1.194,0.423-1.194l-0.073-7.027l-5.996,0.005
				c-0.131,2.726-0.174,5.135-0.462,7.85c-0.013,0.122-0.028,0.244-0.041,0.366H362.123z"/>
			<path style="fill:#FF795C;" d="M382.896,257.79c-0.889-0.138-1.763-0.352-2.637-0.568c-1.357-0.335-4.224-0.474-7.274,2.87
				c-4.224,4.63-11.593,18.346-11.593,18.346l-10.232-8.561l-2.257,2.458c0,0,10.502,14.725,14.327,15.05
				c3.825,0.325,10.08-15.515,10.08-15.515s-3.087,4.386-1.787,7.473c1.3,3.087-1.868,23.8-1.868,23.8s17.004,4.224,29.243,0
				c0,0-6.367-10.965-5.566-21.024c0.801-10.059,3.984-11.985,3.984-11.985s10.517,10.394,10.247,12.973
				c-0.271,2.579-7.296,15.862-7.296,15.862l2.611,2.079c0,0,14.956-13.064,12.041-22.326c-2.915-9.262-12.338-21.767-18.403-21.85
				s-8.881,0.088-8.881,0.088l-3.574,5.677l-0.846-4.798L382.896,257.79z"/>
			<path style="fill:#232D68;" d="M396.9,299.087c0,0,9.267,15.103,3.86,29.724c-5.074,13.724-5.819,23.865-5.854,27.502
				s-2.251,44.875-2.251,44.875h-6.284c0,0-2.792-33.194-4.133-44.65c-1.34-11.456-2.018-30.316-2.018-30.316
				s-7.08,25.871-8.39,34.537c-1.31,8.666-9.285,40.115-9.285,40.115h-6.304c0,0,0.579-40.762,1.553-47.747
				c0.975-6.986,12.188-52.257,12.188-52.257L396.9,299.087z"/>
			<g>
				<path style="fill:#F9AF88;" d="M402.877,301.048c-0.512,0.56-0.725,1.406-1.052,2.077c-0.357,0.733-0.768,1.459-1.332,2.054
					c-0.264,0.278-0.385,0.603-0.534,0.951c-0.107,0.251-0.212,0.503-0.311,0.757c-0.343,0.877-0.669,1.777-0.817,2.711
					l-0.422-0.183c-0.155-0.068-0.241-0.236-0.203-0.401c0.135-0.595,0.411-1.962,0.227-2.525c-0.155-0.471-0.544-0.068-0.722,0.158
					c-0.274,0.348-0.52,0.74-0.805,1.088c-0.297,0.364-0.611,0.716-0.91,1.079l-0.938,1.135l-0.177-0.084
					c-0.36-0.171-0.49-0.62-0.277-0.957c0.016-0.024,0.027-0.042,0.032-0.051c0.259-0.442,0.529-0.877,0.825-1.295
					c0.284-0.401,0.615-0.772,0.849-1.207c0.08-0.149,0.394-0.663,0.257-0.827c-0.084-0.1-0.291-0.045-0.395-0.005
					c-0.401,0.153-0.698,0.497-0.978,0.807c-0.851,0.944-1.699,1.89-2.548,2.835c-0.438-0.293-0.506-0.872-0.169-1.236l1.82-1.97
					c0.251-0.271,0.502-0.543,0.753-0.814c0.184-0.2,0.478-0.443,0.486-0.735c0.017-0.661-1.464,0.331-1.697,0.467
					c-0.71,0.414-1.421,0.828-2.131,1.242c-0.019,0.011-0.357,0.216-0.361,0.211c-0.293-0.403-0.207-0.967,0.192-1.264
					c0.868-0.645,1.751-1.276,2.67-1.843c0.612-0.377,1.268-0.746,1.783-1.255c0.255-0.252,0.639-0.576,0.691-0.953
					c0.008-0.057,0.006-0.118-0.023-0.167c-0.03-0.052-0.084-0.083-0.137-0.112c-0.664-0.367-1.385-0.607-2.097-0.862
					c-0.369-0.132-0.73-0.529-0.872-0.888c-0.188-0.476,0.285-0.801,0.716-0.665c0.318,0.101,0.604,0.288,0.926,0.376
					c0.375,0.103,1.258,0.251,1.625,0.193c0.722-0.115,1.321-0.314,2.07-0.168c0.409,0.079,0.932,0.307,1.375,0.247L402.877,301.048
					z"/>
			</g>
			<path style="fill:#232D68;" d="M383.322,232.505c0,0,3.571,4.621,7.417,8.285c3.846,3.664-2.06,13.278-1.236,18.085
				c0,0,4.183,4.237,9.645-1.315c5.462-5.552-2.091-34.669-15.826-31.373c-11.837,2.841-9.356,27.145-8.444,33.806
				c0.129,0.94,0.994,1.588,1.933,1.454c1.717-0.245,4.387-0.663,6.237-1.125c2.93-0.732,0.655-6.824-2.844-12.572
				C376.704,242.002,378.789,234.016,383.322,232.505z"/>
			<path style="fill:#ED955F;" d="M382.777,252.558c0,0,4.012,2.469,8.424-2.955C391.201,249.603,388.687,257.16,382.777,252.558z"
				/>
			<path style="fill:#FF795C;" d="M356.137,403.204c0,0,1.336,1.498,3.28,1.539c1.944,0.041,2.997-1.337,3.402-1.012
				c0.405,0.324,0.81,4.09,0,4.495c-0.81,0.405-14.862,2.187-15.591,0.526c-0.729-1.66,4.09-2.592,5.629-3.604
				S356.137,403.204,356.137,403.204z"/>
			<path style="fill:#FF795C;" d="M386.284,403.587c0,0,1.336,1.498,3.28,1.539c1.944,0.041,2.997-1.336,3.402-1.012
				c0.405,0.324,0.81,4.09,0,4.495c-0.81,0.405-14.862,2.187-15.591,0.526c-0.729-1.66,4.09-2.592,5.629-3.604
				C384.543,404.518,386.284,403.587,386.284,403.587z"/>
		</g>
		<g>
			<g>
				<path style="fill:#F9AF88;" d="M162.314,267.509c1.016-0.556,0.324-1.3,0.319-1.306c0.172,0.184,0.461-0.563,0.447-0.685
					c-0.115-1.013-1.557-0.424-2.112-0.209c-0.184,0.071-1.925,0.998-2.007,0.926c0,0-0.326-0.283-0.898,0.03
					c-0.572,0.313-1.737,2.227-1.737,2.227l-0.135,0.462l1.344,2.043c0,0,1.005-0.082,1.8-0.016
					c0.795,0.066,0.883-0.306,2.076-0.458c1.193-0.152,0.813-1.495,0.813-1.495C163.102,268.241,162.314,267.509,162.314,267.509z"
					/>
			</g>
			<g>
				<g>
					<g>
						<path style="fill:#5BB039;" d="M180.138,248.899c-5.092-4.517-12.881-4.05-17.398,1.042
							c-4.517,5.092-4.05,12.881,1.042,17.398s12.881,4.05,17.398-1.042S185.23,253.415,180.138,248.899z M165.558,265.336
							c-3.986-3.536-4.351-9.634-0.816-13.62c3.536-3.986,9.634-4.351,13.62-0.816c3.986,3.536,4.352,9.634,0.816,13.62
							C175.642,268.507,169.544,268.872,165.558,265.336z"/>
						<g style="opacity:0.44;">
							<path style="fill:#FFFFFF;" d="M172.082,257.98c-2.351,2.651-5.733,3.49-7.554,1.875s-1.391-5.073,0.96-7.724
								c2.351-2.65,5.733-3.49,7.554-1.875C174.863,251.872,174.434,255.33,172.082,257.98z"/>
						</g>
					</g>
					<g>
						<path style="fill:#FFFFFF;" d="M175.394,247.605c-0.885-0.155-1.778-0.25-2.671-0.234c-0.892-0.021-1.778,0.075-2.65,0.219
							c-1.735,0.316-3.412,1.021-4.719,2.193c-1.32,1.144-2.324,2.637-2.979,4.285c-0.364,0.808-0.541,1.689-0.825,2.535
							l-0.498,2.643c0.07-0.894,0.167-1.792,0.258-2.693c0.239-0.87,0.377-1.777,0.706-2.625l0.528-1.263
							c0.163-0.428,0.468-0.785,0.696-1.182l0.37-0.58c0.135-0.185,0.303-0.345,0.454-0.519c0.326-0.324,0.583-0.721,0.967-0.983
							c1.403-1.202,3.173-1.946,4.98-2.19C171.819,246.974,173.667,247.092,175.394,247.605z"/>
					</g>
				</g>
				<path style="fill:#5BB039;" d="M143.394,290.395l-0.07-0.062c-0.867-0.769-0.946-2.094-0.177-2.961l19.68-22.187l3.208,2.846
					l-19.68,22.187C145.586,291.084,144.26,291.163,143.394,290.395z"/>
				<g>
					<path style="fill:#FFFFFF;" d="M162.501,272.183l-3.376-2.994c-0.353-0.313-0.386-0.854-0.072-1.207l0.702-0.791
						c0.313-0.353,0.854-0.386,1.207-0.072l3.376,2.994c0.353,0.313,0.386,0.854,0.072,1.207l-0.702,0.791
						C163.395,272.464,162.854,272.496,162.501,272.183z"/>
				</g>
				<g>
					<path style="fill:#FFFFFF;" d="M148.023,288.505l-3.376-2.994c-0.353-0.313-0.386-0.854-0.072-1.207l0.702-0.791
						c0.313-0.353,0.854-0.386,1.207-0.072l3.376,2.994c0.353,0.313,0.386,0.854,0.072,1.207l-0.702,0.791
						C148.917,288.786,148.376,288.818,148.023,288.505z"/>
				</g>
			</g>
			<g>
				<path style="fill:#F9AF88;" d="M115.404,247.691l-0.507,8.011l8.649,7.104l2.838-6.008c0,0-3.703-3.214-2.408-6.43
					L115.404,247.691z"/>
				<path style="fill:#F9AF88;" d="M112.156,238.51c-0.027-2.608,0.588-5.211,1.908-7.472c1.464-2.51,3.953-4.693,6.858-4.735
					c2.887-0.042,5.429,2.043,6.958,4.493c2.268,3.636,2.691,8.276,1.411,12.335c-1.043,3.31-4.334,8.716-8.378,8.725
					C115.353,251.869,112.202,242.887,112.156,238.51z"/>
			</g>
			<path style="fill:#FF795C;" d="M135.259,274.814c-0.445-2.375-1.982-4.567-1.982-4.567S134.069,272.25,135.259,274.814z"/>
			<path style="fill:#F9AF88;" d="M109.687,297.464c0,0-9.267,15.103-3.86,29.724c5.075,13.724,5.819,23.865,5.854,27.502
				c0.036,3.636,2.085,50.969,2.085,50.969l5.702-0.574c0,0,3.54-38.714,4.88-50.171c1.34-11.456,2.018-30.316,2.018-30.316
				s8.577,24.098,9.887,32.764c1.31,8.666,9.045,47.723,9.045,47.723c2.012,0.127,3.925,0.028,5.702-0.382
				c0,0-1.234-46.214-2.208-53.2c-0.975-6.986-12.188-52.257-12.188-52.257L109.687,297.464z"/>
			<path style="fill:#ED955F;" d="M123.81,250.935c0,0-4.012,2.469-8.424-2.955C115.386,247.98,117.9,255.537,123.81,250.935z"/>
			<path style="fill:#FF795C;" d="M155.427,268.254l-10.232,8.561c0,0-7.369-13.716-11.593-18.346
				c-3.051-3.344-5.917-3.205-7.274-2.87c-0.874,0.216-1.748,0.43-2.637,0.568l-0.317,0.049l-0.846,4.798l-3.574-5.677
				c0,0-2.556-0.155-8.029-0.098c-0.673,0.025-1.646,6.517-1.744,7.124c-0.271,1.667-0.952,3.916-0.084,5.512
				c0.692,1.272,1.958,2.042,2.647,3.371c2.202,4.243,1.623,10.71,1.029,15.286c-0.182,1.401-3.534,15.522-5.082,14.987
				c12.239,4.224,29.242,0,29.242,0s-3.168-20.714-1.868-23.8c0.381-0.905,0.379-1.921,0.195-2.906
				c-1.19-2.564-1.982-4.567-1.982-4.567s1.536,2.192,1.982,4.567c2.159,4.652,5.632,11.157,8.099,10.948
				c3.826-0.325,14.327-15.05,14.327-15.05L155.427,268.254z"/>
			<path style="fill:#E36C52;" d="M111.874,290.536c-1.154,3.654-2.756,7.15-4.833,10.371c-2.879,4.464-6.61,12.933-2.909,23.391
				c5.536,15.642,5.554,23.051,5.554,23.051h40.176l-14.164-54.966L111.874,290.536L111.874,290.536z"/>
			<g>
				<path style="fill:#F9AF88;" d="M147.873,286.449c1.246-0.033,0.95-1.086,0.948-1.094c0.074,0.261,0.718-0.312,0.765-0.436
					c0.388-1.027-1.283-1.172-1.921-1.239c-0.211-0.022-2.334,0.009-2.378-0.099c0,0-0.173-0.432-0.875-0.413
					c-0.702,0.019-2.759,1.279-2.759,1.279l-0.356,0.376l0.283,2.618c0,0,1.003,0.416,1.733,0.87
					c0.729,0.454,0.996,0.141,2.214,0.582c1.217,0.441,1.514-1.033,1.514-1.033C148.268,287.538,147.873,286.449,147.873,286.449z"
					/>
			</g>
			<path style="fill:#FF795C;" d="M110.914,255.294c-0.457-0.15-1.769,1.882-2.013,2.252c-1.939,2.939-1.229,6.821-0.501,10.043
				c1.205,5.334,3.426,10.59,6.424,15.163c0.466,0.71,0.956,1.405,1.478,2.076c5.961,7.665,25.212,2.789,25.212,2.789l-0.226-3.33
				c0,0-14.921-1.774-17.197-3.018c-2.001-1.094-4.228-12.477-4.737-15.213c-0.074-0.395-1.976-1.724-2.333-2.165
				c-0.734-0.907-1.35-1.904-1.955-2.899C113.928,259.121,112.734,256.613,110.914,255.294z"/>
			<path style="fill:#232D68;" d="M133.245,246.638c0.106-1.261,0.243-2.526,0.285-3.782c0.198-5.878-1.538-14.573-7.576-17.224
				c-9.957-4.371-18.34,8.067-18.825,16.512c-0.483,8.424,1.562,24.172,5.026,26.919c3.464,2.747,0.717-2.564,4.929-2.381
				c4.212,0.183,3.341-1.908,3.868-6.631c0.436-3.905-4.37-8.55-6.77-14.421c-0.472-2.728-1.505-10.094,0.931-10.914
				c2.863-0.965,3.821-4.407,5.259-6.662c1.103,5.134,3.082,9.739,7.522,12.491c1.311,0.812,1.72,2.533,0.952,3.87
				c-0.82,1.428-1.901,2.732-2.996,3.973c-1.042,1.181-2.179,2.495-2.175,4.071c0.004,1.63,1.219,2.958,2.184,4.271
				c1.117,1.52,1.977,3.228,2.531,5.031c0.378,1.229,0.654,2.571,1.565,3.479c1.197,1.193,3.129,1.254,4.773,0.861
				c1.69-0.404,3.475-1.455,3.75-3.171c0.226-1.412-0.645-2.743-1.475-3.908c-1.431-2.007-2.895-4.07-3.514-6.456
				C132.99,250.635,133.077,248.641,133.245,246.638z"/>
			
				<polyline style="fill:none;stroke:#E36C52;stroke-width:0.5379;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" points="
				118.733,270.247 124.092,281.271 133.277,283.206 			"/>
			
				<path style="fill:none;stroke:#E36C52;stroke-width:0.5379;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="
				M114.825,282.753c0,0,2.369,4.645,6.862,5.458"/>
			<path style="fill:#232D68;" d="M151.208,403.587c0,0-1.336,1.498-3.28,1.539c-1.944,0.041-2.997-1.336-3.402-1.012
				c-0.405,0.324-0.81,4.09,0,4.495c0.81,0.405,14.862,2.187,15.591,0.526c0.729-1.66-4.09-2.592-5.629-3.604
				C152.949,404.518,151.208,403.587,151.208,403.587z"/>
			<path style="fill:#232D68;" d="M119.931,403.587c0,0-1.336,1.498-3.28,1.539c-1.944,0.041-2.997-1.336-3.402-1.012
				c-0.405,0.324-0.81,4.09,0,4.495c0.81,0.405,14.862,2.187,15.591,0.526c0.729-1.66-4.09-2.592-5.629-3.604
				C121.673,404.518,119.931,403.587,119.931,403.587z"/>
		</g>
	</g>
</g>
<g id="OBJECTS">
</g>
</svg>
